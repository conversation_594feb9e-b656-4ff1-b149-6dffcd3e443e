import json
import math
import os
from typing import Union

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

plt.style.use(["ggplot", "seaborn-ticks"])


class LatentFactorsClustering():
    def __init__(self, data_location: str, user_col_name: str, entity: str, max_k_elbow_plot: int,
                 num_iterations: int, num_diff_inits: int, num_clusters: int, high_percentile: int, low_percentile: int):
        self.data_location = data_location
        self.user_col_name = user_col_name
        self.entity = entity
        self.max_k_elbow_plot = max_k_elbow_plot
        self.num_iterations = num_iterations if num_iterations else math.inf
        self.num_diff_inits = num_diff_inits
        self.num_clusters = num_clusters
        self.entity_and_entity_idx_df = None
        self.loss = None
        self.high_percentile = high_percentile
        self.low_percentile = low_percentile

    def read_data(self) -> pd.DataFrame:
        """
        Read the data and extract bias and latent factors.

        :param: None
        :return: Dataframe containing bias and latent factors
        """
        print("Reading data...")
        if self.entity == "user":
            sheet_name = "Users"
            entity_info_cols = [self.user_col_name, "user_idx"]
        else:
            sheet_name = "Items"
            entity_info_cols = ["item", "item_idx"]
        df = pd.read_excel(self.data_location, sheet_name=sheet_name)
        self.entity_and_entity_idx_df = df[entity_info_cols]
        df = df.drop(entity_info_cols, axis=1)
        print("Data read.")
        return df

    def k_means(self, df: pd.DataFrame, k: int) -> Union[np.ndarray, np.array, np.array]:
        """
        Run K-Means on the given Dataframe and calculate clusters' centroids, data points'
        assignments and loss per iteration.

        :param df: Dataframe containing bias and latent factors
        :param k: Number of clusters
        :return: Centroids of the clusters
        :return: Assignments of the data points
        :return: Loss per iteration
        """
        print("Executing K-Means...")
        df_values = df.values
        num_entities = df_values.shape[0]
        # centroid dimensions: num_clusters X emb_dimension.
        centroids = df_values[np.random.randint(0, num_entities, size=k)]
        prev_centroids = None
        it = 0
        loss = list()
        while prev_centroids is None or \
                (not (centroids == prev_centroids).all() and it < self.num_iterations):
            # dist dimensions: num_points X num_clusters.
            dist = np.zeros((num_entities, k))
            for c in range(k):
                # Calculate distance between each point and each centroid.
                dist[:, c] = np.sqrt(np.sum(np.power((df_values - centroids[c]), 2), axis=1))
            # Determine which cluster each point belongs to.
            # assignments dimensions: (num_points,).
            assignments = np.argmin(dist, axis=1)
            # Calculate the loss at each epoch as the summation of the distances between each point
            # and the cluster's centroid that it was assigned to.
            loss.append(np.sum(np.amin(dist, axis=1)))
            prev_centroids = centroids.copy()
            for c in range(k):
                # Update each cluster centroid's coordinates as the average of the points'
                # coordinates that belong to it.
                centroid_points = df_values[assignments == c]
                if centroid_points.size != 0:
                    centroids[c] = np.mean(centroid_points, axis=0)
            it += 1
        print("K-Means executed.")
        return centroids, assignments, np.array(loss)

    def save_hyperparameters(self, directory: str):
        """
        Save hyperparameters used for the execution.

        :param directory: Directory to save the hyperparameters.
        :return: None
        """
        hps_dict = {
            "data_location": self.data_location,
            "user_col_name": self.user_col_name,
            "entity": self.entity,
            "max_k_elbow_plot": self.max_k_elbow_plot,
            "num_iterations": self.num_iterations,
            "num_diff_inits": self.num_diff_inits,
            "num_clusters": self.num_clusters,
            "high_percentile": self.high_percentile,
            "low_percentile": self.low_percentile
        }
        hps_file_path = os.path.join(directory, "latent_factors_clustering_hps.json")
        with open(hps_file_path, mode='w') as json_file:
            json.dump(obj=hps_dict, fp=json_file, indent=2)

    def create_plot(self, values: np.array, title: str, y_label: str, x_label: str, x_ticks: list,
                    x_tick_labels: list, fname: str):
        """
        Create a plot according to the given parameters and save it.

        :param values: Values to plot
        :param title: Title of the plot
        :param y_label: Label for the Y-axis
        :param x_label: Label for the X-axis
        :param x_ticks: Number of ticks on the X-axis
        :param x_tick_labels: Labels for the X-axis' ticks
        :param fname: Destination path for the plot
        :return: None
        """
        print("Creating plot...")
        fig, ax = plt.subplots(figsize=(8, 5))
        ax.plot(values)
        plt.title(label=title, fontdict={"fontweight": "bold"})
        plt.ylabel(y_label)
        plt.xlabel(x_label)
        ax.set_xticks(x_ticks)
        ax.set_xticklabels(x_tick_labels)
        plt.savefig(fname=fname, dpi=500, format="png", bbox_inches="tight")
        print("Plot created.")
        plt.show()

    def save_elbow_plot(self, df: pd.DataFrame):
        """
        Generate the elbow plot, save it and return the directory containing it.

        :param df: Dataframe containing bias and latent factors
        :return: None
        """
        print("Saving elbow plot...")
        k_loss = list()
        for k in range(2, self.max_k_elbow_plot + 1):
            _, _, loss = self.k_means(df=df, k=k)
            k_loss.append(loss[-1])
            print(f"Done k = {k}", end='\r')
            print(end='\n')
        self.create_plot(values=k_loss, title="K-means loss for different values of K",
                         y_label="Loss", x_label='K',
                         x_ticks=list(range(self.max_k_elbow_plot - 1)),
                         x_tick_labels=list(range(2, self.max_k_elbow_plot + 1)),
                         fname=os.path.join(os.path.dirname(self.data_location), "elbow_plot.png"))
        print("Elbow plot saved.")

    def get_centroids_assignments_and_loss(self, df: pd.DataFrame, high_percentile: int=80, low_percentile: int=20) -> Union[np.ndarray, np.array, pd.DataFrame, pd.DataFrame]:
        """
        Run K-Means with different centroid initializations and get the results associated with the
        lowest loss.

        :param df: DataFrame containing bias and latent factors
        :param high_percentile: high percentile cutoff for avg. latent variable cluster value lable
        :param low_percentile: low percentile cutoff for avg. latent variable cluster value lable
        :return: Centroids of the clusters
        :return: Assignments of the data points
        :return: High and low percentile cutoff values by latent variable 
        :return: Cluster labels
        """
        high_percentile = int(high_percentile)
        low_percentile = int(low_percentile)
        if 0 <high_percentile<= 100 and 0<=low_percentile<100 and high_percentile>low_percentile:
            pass
        else:
            raise Exception("high_percentile and low_percentile must be integer values between 0 and 100; high must be strictly greater than low")

        print("Calculating centroids, assignments and loss...")
        best_centroids, best_assignments, best_loss, candidate_loss = None, None, None, math.inf
        for i in range(1, self.num_diff_inits + 1):
            centroids, assignments, loss = self.k_means(df=df, k=self.num_clusters)
            if loss[-1] < candidate_loss:
                best_centroids = centroids.copy()
                best_assignments = assignments.copy()
                best_loss = loss.copy()
                candidate_loss = loss[-1]
            print(f"Done i = {i}", end='\r')
            print(end='\n')
        self.loss = best_loss
        print("Centroids, assignments and loss calculated.")

        cluster_tags = np.array([[None] * len(best_centroids[0]) for i in range(len(best_centroids))])
        high_percentile_vals = []
        low_percentile_vals = []

        for latent_factor in range(len(best_centroids[0])):
            high = np.percentile(best_centroids[:,latent_factor], high_percentile)
            high_percentile_vals.append(high)
            low = np.percentile(best_centroids[:,latent_factor], low_percentile)
            low_percentile_vals.append(low)

            for cluster in range(len(best_centroids)):
                if best_centroids[cluster, latent_factor] >= high:
                    cluster_tags[cluster, latent_factor] = "High"
                elif best_centroids[cluster, latent_factor] < low:
                    cluster_tags[cluster, latent_factor] = "Low"
                else:
                    cluster_tags[cluster, latent_factor] = ""

        percentiles_df = pd.DataFrame({f'high_percentile_{high_percentile}': high_percentile_vals,
                                        f'low_percentile_{low_percentile}': low_percentile_vals})

        percentiles_df.index = [f"latent_factor_{lf - 1}" for lf in percentiles_df.index]
        percentiles_df = percentiles_df.rename(index={"latent_factor_-1": "bias"})
        percentiles_df = percentiles_df.T
        percentiles_df.index.name = 'Percentile'

        cluster_tags_df = pd.DataFrame(cluster_tags)
        cluster_tags_df.columns = percentiles_df.columns

        cluster_labels = []

        for cluster in range(len(cluster_tags_df)):
            cluster_label = ""
            for latent_factor in range(cluster_tags_df.shape[1]):
                if cluster_tags_df.columns[latent_factor] != "bias" and cluster_tags_df.iat[cluster, latent_factor] != "":
                    if len(cluster_label) == 0:
                        cluster_label = f'{cluster_tags_df.iat[cluster, latent_factor]} {cluster_tags_df.columns[latent_factor]}'
                    else:
                        cluster_label = cluster_label + f'; {cluster_tags_df.iat[cluster, latent_factor]} {cluster_tags_df.columns[latent_factor]}'

            cluster_labels.append(cluster_label)

        cluster_labels_df = pd.DataFrame(cluster_labels, columns = ['cluster_label'])
        print("Cluster labels generated.")

        return best_centroids, best_assignments, percentiles_df, cluster_labels_df

    def save_results(self, centroids: np.ndarray, assignments: np.array, percentiles: pd.DataFrame, cluster_labels: pd.DataFrame):
        """
        Prepare the results and save them into disk.

        :param centroids: Centroids of the clusters
        :param assignments: Assignments of the data points
        :param percentiles: High and low percentile cutoff values by latent variable
        :param cluster_labels: Cluster labels
        :return: None
        """
        print("Saving results...")
        self.save_hyperparameters(directory=os.path.dirname(self.data_location))
        if self.entity == "user":
            num_entities_col = "number_of_users"
            cluster_info_sheet_name = "Clusters (Users)"
            cluster_percentiles_sheet_name = "Cluster Percentiles (Users)"
            cluster_assignment_sheet_name = "Cluster Assignment (Users)"
        else:
            num_entities_col = "number_of_items"
            cluster_info_sheet_name = "Clusters (Items)"
            cluster_percentiles_sheet_name = "Cluster Percentiles (Items)"
            cluster_assignment_sheet_name = "Cluster Assignment (Items)"
        assignments_counts = np.unique(assignments, return_counts=True)
        assignments_strict_counts = list()
        for i in range(self.num_clusters):
            try:
                cluster_idx = np.where(assignments_counts[0] == i)[0][0]
                assignments_strict_counts.append(assignments_counts[1][cluster_idx])
            except:
                assignments_strict_counts.append(0)
        cluster_info_df = pd.DataFrame.from_dict({"cluster": list(range(self.num_clusters)),
                                                  num_entities_col: assignments_strict_counts})
        centroids_df = pd.DataFrame(centroids)
        centroids_df.columns = [f"latent_factor_{lf - 1}" for lf in centroids_df.columns]
        centroids_df = centroids_df.rename(columns={"latent_factor_-1": "bias"})
        
        cluster_info_df = pd.concat([cluster_info_df, centroids_df, cluster_labels], axis=1)

        self.entity_and_entity_idx_df["cluster"] = assignments
        with pd.ExcelWriter(self.data_location, engine="openpyxl", mode='a') as writer:
            sheet_names = writer.book.sheetnames
            # Remove clustering sheets if they already exist.
            if cluster_info_sheet_name in sheet_names:
                writer.book.remove(writer.book[cluster_info_sheet_name])
            if cluster_percentiles_sheet_name in sheet_names:
                writer.book.remove(writer.book[cluster_percentiles_sheet_name])
            if cluster_assignment_sheet_name in sheet_names:
                writer.book.remove(writer.book[cluster_assignment_sheet_name])

            cluster_info_df.to_excel(writer, index=False, sheet_name=cluster_info_sheet_name)
            percentiles.to_excel(writer, index=True, sheet_name=cluster_percentiles_sheet_name)
            self.entity_and_entity_idx_df.to_excel(writer, index=False,
                                                   sheet_name=cluster_assignment_sheet_name)
        loss_to_plot = self.loss if len(self.loss) < 300 else self.loss[:300]
        self.create_plot(values=loss_to_plot, title="Clustering loss", y_label="Loss",
                         x_label="Iteration", x_ticks=list(range(len(loss_to_plot))),
                         x_tick_labels=list(range(1, len(loss_to_plot) + 1)),
                         fname=os.path.join(os.path.dirname(self.data_location),
                                            "clustering_loss.png"))
        print(f"Results saved in {os.path.dirname(self.data_location)}/.")

    def run(self):
        """
        Run the Latent Factors Clustering pipeline.

        :param: None
        :return: None
        """
        df = self.read_data()
        self.save_elbow_plot(df=df)
        centroids, assignments, percentiles, cluster_labels = self.get_centroids_assignments_and_loss(df=df, high_percentile=self.high_percentile, low_percentile=self.low_percentile)
        self.save_results(centroids=centroids, assignments=assignments, percentiles=percentiles, cluster_labels=cluster_labels)


if __name__ == "__main__":
    data_location = "25082020195119/output.xlsx"
    user_col_name = "sys_RespNum"
    entity = "user"
    max_k_elbow_plot = 10
    num_iterations = 100
    num_diff_inits = 20
    num_clusters = 5
    latent_factors_clustering = LatentFactorsClustering(data_location=data_location,
                                                        user_col_name=user_col_name, entity=entity,
                                                        max_k_elbow_plot=max_k_elbow_plot,
                                                        num_iterations=num_iterations,
                                                        num_diff_inits=num_diff_inits,
                                                        num_clusters=num_clusters)
    latent_factors_clustering.run()
