"""
Math Agent for the Interactive Analytics module.

This module defines the Math Agent which is responsible for performing
mathematical calculations and solving equations using Python.
"""

import logging
import json
import re
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime

from pydantic import BaseModel, Field

from app.services.llm_service import LLMService

# Initialize logger
logger = logging.getLogger(__name__)


class MathAgentInput(BaseModel):
    """Input schema for the Math Agent."""
    query: str = Field(..., description="The mathematical query to solve")
    context: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="Additional context")
    history: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="Conversation history")


class MathAgentOutput(BaseModel):
    """Output schema for the Math Agent."""
    result: str = Field(..., description="The result of the calculation")
    code: str = Field("", description="The Python code used for the calculation")
    thinking: str = Field("", description="Agent's reasoning process")
    error: Optional[str] = Field(None, description="Error message if calculation failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class MathAgent:
    """
    Math Agent for the Interactive Analytics module.
    
    The Math Agent is responsible for:
    1. Parsing mathematical queries
    2. Translating them into Python code
    3. Executing the code to perform calculations
    4. Formatting and returning the results
    """
    
    def __init__(self, llm_service: LLMService, model_id: int):
        """Initialize the Math Agent.
        
        Args:
            llm_service: Service for LLM interactions
            model_id: ID of the LLM model to use
        """
        self.llm_service = llm_service
        self.model_id = model_id
    
    async def process(self, input_data: MathAgentInput) -> MathAgentOutput:
        """Process a mathematical query and return the result.
        
        Args:
            input_data: Input data for the agent
            
        Returns:
            Agent output with calculation result
        """
        logger.info(f"Processing mathematical query: '{input_data.query[:50]}...' if len(input_data.query) > 50 else input_data.query")
        thinking = f"Analyzing mathematical query: '{input_data.query}'\n\n"
        
        try:
            # 1. Parse the query and generate Python code
            code_generation = await self._generate_code(input_data.query)
            thinking += code_generation["thinking"]
            code = code_generation["code"]
            logger.debug(f"Generated Python code for query: {len(code)} characters")
            
            # 2. Execute the code
            thinking += "\nExecuting the generated Python code...\n"
            logger.debug("Executing generated Python code")
            result, error = self._execute_code(code)
            
            if error:
                thinking += f"\nError during execution: {error}\n"
                return MathAgentOutput(
                    result=f"I encountered an error while solving this: {error}",
                    code=code,
                    thinking=thinking,
                    error=error,
                    metadata={
                        "success": False,
                        "processed_at": datetime.utcnow().isoformat()
                    }
                )
            
            # 3. Format the result
            thinking += f"\nExecution successful. Raw result: {result}\n"
            logger.debug(f"Code execution successful, raw result: {str(result)[:100]}...")
            formatted_result = self._format_result(input_data.query, result, code)
            thinking += f"\nFormatted result: {formatted_result}\n"
            
            logger.info(f"Successfully processed mathematical query, result length: {len(formatted_result)}")
            return MathAgentOutput(
                result=formatted_result,
                code=code,
                thinking=thinking,
                metadata={
                    "success": True,
                    "processed_at": datetime.utcnow().isoformat()
                }
            )
            
        except Exception as e:
            error_msg = str(e)
            stack_trace = traceback.format_exc()
            logger.error(f"Error in Math Agent: {error_msg}\n{stack_trace}")
            
            return MathAgentOutput(
                result=f"I encountered an unexpected error: {error_msg}",
                thinking=thinking + f"\nUnexpected error: {error_msg}\n{stack_trace}",
                error=error_msg,
                metadata={
                    "success": False,
                    "processed_at": datetime.utcnow().isoformat()
                }
            )
    
    async def _generate_code(self, query: str) -> Dict[str, str]:
        """Generate Python code to solve the mathematical query.
        
        Args:
            query: The mathematical query
            
        Returns:
            Dictionary with generated code and thinking process
        """
        # In a real implementation, this would use the LLM service to generate code
        # For now, we'll use a simple approach for demonstration
        
        thinking = "Translating the mathematical query into executable Python code.\n"
        
        # Simple pattern matching for basic arithmetic operations
        if re.search(r'(\d+\s*[\+\-\*\/\^\%]\s*\d+)', query):
            thinking += "Detected basic arithmetic operations. Generating Python code to evaluate the expression."
            
            # Extract the expression
            expression = re.search(r'(\d+\s*[\+\-\*\/\^\%]\s*\d+)', query).group(1)
            
            # Replace ^ with ** for Python exponentiation
            expression = expression.replace('^', '**')
            
            code = f"""
import math
import numpy as np

def calculate():
    # Solving: {query}
    result = {expression}
    return result

# Run the calculation
result = calculate()
print(f"Result: {{result}}")
"""
        
        # Check for more complex calculations like square roots, etc.
        elif any(term in query.lower() for term in ["square root", "sqrt", "root", "log", "sin", "cos", "tan"]):
            thinking += "Detected advanced mathematical operations. Generating Python code using math functions."
            
            # This is a simplified approach - in a real implementation, 
            # we would use the LLM to parse the query more thoroughly
            
            code = f"""
import math
import numpy as np

def calculate():
    # Solving: {query}
    
    # This is a placeholder - in a real implementation,
    # this would be generated based on the specific query
    if "square root" in "{query.lower()}" or "sqrt" in "{query.lower()}":
        # Assuming we're looking for the square root of a number
        # Extracting the number with a simple regex
        import re
        match = re.search(r'\\d+', "{query}")
        if match:
            number = float(match.group(0))
            result = math.sqrt(number)
            return result
    
    # Fallback
    return "Unable to parse the specific calculation"

# Run the calculation
result = calculate()
print(f"Result: {{result}}")
"""
        
        # For statistical operations
        elif any(term in query.lower() for term in ["average", "mean", "median", "std", "standard deviation"]):
            thinking += "Detected statistical operations. Generating Python code using numpy statistics functions."
            
            code = f"""
import numpy as np

def calculate():
    # Solving: {query}
    
    # This is a placeholder - in a real implementation,
    # we would extract the actual data points from the query
    # For now, using sample data
    data = [1, 2, 3, 4, 5]  # Placeholder
    
    if "average" in "{query.lower()}" or "mean" in "{query.lower()}":
        result = np.mean(data)
        return f"The mean of {{data}} is {{result}}"
    elif "median" in "{query.lower()}":
        result = np.median(data)
        return f"The median of {{data}} is {{result}}"
    elif "std" in "{query.lower()}" or "standard deviation" in "{query.lower()}":
        result = np.std(data)
        return f"The standard deviation of {{data}} is {{result}}"
    
    # Fallback
    return "Unable to parse the specific calculation"

# Run the calculation
result = calculate()
print(f"Result: {{result}}")
"""
        
        # Default fallback
        else:
            thinking += "Could not determine specific operation. Using a generic approach for evaluation."
            
            code = f"""
def calculate():
    # Solving: {query}
    # This is a placeholder for a generic calculation
    return "This calculation requires more specific input. Please provide a clearer mathematical question."

# Run the calculation
result = calculate()
print(f"Result: {{result}}")
"""
        
        return {
            "code": code,
            "thinking": thinking
        }
    
    def _execute_code(self, code: str) -> tuple:
        """Execute the generated Python code safely.
        
        Args:
            code: The Python code to execute
            
        Returns:
            Tuple of (result, error)
        """
        # In a production environment, we would use a sandboxed execution environment
        # For now, we'll use a simple approach with restrictions
        
        result = None
        error = None
        
        try:
            # Create a restricted local environment
            local_env = {
                'math': __import__('math'),
                'np': __import__('numpy'),
                'print': print,
                'result': None
            }
            
            # Execute the code with restrictions
            exec(code, {'__builtins__': {}}, local_env)
            
            # Get the result
            result = local_env.get('result', 'No result returned')
            
        except Exception as e:
            error = str(e)
        
        return result, error
    
    def _format_result(self, query: str, result: Any, code: str) -> str:
        """Format the result into a user-friendly response.
        
        Args:
            query: The original query
            result: The raw calculation result
            code: The Python code used
            
        Returns:
            Formatted response
        """
        # In a real implementation, this would generate a more polished response
        # For now, we'll use a simple template-based approach
        
        if isinstance(result, (int, float)):
            if result == int(result):
                # Convert to integer if it's a whole number
                result = int(result)
            
            response = f"The result of the calculation is: {result}"
            
            # Add some explanation based on the type of calculation
            if "sqrt" in code or "square root" in code:
                response += "\n\nI calculated this by taking the square root of the given number."
            elif "mean" in code:
                response += "\n\nI calculated this by taking the mean (average) of the values."
            elif "median" in code:
                response += "\n\nI calculated this by finding the median value (middle value when sorted)."
            elif "std" in code:
                response += "\n\nI calculated this by finding the standard deviation, which measures the amount of variation in a set of values."
            elif "**" in code:
                response += "\n\nI calculated this by performing the exponentiation operation."
            elif any(op in code for op in ["+", "-", "*", "/"]):
                response += "\n\nI calculated this by performing the basic arithmetic operation."
            
        else:
            # For string results or other types
            response = str(result)
        
        return response
