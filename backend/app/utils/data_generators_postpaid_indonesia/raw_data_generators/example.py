"""
Example usage of the raw data generators.

This module demonstrates how to use the raw data generators to create synthetic customer data
with churn behavior and transform it into features for a churn model.
"""

import os
import pandas as pd
import numpy as np
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Fix import for direct script execution
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import directly from the current directory
from main_generator import generate_customer, generate_customers, generate_customers_parallel


def generate_example_customer(
    output_dir: str = "example_data",
    baseline_churn_rate: float = 0.008,
    min_date: str = None
) -> Dict[str, Any]:
    """
    Generate an example customer with all related data.
    
    Args:
        output_dir: Directory to save generated data
        baseline_churn_rate: Base monthly churn probability (default: 0.008 or 0.8%)
        min_date: Minimum date for all customers (earliest date for billing, plans, network data)
        
    Returns:
        Dictionary with customer data and related records
    """
    # Generate a customer with all related data
    customer_data = generate_customer(
        random_seed=42,  # Use a fixed seed for reproducibility
        output_dir=output_dir,
        baseline_churn_rate=baseline_churn_rate,
        min_date=min_date
    )
    
    return customer_data


def generate_example_customers(
    num_customers: int = 100,
    output_dir: str = "example_data",
    baseline_churn_rate: float = 0.008,
    min_date: str = None,
    use_ray: bool = False,
    num_cpus: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Generate multiple example customers with all related data.
    
    Args:
        num_customers: Number of customers to generate
        output_dir: Directory to save generated data
        baseline_churn_rate: Base monthly churn probability (default: 0.008 or 0.8%)
        min_date: Minimum date for all customers (earliest date for billing, plans, network data)
        use_ray: Whether to use Ray for parallel processing
        num_cpus: Number of CPUs to use with Ray (defaults to all available)
        
    Returns:
        List of dictionaries with customer data and related records
    """
    # Generate customers with all related data
    customers_data = generate_customers_parallel(
        num_customers=num_customers,
        random_seed=42,  # Use a fixed seed for reproducibility
        output_dir=output_dir,
        baseline_churn_rate=baseline_churn_rate,
        min_date=min_date,
        use_ray=use_ray,
        num_cpus=num_cpus
    )
    
    return customers_data


def transform_to_churn_features(customer_data: Dict[str, Any]) -> pd.DataFrame:
    """
    Transform raw customer data into features for a churn model.
    
    Args:
        customer_data: Dictionary with customer data and related records
        
    Returns:
        DataFrame with features for churn prediction
    """
    # Extract customer data
    customer = customer_data['customer']
    plans = customer_data['plans']
    billing = customer_data['billing']
    network = customer_data['network']
    
    # Create a dictionary to hold features
    features = {}
    
    # Add customer ID
    features['customer_id'] = customer['customer_id']
    
    # Add churn status
    features['churned'] = customer.get('churned', False)
    if customer.get('churn_date'):
        if isinstance(customer['churn_date'], str):
            features['churn_date'] = customer['churn_date']
        else:
            features['churn_date'] = customer['churn_date'].strftime('%Y-%m-%d %H:%M:%S')
    else:
        features['churn_date'] = None
    
    # Add demographic features
    features['age'] = customer['age']
    features['gender'] = customer['gender']
    features['income'] = customer['income']
    features['region'] = customer['region']
    features['city_type'] = customer['city_type']
    features['household_size'] = customer['household_size']
    features['dependents'] = customer['dependents']
    features['marital_status'] = customer['marital_status']

    # Add prepaid-specific customer features
    if 'topup_frequency' in customer:
        features['topup_frequency_daily'] = 1 if customer['topup_frequency'] == 'Daily' else 0
        features['topup_frequency_weekly'] = 1 if customer['topup_frequency'] == 'Weekly' else 0
        features['topup_frequency_monthly'] = 1 if customer['topup_frequency'] == 'Monthly' else 0
        features['topup_frequency_irregular'] = 1 if customer['topup_frequency'] == 'Not Recurring' else 0

    if 'topup_amount_avg' in customer:
        features['topup_amount_avg'] = customer['topup_amount_avg']

    if 'total_topup_3m' in customer:
        features['total_topup_3m'] = customer['total_topup_3m']

    if 'data_usage_last_30d' in customer:
        features['data_usage_last_30d'] = customer['data_usage_last_30d']

    if 'call_minutes_last_30d' in customer:
        features['call_minutes_last_30d'] = customer['call_minutes_last_30d']

    if 'days_since_last_activity' in customer:
        features['days_since_last_activity'] = customer['days_since_last_activity']

    if 'device_type' in customer:
        features['device_smartphone'] = 1 if customer['device_type'] == 'Smartphone' else 0
        features['device_basic'] = 1 if customer['device_type'] == 'Basic Phone' else 0
    features['education_level'] = customer['education_level']
    features['occupation'] = customer['occupation']
    features['employment_duration'] = customer['employment_duration']
    features['housing_type'] = customer['housing_type']
    features['ownership_status'] = customer['ownership_status']
    features['years_at_address'] = customer['years_at_address']
    
    # Calculate tenure in months
    created_date = customer['created_date']
    if isinstance(created_date, str):
        created_date = datetime.strptime(created_date, '%Y-%m-%d %H:%M:%S')
    
    # Use churn date if customer has churned, otherwise use current date
    if customer.get('churned', False) and customer.get('churn_date'):
        end_date = customer['churn_date']
        if isinstance(end_date, str):
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
    else:
        end_date = datetime.now()
    
    features['tenure_months'] = (end_date - created_date).days // 30
    
    # Add plan features
    current_plan = None
    for plan in plans:
        if plan.get('is_current', False) or 'end_date' not in plan:
            current_plan = plan
            break
    
    if current_plan:
        features['plan_category'] = current_plan['plan_category']
        features['plan_tier'] = current_plan['plan_tier']
        features['base_price'] = current_plan['base_price']
        features['num_lines'] = current_plan.get('num_lines', 1)
        features['has_promotion'] = 'promotion' in current_plan
        
        # Count add-ons by category
        add_ons = current_plan.get('add_ons', [])
        features['num_add_ons'] = len(add_ons)
        features['num_svod_add_ons'] = sum(1 for a in add_ons if a['category'] == 'SVOD')
        features['num_security_add_ons'] = sum(1 for a in add_ons if a['category'] == 'Security')
        features['num_insurance_add_ons'] = sum(1 for a in add_ons if a['category'] == 'Insurance')
        
        # Calculate add-on costs
        features['add_on_cost'] = sum(a['price'] for a in add_ons)
    else:
        # Default values if no current plan
        features['plan_category'] = 'Unknown'
        features['plan_tier'] = 'Unknown'
        features['base_price'] = 0
        features['num_lines'] = 1
        features['has_promotion'] = False
        features['num_add_ons'] = 0
        features['num_svod_add_ons'] = 0
        features['num_security_add_ons'] = 0
        features['num_insurance_add_ons'] = 0
        features['add_on_cost'] = 0
    
    # Add prepaid transaction features
    if billing:
        # Sort transaction records by date
        sorted_billing = sorted(billing, key=lambda x: x['transaction_date'])

        # Get recent transaction records (last 6 months)
        recent_billing = sorted_billing[-6:] if len(sorted_billing) >= 6 else sorted_billing

        # Calculate prepaid transaction features
        features['avg_monthly_topup'] = np.mean([b['total_amount'] for b in recent_billing])
        features['max_monthly_topup'] = np.max([b['total_amount'] for b in recent_billing])
        features['min_monthly_topup'] = np.min([b['total_amount'] for b in recent_billing])
        features['topup_variability'] = np.std([b['total_amount'] for b in recent_billing])
        
        # Calculate payment channel diversity (prepaid metric)
        # For prepaid, we track payment channel diversity instead of payment issues
        unique_channels = len(set(b['payment_channel'] for b in recent_billing))
        features['payment_channel_diversity'] = unique_channels
        
        # Calculate price increases
        bill_amounts = [b['total_amount'] for b in sorted_billing]
        price_increases = sum(1 for i in range(1, len(bill_amounts)) if bill_amounts[i] > bill_amounts[i-1] * 1.05)
        features['price_increases'] = price_increases
        
        # Check for recent price increase
        if len(sorted_billing) >= 2:
            last_bill = sorted_billing[-1]['total_amount']
            previous_bill = sorted_billing[-2]['total_amount']
            features['recent_price_increase'] = last_bill > previous_bill * 1.05
        else:
            features['recent_price_increase'] = False
    else:
        # Default values if no transaction records
        features['avg_monthly_topup'] = 0
        features['max_monthly_topup'] = 0
        features['min_monthly_topup'] = 0
        features['topup_variability'] = 0
        features['payment_channel_diversity'] = 0
        features['price_increases'] = 0
        features['recent_price_increase'] = False
    
    # Add network features
    if network:
        # Sort network records by date
        sorted_network = sorted(network, key=lambda x: x['month_start_date'])
        
        # Get recent network records (last 6 months)
        recent_network = sorted_network[-6:] if len(sorted_network) >= 6 else sorted_network
        
        # Calculate network features
        features['avg_data_usage'] = np.mean([n['data_usage_gb'] for n in recent_network])
        features['max_data_usage'] = np.max([n['data_usage_gb'] for n in recent_network])
        features['avg_call_minutes'] = np.mean([n['call_minutes'] for n in recent_network])
        features['avg_texts'] = np.mean([n['text_messages'] for n in recent_network])
        features['total_dropped_calls'] = sum(n['dropped_calls'] for n in recent_network)
        features['avg_dropped_calls'] = np.mean([n['dropped_calls'] for n in recent_network])
        features['avg_speed_mbps'] = np.mean([n['avg_speed_mbps'] for n in recent_network])
        
        # Calculate data limit exceeded
        features['data_limit_exceeded'] = any(n.get('data_limit_exceeded', False) for n in recent_network)
        
        # Calculate usage trend (increasing or decreasing)
        if len(sorted_network) >= 3:
            recent_usage = [n['data_usage_gb'] for n in sorted_network[-3:]]
            features['usage_trend'] = (recent_usage[2] - recent_usage[0]) / recent_usage[0] if recent_usage[0] > 0 else 0
        else:
            features['usage_trend'] = 0
    else:
        # Default values if no network records
        features['avg_data_usage'] = 0
        features['max_data_usage'] = 0
        features['avg_call_minutes'] = 0
        features['avg_texts'] = 0
        features['total_dropped_calls'] = 0
        features['avg_dropped_calls'] = 0
        features['avg_speed_mbps'] = 0
        features['data_limit_exceeded'] = False
        features['usage_trend'] = 0
    
    # Create DataFrame from features
    df = pd.DataFrame([features])
    
    return df


def analyze_churn_distribution(customers_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze the distribution of churned vs non-churned customers.
    
    Args:
        customers_data: List of dictionaries with customer data and related records
        
    Returns:
        Dictionary with churn analysis results
    """
    total_customers = len(customers_data)
    churned_customers = sum(1 for data in customers_data if data['customer'].get('churned', False))
    churn_rate = churned_customers / total_customers if total_customers > 0 else 0
    
    # Analyze churn by demographic factors
    churn_by_age = {}
    churn_by_income = {}
    churn_by_city_type = {}
    
    for data in customers_data:
        customer = data['customer']
        churned = customer.get('churned', False)
        
        # Age groups
        age = customer['age']
        age_group = None
        if age < 25:
            age_group = "18-24"
        elif age < 35:
            age_group = "25-34"
        elif age < 45:
            age_group = "35-44"
        elif age < 55:
            age_group = "45-54"
        elif age < 65:
            age_group = "55-64"
        else:
            age_group = "65+"
        
        if age_group not in churn_by_age:
            churn_by_age[age_group] = {'total': 0, 'churned': 0}
        churn_by_age[age_group]['total'] += 1
        if churned:
            churn_by_age[age_group]['churned'] += 1
        
        # Income groups
        income = customer['income']
        income_group = None
        if income < 30000:
            income_group = "<$30k"
        elif income < 50000:
            income_group = "$30k-$50k"
        elif income < 75000:
            income_group = "$50k-$75k"
        elif income < 100000:
            income_group = "$75k-$100k"
        elif income < 150000:
            income_group = "$100k-$150k"
        else:
            income_group = ">$150k"
        
        if income_group not in churn_by_income:
            churn_by_income[income_group] = {'total': 0, 'churned': 0}
        churn_by_income[income_group]['total'] += 1
        if churned:
            churn_by_income[income_group]['churned'] += 1
        
        # City type
        city_type = customer['city_type']
        if city_type not in churn_by_city_type:
            churn_by_city_type[city_type] = {'total': 0, 'churned': 0}
        churn_by_city_type[city_type]['total'] += 1
        if churned:
            churn_by_city_type[city_type]['churned'] += 1
    
    # Calculate churn rates
    for group_data in churn_by_age.values():
        group_data['churn_rate'] = group_data['churned'] / group_data['total'] if group_data['total'] > 0 else 0
    
    for group_data in churn_by_income.values():
        group_data['churn_rate'] = group_data['churned'] / group_data['total'] if group_data['total'] > 0 else 0
    
    for group_data in churn_by_city_type.values():
        group_data['churn_rate'] = group_data['churned'] / group_data['total'] if group_data['total'] > 0 else 0
    
    return {
        'total_customers': total_customers,
        'churned_customers': churned_customers,
        'churn_rate': churn_rate,
        'churn_by_age': churn_by_age,
        'churn_by_income': churn_by_income,
        'churn_by_city_type': churn_by_city_type
    }


def main():
    """
    Generate example customers with churn behavior and analyze the results.
    """
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Generate synthetic customer data with churn behavior.')
    parser.add_argument('--num-customers', type=int, default=100, help='Number of customers to generate')
    parser.add_argument('--baseline-churn-rate', type=float, default=0.008, help='Base monthly churn probability')
    parser.add_argument('--output-dir', type=str, default='example_data', help='Directory to save generated data')
    parser.add_argument('--min-date', type=str, help='Minimum date for customer data (YYYY-MM-DD)')
    parser.add_argument('--use-ray', action='store_true', help='Use Ray for parallel processing')
    parser.add_argument('--num-cpus', type=int, help='Number of CPUs to use with Ray')
    args = parser.parse_args()
    
    # Set parameters
    num_customers = args.num_customers
    baseline_churn_rate = args.baseline_churn_rate
    output_dir = args.output_dir
    
    # Set minimum date (default to 6 months ago if not specified)
    min_date = args.min_date
    if min_date is None:
        min_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')  # 6 months ago
    
    # Generate multiple customers
    print(f"Generating {num_customers} customers with baseline churn rate of {baseline_churn_rate}...")
    if args.use_ray:
        print(f"Using Ray for parallel processing{f' with {args.num_cpus} CPUs' if args.num_cpus else ''}")
    
    customers_data = generate_example_customers(
        num_customers=num_customers,
        output_dir=output_dir,
        baseline_churn_rate=baseline_churn_rate,
        min_date=min_date,
        use_ray=args.use_ray,
        num_cpus=args.num_cpus
    )
    
    # Analyze churn distribution
    print("\nAnalyzing churn distribution...")
    churn_analysis = analyze_churn_distribution(customers_data)
    
    # Print churn analysis results
    print(f"\nChurn Analysis Results:")
    print(f"Total Customers: {churn_analysis['total_customers']}")
    print(f"Churned Customers: {churn_analysis['churned_customers']}")
    print(f"Overall Churn Rate: {churn_analysis['churn_rate']:.2%}")
    
    print("\nChurn by Age Group:")
    for age_group, data in sorted(churn_analysis['churn_by_age'].items()):
        print(f"  {age_group}: {data['churn_rate']:.2%} ({data['churned']}/{data['total']})")
    
    print("\nChurn by Income Group:")
    for income_group, data in sorted(churn_analysis['churn_by_income'].items()):
        print(f"  {income_group}: {data['churn_rate']:.2%} ({data['churned']}/{data['total']})")
    
    print("\nChurn by City Type:")
    for city_type, data in sorted(churn_analysis['churn_by_city_type'].items()):
        print(f"  {city_type}: {data['churn_rate']:.2%} ({data['churned']}/{data['total']})")
    
    # Generate a single customer for feature transformation example
    print("\nGenerating a single example customer for feature transformation...")
    customer_data = generate_example_customer(
        output_dir=output_dir,
        baseline_churn_rate=baseline_churn_rate,
        min_date=min_date
    )
    
    # Transform to churn features
    print("Transforming to churn features...")
    features_df = transform_to_churn_features(customer_data)
    
    # Print features
    print("\nChurn Model Features:")
    print(features_df.T)  # Transpose for better display
    
    # Save features to CSV
    os.makedirs(output_dir, exist_ok=True)
    features_file = os.path.join(output_dir, "churn_features.csv")
    features_df.to_csv(features_file, index=False)
    print(f"\nFeatures saved to: {os.path.abspath(features_file)}")
    
    # Save all customers' features
    all_features = []
    for data in customers_data:
        features = transform_to_churn_features(data)
        all_features.append(features)
    
    all_features_df = pd.concat(all_features, ignore_index=True)
    all_features_file = os.path.join(output_dir, "all_churn_features.csv")
    all_features_df.to_csv(all_features_file, index=False)
    print(f"All customers' features saved to: {os.path.abspath(all_features_file)}")


if __name__ == "__main__":
    main()
