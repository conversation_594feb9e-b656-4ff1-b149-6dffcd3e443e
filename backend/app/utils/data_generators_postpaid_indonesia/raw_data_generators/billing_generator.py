"""
Billing generator for creating synthetic prepaid transaction data.

This module provides utilities to generate realistic prepaid transaction data
including top-ups, SIM fees, and activation bonuses.
"""

import uuid
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime, timedelta
import logging
import random

# Set up logging
logger = logging.getLogger(__name__)

# Import country configurations from customer_generator
from customer_generator import COUNTRY_CONFIGS

# Define prepaid packages
PREPAID_PACKAGES = {
    "PKG-BASIC": {
        "package_name": "Basic Prepaid",
        "description": "Basic prepaid package with essential features",
        "typical_amount": 25.0
    },
    "PKG-STANDARD": {
        "package_name": "Standard Prepaid",
        "description": "Standard prepaid package with more data and minutes",
        "typical_amount": 40.0
    },
    "PKG-PREMIUM": {
        "package_name": "Premium Prepaid",
        "description": "Premium prepaid package with unlimited features",
        "typical_amount": 60.0
    },
    "PKG-UNLIMITED": {
        "package_name": "Unlimited Prepaid",
        "description": "Unlimited prepaid package with all features",
        "typical_amount": 80.0
    },
    "PKG-FAMILY": {
        "package_name": "Family Prepaid",
        "description": "Family prepaid package for multiple lines",
        "typical_amount": 100.0
    }
}

class BillingGenerator:
    """Generate synthetic prepaid transaction data."""

    def __init__(
        self,
        random_seed: Optional[int] = None,
        current_date: Optional[str] = None,
        country: str = "US"
    ):
        """
        Initialize the prepaid transaction generator.

        Args:
            random_seed: Seed for reproducibility
            current_date: Reference date for date calculations, format YYYY-MM-DD
            country: Country code for localization ("US" or "ID")
        """
        # Set country configuration
        if country not in COUNTRY_CONFIGS:
            raise ValueError(f"Unsupported country: {country}. Supported countries: {list(COUNTRY_CONFIGS.keys())}")
        self.country = country
        self.config = COUNTRY_CONFIGS[country]

        # Set the current date or default to today
        if current_date:
            self.current_date = datetime.strptime(current_date, '%Y-%m-%d')
        else:
            self.current_date = datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0
            )

        # Set random seed for reproducibility if provided
        if random_seed is not None:
            np.random.seed(random_seed)
            random.seed(random_seed)
    
    def generate_customer_billing(
        self,
        customer_id: str,
        plans: List[Dict[str, Any]],
        customer_data: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate prepaid transaction records for a customer based on their plans and behavior.

        Args:
            customer_id: Unique identifier for the customer
            plans: List of plan dictionaries for the customer
            customer_data: Customer data including topup_amount_avg and topup_frequency

        Returns:
            List of dictionaries with prepaid transaction data
        """
        transaction_records = []

        # Process each plan to generate transactions
        for plan in plans:
            # Generate prepaid transactions for this plan
            plan_transactions = self._generate_plan_transactions(customer_id, plan, customer_data)
            transaction_records.extend(plan_transactions)

        # Sort transaction records by date
        transaction_records.sort(key=lambda x: x['transaction_date'])

        return transaction_records
    
    def _generate_plan_transactions(
        self,
        customer_id: str,
        plan: Dict[str, Any],
        customer_data: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate prepaid transaction records for a specific plan.

        Args:
            customer_id: Unique identifier for the customer
            plan: Plan dictionary
            customer_data: Customer data including topup_amount_avg and topup_frequency

        Returns:
            List of dictionaries with prepaid transaction data for the plan
        """
        transaction_records = []
        
        # Get plan start and end dates
        start_date = datetime.strptime(plan['start_date'], '%Y-%m-%d')

        if 'end_date' in plan:
            end_date = datetime.strptime(plan['end_date'], '%Y-%m-%d')
        else:
            end_date = self.current_date

        # Calculate number of months for prepaid transactions
        num_months = (end_date.year - start_date.year) * 12 + end_date.month - start_date.month

        # Ensure at least one transaction record
        num_months = max(1, num_months)

        # Select a random package for this customer
        package_id = random.choice(list(PREPAID_PACKAGES.keys()))
        package_info = PREPAID_PACKAGES[package_id]

        # Get promotion details
        promotion = plan.get('promotion')
        promotion_discount = plan.get('promotion_discount', 0)
        promotion_end_date = None
        if 'promotion_end_date' in plan:
            promotion_end_date = datetime.strptime(plan['promotion_end_date'], '%Y-%m-%d')

        # Get add-ons
        add_ons = plan.get('add_ons', [])
        
        # Get customer's actual top-up behavior if available
        if customer_data and 'topup_amount_avg' in customer_data:
            customer_topup_avg = customer_data['topup_amount_avg']
            customer_frequency = customer_data.get('topup_frequency', 'Monthly')
        else:
            # Fallback to package-based amounts
            customer_topup_avg = package_info['typical_amount']
            customer_frequency = 'Monthly'

        # Generate transactions based on customer's actual frequency
        if customer_frequency == "Daily":
            transactions_per_month = 25  # ~25 working days
        elif customer_frequency == "Weekly":
            transactions_per_month = 4
        elif customer_frequency == "Monthly":
            transactions_per_month = 1
        else:  # Not Recurring
            transactions_per_month = random.uniform(0.5, 2)  # 0.5-2 per month

        total_transactions = max(1, int(transactions_per_month * num_months))

        # Calculate the total days in the period
        days_range = (end_date - start_date).days
        if days_range <= 0:
            days_range = 30  # Default to 30 days if same start/end date

        for transaction_num in range(total_transactions):
            # Calculate transaction date based on frequency pattern
            if customer_frequency == "Daily":
                # Spread daily transactions across working days
                days_offset = transaction_num * (days_range // max(1, total_transactions))
            elif customer_frequency == "Weekly":
                # Weekly transactions every ~7 days
                days_offset = transaction_num * 7 + random.randint(0, 3)
            elif customer_frequency == "Monthly":
                # Monthly transactions spread across months
                days_offset = transaction_num * 30 + random.randint(0, 10)
            else:  # Not Recurring
                # Random distribution
                days_offset = random.randint(0, days_range)

            transaction_date = start_date + timedelta(days=min(days_offset, days_range))

            # Skip if transaction date is after current date
            if transaction_date > self.current_date:
                continue

            # Use customer's actual average top-up amount with small variation
            # Add only small variation (±10%) to maintain consistency
            variation = random.uniform(0.9, 1.1)
            topup_amount = round(customer_topup_avg * variation, 2)
            
            # Apply promotion discount if applicable
            discount_amount = 0
            discount_reason = None

            if promotion and (not promotion_end_date or transaction_date <= promotion_end_date):
                discount_amount = round(promotion_discount * (topup_amount / customer_topup_avg), 2)
                discount_reason = promotion

            # Add charges for add-ons
            add_on_charges = 0
            for add_on in add_ons:
                add_on_start = datetime.strptime(add_on['start_date'], '%Y-%m-%d')

                # Check if add-on is active for this transaction period
                if add_on_start <= transaction_date:
                    if 'end_date' not in add_on or datetime.strptime(add_on['end_date'], '%Y-%m-%d') >= transaction_date:
                        add_on_charges += add_on['price']

            # Add SIM fee occasionally (replaces one_time_charges)
            sim_fee = 0
            activation_type = None

            if random.random() < 0.15:  # 15% chance of SIM fee/activation
                activation_options = [
                    {"amount": 10, "type": "SIM Activation"},
                    {"amount": 25, "type": "Welcome Bonus"},
                    {"amount": 15, "type": "Referral Credit"},
                    {"amount": 5, "type": "SIM Activation"},
                    {"amount": 20, "type": "Welcome Bonus"}
                ]
                activation = random.choice(activation_options)
                sim_fee = activation["amount"]
                activation_type = activation["type"]
            
            # Calculate taxes (approximately 8-12% of topup amount)
            tax_rate = random.uniform(0.08, 0.12)
            taxes = (topup_amount + add_on_charges + sim_fee - discount_amount) * tax_rate

            # Calculate total amount
            total_amount = topup_amount + add_on_charges + sim_fee + taxes - discount_amount

            # Round amounts to 2 decimal places
            discount_amount = round(discount_amount, 2)
            add_on_charges = round(add_on_charges, 2)
            sim_fee = round(sim_fee, 2)
            taxes = round(taxes, 2)
            total_amount = round(total_amount, 2)

            # Create transaction record
            transaction_record = {
                'transaction_id': f"TXN-{uuid.uuid4().hex[:8].upper()}",
                'customer_id': customer_id,
                'package_id': package_id,
                'transaction_date': transaction_date.strftime('%Y-%m-%d'),
                'topup_amount': topup_amount,
                'add_on_charges': add_on_charges,
                'taxes': taxes,
                'total_amount': total_amount,
                'payment_channel': self._generate_payment_channel()
            }

            # Add discount if applicable
            if discount_amount > 0:
                transaction_record['discount_amount'] = discount_amount
                transaction_record['discount_reason'] = discount_reason

            # Add SIM fee if applicable
            if sim_fee > 0:
                transaction_record['sim_fee'] = sim_fee
                transaction_record['activation_type'] = activation_type

            transaction_records.append(transaction_record)

        return transaction_records
    
    def _generate_payment_channel(self) -> str:
        """
        Generate a payment channel for a prepaid transaction.

        Returns:
            Payment channel string
        """
        # Use country-specific channels
        channel_options = self.config["topup_channels"]
        channel_probs = self.config["channel_probs"]
        # Normalize probabilities to ensure they sum to exactly 1.0
        channel_probs = [p / sum(channel_probs) for p in channel_probs]

        return np.random.choice(channel_options, p=channel_probs)
