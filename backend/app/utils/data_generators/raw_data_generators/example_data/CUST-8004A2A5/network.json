[{"network_id": "NET-9D3A38F5", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2022-08-01", "data_usage_gb": 3.56, "call_minutes": 161, "text_messages": 459, "dropped_calls": 0, "avg_speed_mbps": 58.4, "num_lines": 1}, {"network_id": "NET-A69E49D3", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2022-08-31", "data_usage_gb": 4.94, "call_minutes": 191, "text_messages": 336, "dropped_calls": 4, "avg_speed_mbps": 42.1, "num_lines": 1}, {"network_id": "NET-97D8199B", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2022-09-30", "data_usage_gb": 4.6, "call_minutes": 177, "text_messages": 362, "dropped_calls": 2, "avg_speed_mbps": 42.9, "num_lines": 1}, {"network_id": "NET-4FF67BE3", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2022-10-30", "data_usage_gb": 8.45, "call_minutes": 166, "text_messages": 371, "dropped_calls": 1, "avg_speed_mbps": 55.3, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.45, "data_overage_charges": 51.75}, {"network_id": "NET-6E14136B", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2022-11-29", "data_usage_gb": 4.92, "call_minutes": 193, "text_messages": 360, "dropped_calls": 3, "avg_speed_mbps": 48.5, "num_lines": 1}, {"network_id": "NET-97BF02BB", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2022-12-29", "data_usage_gb": 7.35, "call_minutes": 173, "text_messages": 399, "dropped_calls": 3, "avg_speed_mbps": 51.6, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 2.35, "data_overage_charges": 35.25}, {"network_id": "NET-E1ADFECE", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-01-28", "data_usage_gb": 4.58, "call_minutes": 200, "text_messages": 386, "dropped_calls": 1, "avg_speed_mbps": 51.3, "num_lines": 1}, {"network_id": "NET-FCFB5920", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-02-27", "data_usage_gb": 4.35, "call_minutes": 149, "text_messages": 476, "dropped_calls": 1, "avg_speed_mbps": 59.0, "num_lines": 1}, {"network_id": "NET-627C2AC7", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-03-29", "data_usage_gb": 4.06, "call_minutes": 183, "text_messages": 400, "dropped_calls": 1, "avg_speed_mbps": 55.6, "num_lines": 1}, {"network_id": "NET-E2ACD51A", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-04-28", "data_usage_gb": 4.28, "call_minutes": 167, "text_messages": 331, "dropped_calls": 4, "avg_speed_mbps": 54.2, "num_lines": 1}, {"network_id": "NET-90C07161", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-05-28", "data_usage_gb": 9.98, "call_minutes": 212, "text_messages": 474, "dropped_calls": 1, "avg_speed_mbps": 58.3, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 4.98, "data_overage_charges": 74.7}, {"network_id": "NET-04EE82E6", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-06-27", "data_usage_gb": 8.38, "call_minutes": 179, "text_messages": 341, "dropped_calls": 7, "avg_speed_mbps": 41.9, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.38, "data_overage_charges": 50.7}, {"network_id": "NET-62F6E353", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-07-27", "data_usage_gb": 7.33, "call_minutes": 175, "text_messages": 445, "dropped_calls": 0, "avg_speed_mbps": 40.8, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 2.33, "data_overage_charges": 34.95}, {"network_id": "NET-596B0E8C", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-08-26", "data_usage_gb": 4.27, "call_minutes": 169, "text_messages": 369, "dropped_calls": 0, "avg_speed_mbps": 59.8, "num_lines": 1}, {"network_id": "NET-C1D94BBC", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-09-25", "data_usage_gb": 9.32, "call_minutes": 204, "text_messages": 331, "dropped_calls": 1, "avg_speed_mbps": 50.6, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 4.32, "data_overage_charges": 64.8}, {"network_id": "NET-DDD1A0D3", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-10-25", "data_usage_gb": 3.84, "call_minutes": 150, "text_messages": 357, "dropped_calls": 2, "avg_speed_mbps": 44.8, "num_lines": 1}, {"network_id": "NET-891BED3B", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-11-24", "data_usage_gb": 3.91, "call_minutes": 173, "text_messages": 380, "dropped_calls": 2, "avg_speed_mbps": 58.8, "num_lines": 1}, {"network_id": "NET-7EAB4E5B", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2023-12-24", "data_usage_gb": 4.32, "call_minutes": 174, "text_messages": 352, "dropped_calls": 1, "avg_speed_mbps": 55.6, "num_lines": 1}, {"network_id": "NET-31B1AF81", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-01-23", "data_usage_gb": 3.53, "call_minutes": 182, "text_messages": 473, "dropped_calls": 11, "avg_speed_mbps": 48.7, "num_lines": 1}, {"network_id": "NET-97E0AE3C", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-02-22", "data_usage_gb": 7.88, "call_minutes": 175, "text_messages": 443, "dropped_calls": 4, "avg_speed_mbps": 50.8, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 2.88, "data_overage_charges": 43.2}, {"network_id": "NET-D36355DE", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-03-23", "data_usage_gb": 9.48, "call_minutes": 183, "text_messages": 421, "dropped_calls": 4, "avg_speed_mbps": 44.1, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 4.48, "data_overage_charges": 67.2}, {"network_id": "NET-DFB4E0DE", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-04-22", "data_usage_gb": 4.51, "call_minutes": 176, "text_messages": 341, "dropped_calls": 1, "avg_speed_mbps": 47.3, "num_lines": 1}, {"network_id": "NET-DBE81282", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-05-22", "data_usage_gb": 3.6, "call_minutes": 196, "text_messages": 329, "dropped_calls": 5, "avg_speed_mbps": 41.5, "num_lines": 1}, {"network_id": "NET-F0A69A6B", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-06-21", "data_usage_gb": 3.58, "call_minutes": 144, "text_messages": 455, "dropped_calls": 3, "avg_speed_mbps": 47.8, "num_lines": 1}, {"network_id": "NET-2639FFCF", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-07-21", "data_usage_gb": 4.01, "call_minutes": 146, "text_messages": 436, "dropped_calls": 2, "avg_speed_mbps": 59.8, "num_lines": 1}, {"network_id": "NET-AB154027", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-08-20", "data_usage_gb": 10.53, "call_minutes": 173, "text_messages": 478, "dropped_calls": 2, "avg_speed_mbps": 51.5, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.53, "data_overage_charges": 82.95}, {"network_id": "NET-9168F194", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-09-19", "data_usage_gb": 8.8, "call_minutes": 150, "text_messages": 393, "dropped_calls": 0, "avg_speed_mbps": 41.5, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.8, "data_overage_charges": 57.0}, {"network_id": "NET-67CEFDE2", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-10-19", "data_usage_gb": 3.88, "call_minutes": 194, "text_messages": 450, "dropped_calls": 3, "avg_speed_mbps": 51.3, "num_lines": 1}, {"network_id": "NET-09CA9109", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-11-18", "data_usage_gb": 4.24, "call_minutes": 145, "text_messages": 425, "dropped_calls": 3, "avg_speed_mbps": 41.8, "num_lines": 1}, {"network_id": "NET-00E6F1E3", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2024-12-18", "data_usage_gb": 3.59, "call_minutes": 148, "text_messages": 432, "dropped_calls": 2, "avg_speed_mbps": 24.9, "num_lines": 1}, {"network_id": "NET-3491D26E", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2025-01-17", "data_usage_gb": 4.92, "call_minutes": 156, "text_messages": 367, "dropped_calls": 2, "avg_speed_mbps": 44.0, "num_lines": 1}, {"network_id": "NET-4AAF4042", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2025-02-16", "data_usage_gb": 3.62, "call_minutes": 175, "text_messages": 395, "dropped_calls": 5, "avg_speed_mbps": 47.2, "num_lines": 1}, {"network_id": "NET-24953E62", "customer_id": "CUST-8004A2A5", "plan_id": "PLAN-20C9A50D", "month_start_date": "2025-03-18", "data_usage_gb": 7.91, "call_minutes": 169, "text_messages": 328, "dropped_calls": 0, "avg_speed_mbps": 47.1, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 2.91, "data_overage_charges": 43.65}]