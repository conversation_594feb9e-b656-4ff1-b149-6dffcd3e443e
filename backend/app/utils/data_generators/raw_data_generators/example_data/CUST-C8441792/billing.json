[{"billing_id": "BILL-67909C6C", "customer_id": "CUST-C8441792", "plan_id": "PLAN-AD90A558", "billing_date": "2024-11-01", "base_amount": 110, "add_on_charges": 22.97, "taxes": 10.31, "total_amount": 121.28, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-6E0A249D", "customer_id": "CUST-C8441792", "plan_id": "PLAN-AD90A558", "billing_date": "2024-12-01", "base_amount": 110, "add_on_charges": 22.97, "taxes": 9.82, "total_amount": 120.79, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-4984D617", "customer_id": "CUST-C8441792", "plan_id": "PLAN-AD90A558", "billing_date": "2024-12-31", "base_amount": 110, "add_on_charges": 22.97, "taxes": 11.77, "total_amount": 137.74, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer", "one_time_charges": 15, "one_time_description": "Device upgrade fee"}, {"billing_id": "BILL-0E25A0D5", "customer_id": "CUST-C8441792", "plan_id": "PLAN-AD90A558", "billing_date": "2025-01-30", "base_amount": 110, "add_on_charges": 22.97, "taxes": 9.5, "total_amount": 120.47, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-F9F9616F", "customer_id": "CUST-C8441792", "plan_id": "PLAN-AD90A558", "billing_date": "2025-03-01", "base_amount": 110, "add_on_charges": 22.97, "taxes": 11.97, "total_amount": 122.94, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-4BD20EA9", "customer_id": "CUST-C8441792", "plan_id": "PLAN-AD90A558", "billing_date": "2025-03-31", "base_amount": 110, "add_on_charges": 22.97, "taxes": 10.82, "total_amount": 121.79, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}]