[{"billing_id": "BILL-CC7E19A2", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-01-01", "base_amount": 130, "add_on_charges": 15.98, "taxes": 11.88, "total_amount": 131.86, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A78939D5", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-01-31", "base_amount": 130, "add_on_charges": 15.98, "taxes": 14.27, "total_amount": 134.25, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-CD56EB8A", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-03-02", "base_amount": 130, "add_on_charges": 15.98, "taxes": 11.39, "total_amount": 131.37, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5FFCEB8C", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-04-01", "base_amount": 130, "add_on_charges": 15.98, "taxes": 13.36, "total_amount": 133.34, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-0C38BB63", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-05-01", "base_amount": 130, "add_on_charges": 15.98, "taxes": 10.7, "total_amount": 130.68, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-F2A26A7D", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-05-31", "base_amount": 130, "add_on_charges": 15.98, "taxes": 12.13, "total_amount": 132.11, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A0DEECEC", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-06-30", "base_amount": 130, "add_on_charges": 15.98, "taxes": 12.43, "total_amount": 132.41, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-BE6F07E9", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-07-30", "base_amount": 130, "add_on_charges": 15.98, "taxes": 11.83, "total_amount": 157.81, "payment_status": "Paid"}, {"billing_id": "BILL-4F452E05", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-08-29", "base_amount": 130, "add_on_charges": 15.98, "taxes": 14.08, "total_amount": 160.06, "payment_status": "Paid"}, {"billing_id": "BILL-C450B78D", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-09-28", "base_amount": 130, "add_on_charges": 15.98, "taxes": 15.84, "total_amount": 161.82, "payment_status": "Paid"}, {"billing_id": "BILL-34DBC4A3", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-10-28", "base_amount": 130, "add_on_charges": 15.98, "taxes": 13.38, "total_amount": 159.36, "payment_status": "Paid"}, {"billing_id": "BILL-C4203987", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-11-27", "base_amount": 130, "add_on_charges": 15.98, "taxes": 14.65, "total_amount": 160.63, "payment_status": "Paid"}, {"billing_id": "BILL-F13AEDD0", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-B1415EC9", "billing_date": "2022-12-27", "base_amount": 130, "add_on_charges": 15.98, "taxes": 17.38, "total_amount": 163.36, "payment_status": "Paid"}, {"billing_id": "BILL-F3258FE3", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-01-01", "base_amount": 40, "add_on_charges": 3.99, "taxes": 6.84, "total_amount": 66.83, "payment_status": "Paid", "discount_amount": 4.0, "discount_reason": "Upgrade", "one_time_charges": 20, "one_time_description": "International usage"}, {"billing_id": "BILL-24B75841", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-01-31", "base_amount": 40, "add_on_charges": 3.99, "taxes": 4.19, "total_amount": 44.18, "payment_status": "Paid", "discount_amount": 4.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-50A43389", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-03-02", "base_amount": 40, "add_on_charges": 3.99, "taxes": 4.76, "total_amount": 44.75, "payment_status": "Paid", "discount_amount": 4.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-F611B01E", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-04-01", "base_amount": 40, "add_on_charges": 3.99, "taxes": 4.27, "total_amount": 44.26, "payment_status": "Paid", "discount_amount": 4.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-4E709723", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-05-01", "base_amount": 40, "add_on_charges": 3.99, "taxes": 3.8, "total_amount": 47.79, "payment_status": "Unpaid"}, {"billing_id": "BILL-FC4C76E6", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-05-31", "base_amount": 40, "add_on_charges": 3.99, "taxes": 4.58, "total_amount": 48.57, "payment_status": "Paid"}, {"billing_id": "BILL-FD5DC462", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-06-30", "base_amount": 40, "add_on_charges": 3.99, "taxes": 7.05, "total_amount": 71.04, "payment_status": "Paid", "one_time_charges": 20, "one_time_description": "International usage"}, {"billing_id": "BILL-406F51FB", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-07-30", "base_amount": 40, "add_on_charges": 3.99, "taxes": 3.75, "total_amount": 47.74, "payment_status": "Paid"}, {"billing_id": "BILL-37AE5B51", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-08-29", "base_amount": 40, "add_on_charges": 3.99, "taxes": 4.22, "total_amount": 48.21, "payment_status": "Paid"}, {"billing_id": "BILL-B7318CB0", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-A079B6A0", "billing_date": "2023-09-28", "base_amount": 40, "add_on_charges": 3.99, "taxes": 5.12, "total_amount": 49.11, "payment_status": "Paid"}, {"billing_id": "BILL-F69A9F54", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2023-10-01", "base_amount": 70, "add_on_charges": 0, "taxes": 5.18, "total_amount": 68.18, "payment_status": "Paid", "discount_amount": 7.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-AD2B8D2E", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2023-10-31", "base_amount": 70, "add_on_charges": 0, "taxes": 6.53, "total_amount": 69.53, "payment_status": "Paid", "discount_amount": 7.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-58BDD01C", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2023-11-30", "base_amount": 70, "add_on_charges": 0, "taxes": 7.32, "total_amount": 70.32, "payment_status": "Paid", "discount_amount": 7.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-01B4FA0F", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2023-12-30", "base_amount": 70, "add_on_charges": 0, "taxes": 7.17, "total_amount": 70.17, "payment_status": "Paid", "discount_amount": 7.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-1D844C51", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-01-29", "base_amount": 70, "add_on_charges": 0, "taxes": 8.03, "total_amount": 78.03, "payment_status": "Paid"}, {"billing_id": "BILL-E55A309E", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-02-28", "base_amount": 70, "add_on_charges": 0, "taxes": 6.49, "total_amount": 76.49, "payment_status": "Paid"}, {"billing_id": "BILL-8CF55C83", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-03-29", "base_amount": 70, "add_on_charges": 0, "taxes": 7.15, "total_amount": 77.15, "payment_status": "Paid"}, {"billing_id": "BILL-ED9A6FCB", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-04-28", "base_amount": 70, "add_on_charges": 0, "taxes": 7.46, "total_amount": 77.46, "payment_status": "Paid"}, {"billing_id": "BILL-3CA976A8", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-05-28", "base_amount": 70, "add_on_charges": 0, "taxes": 8.03, "total_amount": 78.03, "payment_status": "Paid"}, {"billing_id": "BILL-6788B581", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-06-27", "base_amount": 70, "add_on_charges": 0, "taxes": 6.98, "total_amount": 76.98, "payment_status": "Paid"}, {"billing_id": "BILL-237ECEB4", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-07-27", "base_amount": 70, "add_on_charges": 0, "taxes": 7.44, "total_amount": 77.44, "payment_status": "Paid"}, {"billing_id": "BILL-D7FDE105", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-08-26", "base_amount": 70, "add_on_charges": 0, "taxes": 8.3, "total_amount": 78.3, "payment_status": "Paid"}, {"billing_id": "BILL-43C300E4", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-09-25", "base_amount": 70, "add_on_charges": 0, "taxes": 6.07, "total_amount": 76.07, "payment_status": "Paid"}, {"billing_id": "BILL-7DAB0AA6", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-10-25", "base_amount": 70, "add_on_charges": 0, "taxes": 6.99, "total_amount": 76.99, "payment_status": "Paid"}, {"billing_id": "BILL-894A1EDB", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-11-24", "base_amount": 70, "add_on_charges": 0, "taxes": 7.35, "total_amount": 77.35, "payment_status": "Paid"}, {"billing_id": "BILL-53081A50", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2024-12-24", "base_amount": 70, "add_on_charges": 0, "taxes": 8.37, "total_amount": 78.37, "payment_status": "Paid"}, {"billing_id": "BILL-CE433E6F", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2025-01-23", "base_amount": 70, "add_on_charges": 0, "taxes": 8.12, "total_amount": 78.12, "payment_status": "Paid"}, {"billing_id": "BILL-0AD13503", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2025-02-22", "base_amount": 70, "add_on_charges": 0, "taxes": 6.32, "total_amount": 76.32, "payment_status": "Paid"}, {"billing_id": "BILL-135AD863", "customer_id": "CUST-57C2335C", "plan_id": "PLAN-8BEC6E73", "billing_date": "2025-03-24", "base_amount": 70, "add_on_charges": 0, "taxes": 5.66, "total_amount": 75.66, "payment_status": "Paid"}]