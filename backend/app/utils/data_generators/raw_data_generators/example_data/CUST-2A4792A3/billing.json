[{"billing_id": "BILL-7B3D1A2E", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-04-01", "base_amount": 60, "add_on_charges": 0, "taxes": 5.31, "total_amount": 53.31, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-1A776202", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-05-01", "base_amount": 60, "add_on_charges": 0, "taxes": 5.43, "total_amount": 53.43, "payment_status": "Late", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-DF27A143", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-05-31", "base_amount": 60, "add_on_charges": 0, "taxes": 5.72, "total_amount": 53.72, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-98963FCE", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-06-30", "base_amount": 60, "add_on_charges": 0, "taxes": 5.36, "total_amount": 63.36, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-8D36CC89", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-07-30", "base_amount": 60, "add_on_charges": 0, "taxes": 5.07, "total_amount": 53.07, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-95FEEF76", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-08-29", "base_amount": 60, "add_on_charges": 0, "taxes": 3.94, "total_amount": 51.94, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-90192B91", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-09-28", "base_amount": 60, "add_on_charges": 0, "taxes": 5.48, "total_amount": 53.48, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-1D3B00A6", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-10-28", "base_amount": 60, "add_on_charges": 0, "taxes": 6.78, "total_amount": 66.78, "payment_status": "Paid"}, {"billing_id": "BILL-3971E7C7", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-11-27", "base_amount": 60, "add_on_charges": 0, "taxes": 6.6, "total_amount": 66.6, "payment_status": "Paid"}, {"billing_id": "BILL-3A88196A", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2024-12-27", "base_amount": 60, "add_on_charges": 0, "taxes": 5.24, "total_amount": 65.24, "payment_status": "Paid"}, {"billing_id": "BILL-EC397C77", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2025-01-26", "base_amount": 60, "add_on_charges": 0, "taxes": 6.08, "total_amount": 66.08, "payment_status": "Paid"}, {"billing_id": "BILL-E2E98D07", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2025-02-25", "base_amount": 60, "add_on_charges": 0, "taxes": 5.33, "total_amount": 65.33, "payment_status": "Paid"}, {"billing_id": "BILL-51FA127F", "customer_id": "CUST-2A4792A3", "plan_id": "PLAN-85C11F58", "billing_date": "2025-03-27", "base_amount": 60, "add_on_charges": 0, "taxes": 4.99, "total_amount": 64.99, "payment_status": "Paid"}]