[{"network_id": "NET-F0D50787", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-02-01", "data_usage_gb": 3.51, "call_minutes": 101, "text_messages": 934, "dropped_calls": 1, "avg_speed_mbps": 41.9, "num_lines": 1}, {"network_id": "NET-7F2AC26E", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-03-03", "data_usage_gb": 4.71, "call_minutes": 131, "text_messages": 1140, "dropped_calls": 4, "avg_speed_mbps": 59.3, "num_lines": 1}, {"network_id": "NET-B1E2E482", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-04-02", "data_usage_gb": 4.53, "call_minutes": 137, "text_messages": 1074, "dropped_calls": 1, "avg_speed_mbps": 41.3, "num_lines": 1}, {"network_id": "NET-53811C81", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-05-02", "data_usage_gb": 4.21, "call_minutes": 141, "text_messages": 1000, "dropped_calls": 3, "avg_speed_mbps": 49.6, "num_lines": 1}, {"network_id": "NET-62A28C07", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-06-01", "data_usage_gb": 4.74, "call_minutes": 141, "text_messages": 1166, "dropped_calls": 1, "avg_speed_mbps": 48.2, "num_lines": 1}, {"network_id": "NET-320CA777", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-07-01", "data_usage_gb": 3.8, "call_minutes": 119, "text_messages": 1135, "dropped_calls": 2, "avg_speed_mbps": 57.7, "num_lines": 1}, {"network_id": "NET-9809E830", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-07-31", "data_usage_gb": 4.81, "call_minutes": 108, "text_messages": 933, "dropped_calls": 0, "avg_speed_mbps": 54.8, "num_lines": 1}, {"network_id": "NET-2F7D4F4B", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-08-30", "data_usage_gb": 3.9, "call_minutes": 117, "text_messages": 831, "dropped_calls": 2, "avg_speed_mbps": 52.6, "num_lines": 1}, {"network_id": "NET-D4B4B92E", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-09-29", "data_usage_gb": 3.88, "call_minutes": 111, "text_messages": 814, "dropped_calls": 1, "avg_speed_mbps": 45.1, "num_lines": 1}, {"network_id": "NET-8A11DCDF", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-10-29", "data_usage_gb": 4.4, "call_minutes": 96, "text_messages": 1089, "dropped_calls": 1, "avg_speed_mbps": 55.0, "num_lines": 1}, {"network_id": "NET-35FA04AD", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-11-28", "data_usage_gb": 4.44, "call_minutes": 111, "text_messages": 810, "dropped_calls": 3, "avg_speed_mbps": 56.3, "num_lines": 1}, {"network_id": "NET-A213D684", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2023-12-28", "data_usage_gb": 4.95, "call_minutes": 121, "text_messages": 949, "dropped_calls": 1, "avg_speed_mbps": 44.0, "num_lines": 1}, {"network_id": "NET-83F30719", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2024-01-27", "data_usage_gb": 3.78, "call_minutes": 125, "text_messages": 899, "dropped_calls": 2, "avg_speed_mbps": 46.1, "num_lines": 1}, {"network_id": "NET-F4188E06", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-FFBBFAB1", "month_start_date": "2024-02-26", "data_usage_gb": 9.81, "call_minutes": 134, "text_messages": 811, "dropped_calls": 0, "avg_speed_mbps": 44.2, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 4.81, "data_overage_charges": 72.15}, {"network_id": "NET-9A1FDAE3", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-03-01", "data_usage_gb": 3.5, "call_minutes": 138, "text_messages": 887, "dropped_calls": 1, "avg_speed_mbps": 81.2, "num_lines": 1}, {"network_id": "NET-1A1710FD", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-03-31", "data_usage_gb": 4.61, "call_minutes": 102, "text_messages": 944, "dropped_calls": 0, "avg_speed_mbps": 76.0, "num_lines": 1}, {"network_id": "NET-A8AEBAE9", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-04-30", "data_usage_gb": 3.94, "call_minutes": 114, "text_messages": 912, "dropped_calls": 1, "avg_speed_mbps": 80.7, "num_lines": 1}, {"network_id": "NET-8502297F", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-05-30", "data_usage_gb": 4.15, "call_minutes": 97, "text_messages": 887, "dropped_calls": 1, "avg_speed_mbps": 64.1, "num_lines": 1}, {"network_id": "NET-60489883", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-06-29", "data_usage_gb": 4.21, "call_minutes": 101, "text_messages": 1037, "dropped_calls": 1, "avg_speed_mbps": 81.3, "num_lines": 1}, {"network_id": "NET-0556F7BE", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-07-29", "data_usage_gb": 10.96, "call_minutes": 98, "text_messages": 835, "dropped_calls": 2, "avg_speed_mbps": 55.4, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.96, "data_overage_charges": 89.4}, {"network_id": "NET-76DB7F5D", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-08-28", "data_usage_gb": 10.13, "call_minutes": 111, "text_messages": 956, "dropped_calls": 0, "avg_speed_mbps": 67.9, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.13, "data_overage_charges": 76.95}, {"network_id": "NET-32F5416E", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-09-27", "data_usage_gb": 3.8, "call_minutes": 105, "text_messages": 894, "dropped_calls": 3, "avg_speed_mbps": 72.8, "num_lines": 1}, {"network_id": "NET-28363610", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-10-27", "data_usage_gb": 4.79, "call_minutes": 113, "text_messages": 1082, "dropped_calls": 1, "avg_speed_mbps": 77.7, "num_lines": 1}, {"network_id": "NET-61611F1A", "customer_id": "CUST-249E4DB2", "plan_id": "PLAN-91852D10", "month_start_date": "2024-11-26", "data_usage_gb": 9.71, "call_minutes": 112, "text_messages": 1102, "dropped_calls": 0, "avg_speed_mbps": 47.7, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 4.71, "data_overage_charges": 70.65}]