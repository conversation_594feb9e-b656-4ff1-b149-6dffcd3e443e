[{"network_id": "NET-8E3A9626", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-04-01", "data_usage_gb": 4.05, "call_minutes": 332, "text_messages": 454, "dropped_calls": 4, "avg_speed_mbps": 93.3, "num_lines": 1}, {"network_id": "NET-B22D4F4B", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-05-01", "data_usage_gb": 3.74, "call_minutes": 358, "text_messages": 465, "dropped_calls": 1, "avg_speed_mbps": 114.0, "num_lines": 1}, {"network_id": "NET-CF1CC4BB", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-05-31", "data_usage_gb": 8.9, "call_minutes": 337, "text_messages": 421, "dropped_calls": 1, "avg_speed_mbps": 104.7, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.9, "data_overage_charges": 58.5}, {"network_id": "NET-B8DA3F7D", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-06-30", "data_usage_gb": 4.1, "call_minutes": 327, "text_messages": 420, "dropped_calls": 4, "avg_speed_mbps": 83.8, "num_lines": 1}, {"network_id": "NET-5B9D38C4", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-07-30", "data_usage_gb": 4.48, "call_minutes": 318, "text_messages": 490, "dropped_calls": 5, "avg_speed_mbps": 86.0, "num_lines": 1}, {"network_id": "NET-F7CFE35E", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-08-29", "data_usage_gb": 8.25, "call_minutes": 246, "text_messages": 577, "dropped_calls": 1, "avg_speed_mbps": 106.5, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.25, "data_overage_charges": 48.75}, {"network_id": "NET-A9CE7001", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-09-28", "data_usage_gb": 10.68, "call_minutes": 334, "text_messages": 546, "dropped_calls": 0, "avg_speed_mbps": 97.1, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.68, "data_overage_charges": 85.2}, {"network_id": "NET-5558F74D", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-10-28", "data_usage_gb": 10.15, "call_minutes": 270, "text_messages": 475, "dropped_calls": 4, "avg_speed_mbps": 114.2, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.15, "data_overage_charges": 77.25}, {"network_id": "NET-C77BA296", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-11-27", "data_usage_gb": 10.25, "call_minutes": 306, "text_messages": 593, "dropped_calls": 1, "avg_speed_mbps": 117.1, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.25, "data_overage_charges": 78.75}, {"network_id": "NET-DC27566B", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2024-12-27", "data_usage_gb": 3.97, "call_minutes": 315, "text_messages": 412, "dropped_calls": 2, "avg_speed_mbps": 111.6, "num_lines": 1}, {"network_id": "NET-6AB0308F", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2025-01-26", "data_usage_gb": 3.9, "call_minutes": 315, "text_messages": 573, "dropped_calls": 1, "avg_speed_mbps": 91.0, "num_lines": 1}, {"network_id": "NET-D200F01D", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2025-02-25", "data_usage_gb": 8.66, "call_minutes": 323, "text_messages": 412, "dropped_calls": 3, "avg_speed_mbps": 111.9, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.66, "data_overage_charges": 54.9}, {"network_id": "NET-5DFD9B0F", "customer_id": "CUST-9DFF2CFD", "plan_id": "PLAN-7BA2741F", "month_start_date": "2025-03-27", "data_usage_gb": 7.79, "call_minutes": 341, "text_messages": 409, "dropped_calls": 5, "avg_speed_mbps": 116.7, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 2.79, "data_overage_charges": 41.85}]