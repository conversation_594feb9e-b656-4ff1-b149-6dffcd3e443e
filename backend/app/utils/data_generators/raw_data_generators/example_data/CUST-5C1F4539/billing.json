[{"billing_id": "BILL-B2C8CABB", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-05-13", "base_amount": 60, "add_on_charges": 8.99, "taxes": 5.69, "total_amount": 62.68, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-7AC15143", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-06-12", "base_amount": 60, "add_on_charges": 8.99, "taxes": 5.76, "total_amount": 62.75, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-DB21ECBD", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-07-12", "base_amount": 60, "add_on_charges": 8.99, "taxes": 6.55, "total_amount": 63.54, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A15B3628", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-08-11", "base_amount": 60, "add_on_charges": 8.99, "taxes": 4.58, "total_amount": 61.57, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-19E61EA6", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-09-10", "base_amount": 60, "add_on_charges": 8.99, "taxes": 6.83, "total_amount": 63.82, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-552F99D0", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-10-10", "base_amount": 60, "add_on_charges": 8.99, "taxes": 6.57, "total_amount": 63.56, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-99258C19", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-11-09", "base_amount": 60, "add_on_charges": 8.99, "taxes": 5.29, "total_amount": 62.28, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-080204C9", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2024-12-09", "base_amount": 60, "add_on_charges": 8.99, "taxes": 7.08, "total_amount": 76.07, "payment_status": "Paid"}, {"billing_id": "BILL-5D015013", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2025-01-08", "base_amount": 60, "add_on_charges": 8.99, "taxes": 6.32, "total_amount": 75.31, "payment_status": "Paid"}, {"billing_id": "BILL-0B965E0D", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2025-02-07", "base_amount": 60, "add_on_charges": 8.99, "taxes": 7.73, "total_amount": 76.72, "payment_status": "Paid"}, {"billing_id": "BILL-B4E74098", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2025-03-09", "base_amount": 60, "add_on_charges": 8.99, "taxes": 6.54, "total_amount": 75.53, "payment_status": "Paid"}, {"billing_id": "BILL-097A8F1D", "customer_id": "CUST-5C1F4539", "plan_id": "PLAN-57EEDB43", "billing_date": "2025-04-08", "base_amount": 60, "add_on_charges": 8.99, "taxes": 6.82, "total_amount": 75.81, "payment_status": "Paid"}]