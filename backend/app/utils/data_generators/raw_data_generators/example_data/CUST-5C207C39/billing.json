[{"billing_id": "BILL-6F8A5E8C", "customer_id": "CUST-5C207C39", "plan_id": "PLAN-52209B89", "billing_date": "2025-01-01", "base_amount": 70, "add_on_charges": 4.99, "taxes": 5.7, "total_amount": 76.69, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-AEB3149F", "customer_id": "CUST-5C207C39", "plan_id": "PLAN-52209B89", "billing_date": "2025-01-31", "base_amount": 70, "add_on_charges": 4.99, "taxes": 6.42, "total_amount": 82.41, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer", "one_time_charges": 15, "one_time_description": "Device upgrade fee"}, {"billing_id": "BILL-EB811C34", "customer_id": "CUST-5C207C39", "plan_id": "PLAN-52209B89", "billing_date": "2025-03-02", "base_amount": 70, "add_on_charges": 4.99, "taxes": 5.87, "total_amount": 66.86, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-D09B8019", "customer_id": "CUST-5C207C39", "plan_id": "PLAN-52209B89", "billing_date": "2025-04-01", "base_amount": 70, "add_on_charges": 4.99, "taxes": 6.47, "total_amount": 67.46, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}]