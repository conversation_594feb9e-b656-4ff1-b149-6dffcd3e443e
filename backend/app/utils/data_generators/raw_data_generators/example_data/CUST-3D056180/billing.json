[{"billing_id": "BILL-DA17AB4B", "customer_id": "CUST-3D056180", "plan_id": "PLAN-0160D0A9", "billing_date": "2024-12-01", "base_amount": 90, "add_on_charges": 19.99, "taxes": 8.38, "total_amount": 100.37, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-67A044F8", "customer_id": "CUST-3D056180", "plan_id": "PLAN-0160D0A9", "billing_date": "2024-12-31", "base_amount": 90, "add_on_charges": 19.99, "taxes": 8.59, "total_amount": 100.58, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-6AF01F2F", "customer_id": "CUST-3D056180", "plan_id": "PLAN-0160D0A9", "billing_date": "2025-01-30", "base_amount": 90, "add_on_charges": 19.99, "taxes": 10.98, "total_amount": 102.97, "payment_status": "Late", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-ABC83FBD", "customer_id": "CUST-3D056180", "plan_id": "PLAN-0160D0A9", "billing_date": "2025-03-01", "base_amount": 90, "add_on_charges": 19.99, "taxes": 8.91, "total_amount": 100.9, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-243D9159", "customer_id": "CUST-3D056180", "plan_id": "PLAN-0160D0A9", "billing_date": "2025-03-31", "base_amount": 90, "add_on_charges": 19.99, "taxes": 9.89, "total_amount": 101.88, "payment_status": "Partial", "discount_amount": 18.0, "discount_reason": "New Customer"}]