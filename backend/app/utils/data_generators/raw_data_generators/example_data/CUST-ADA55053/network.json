[{"network_id": "NET-40821943", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-02-01", "data_usage_gb": 4.88, "call_minutes": 336, "text_messages": 182, "dropped_calls": 3, "avg_speed_mbps": 102.5, "num_lines": 1}, {"network_id": "NET-AE059B0C", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-03-03", "data_usage_gb": 3.78, "call_minutes": 322, "text_messages": 216, "dropped_calls": 9, "avg_speed_mbps": 81.7, "num_lines": 1}, {"network_id": "NET-63C059D4", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-04-02", "data_usage_gb": 5.78, "call_minutes": 373, "text_messages": 180, "dropped_calls": 8, "avg_speed_mbps": 89.2, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 0.78, "data_overage_charges": 11.7}, {"network_id": "NET-E995E860", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-05-02", "data_usage_gb": 4.87, "call_minutes": 425, "text_messages": 202, "dropped_calls": 4, "avg_speed_mbps": 72.7, "num_lines": 1}, {"network_id": "NET-C9140540", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-06-01", "data_usage_gb": 4.76, "call_minutes": 405, "text_messages": 171, "dropped_calls": 4, "avg_speed_mbps": 103.1, "num_lines": 1}, {"network_id": "NET-BEAF5432", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-07-01", "data_usage_gb": 4.59, "call_minutes": 346, "text_messages": 175, "dropped_calls": 6, "avg_speed_mbps": 107.2, "num_lines": 1}, {"network_id": "NET-363A8567", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-07-31", "data_usage_gb": 3.66, "call_minutes": 425, "text_messages": 174, "dropped_calls": 0, "avg_speed_mbps": 88.2, "num_lines": 1}, {"network_id": "NET-B2019016", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-08-30", "data_usage_gb": 3.79, "call_minutes": 379, "text_messages": 170, "dropped_calls": 4, "avg_speed_mbps": 100.2, "num_lines": 1}, {"network_id": "NET-E837CADE", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-09-29", "data_usage_gb": 5.35, "call_minutes": 302, "text_messages": 211, "dropped_calls": 5, "avg_speed_mbps": 118.0, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 0.35, "data_overage_charges": 5.25}, {"network_id": "NET-B3FD83A9", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-10-29", "data_usage_gb": 4.07, "call_minutes": 322, "text_messages": 180, "dropped_calls": 1, "avg_speed_mbps": 106.5, "num_lines": 1}, {"network_id": "NET-F707521F", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-11-28", "data_usage_gb": 3.53, "call_minutes": 388, "text_messages": 171, "dropped_calls": 9, "avg_speed_mbps": 96.2, "num_lines": 1}, {"network_id": "NET-25B90DE3", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2022-12-28", "data_usage_gb": 4.99, "call_minutes": 360, "text_messages": 225, "dropped_calls": 4, "avg_speed_mbps": 87.3, "num_lines": 1}, {"network_id": "NET-C6CEFF8A", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2023-01-27", "data_usage_gb": 4.7, "call_minutes": 343, "text_messages": 171, "dropped_calls": 3, "avg_speed_mbps": 115.2, "num_lines": 1}, {"network_id": "NET-C2EDE181", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2023-02-26", "data_usage_gb": 4.78, "call_minutes": 351, "text_messages": 187, "dropped_calls": 7, "avg_speed_mbps": 113.3, "num_lines": 1}, {"network_id": "NET-E6DBA8E1", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2023-03-28", "data_usage_gb": 3.56, "call_minutes": 306, "text_messages": 208, "dropped_calls": 3, "avg_speed_mbps": 99.7, "num_lines": 1}, {"network_id": "NET-F3763075", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2023-04-27", "data_usage_gb": 4.45, "call_minutes": 372, "text_messages": 238, "dropped_calls": 3, "avg_speed_mbps": 69.5, "num_lines": 1}, {"network_id": "NET-0D41276C", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-1572500E", "month_start_date": "2023-05-27", "data_usage_gb": 4.04, "call_minutes": 348, "text_messages": 176, "dropped_calls": 12, "avg_speed_mbps": 70.5, "num_lines": 1}, {"network_id": "NET-08B6A3C4", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-06-01", "data_usage_gb": 11.12, "call_minutes": 781, "text_messages": 330, "dropped_calls": 6, "avg_speed_mbps": 142.9, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 6.12, "data_overage_charges": 91.8}, {"network_id": "NET-EA83153A", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-07-01", "data_usage_gb": 4.35, "call_minutes": 790, "text_messages": 458, "dropped_calls": 7, "avg_speed_mbps": 136.6, "num_lines": 2}, {"network_id": "NET-32303BBB", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-07-31", "data_usage_gb": 3.74, "call_minutes": 594, "text_messages": 430, "dropped_calls": 3, "avg_speed_mbps": 75.9, "num_lines": 2}, {"network_id": "NET-DC543062", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-08-30", "data_usage_gb": 8.71, "call_minutes": 786, "text_messages": 420, "dropped_calls": 7, "avg_speed_mbps": 135.8, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 3.71, "data_overage_charges": 55.65}, {"network_id": "NET-DBF29746", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-09-29", "data_usage_gb": 3.65, "call_minutes": 858, "text_messages": 459, "dropped_calls": 9, "avg_speed_mbps": 169.5, "num_lines": 2}, {"network_id": "NET-4F9282F7", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-10-29", "data_usage_gb": 3.54, "call_minutes": 852, "text_messages": 332, "dropped_calls": 11, "avg_speed_mbps": 153.3, "num_lines": 2}, {"network_id": "NET-E60DD173", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-11-28", "data_usage_gb": 3.69, "call_minutes": 688, "text_messages": 459, "dropped_calls": 6, "avg_speed_mbps": 120.7, "num_lines": 2}, {"network_id": "NET-871267B8", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2023-12-28", "data_usage_gb": 8.6, "call_minutes": 629, "text_messages": 347, "dropped_calls": 7, "avg_speed_mbps": 163.5, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 3.6, "data_overage_charges": 54.0}, {"network_id": "NET-3241D65C", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-01-27", "data_usage_gb": 3.64, "call_minutes": 705, "text_messages": 396, "dropped_calls": 23, "avg_speed_mbps": 155.3, "num_lines": 2}, {"network_id": "NET-D3F805DC", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-02-26", "data_usage_gb": 3.7, "call_minutes": 707, "text_messages": 428, "dropped_calls": 5, "avg_speed_mbps": 174.3, "num_lines": 2}, {"network_id": "NET-9705B726", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-03-27", "data_usage_gb": 4.29, "call_minutes": 656, "text_messages": 476, "dropped_calls": 10, "avg_speed_mbps": 129.9, "num_lines": 2}, {"network_id": "NET-02F55B9D", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-04-26", "data_usage_gb": 4.85, "call_minutes": 745, "text_messages": 453, "dropped_calls": 3, "avg_speed_mbps": 125.9, "num_lines": 2}, {"network_id": "NET-2C9C632B", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-05-26", "data_usage_gb": 11.62, "call_minutes": 728, "text_messages": 377, "dropped_calls": 6, "avg_speed_mbps": 179.6, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 6.62, "data_overage_charges": 99.3}, {"network_id": "NET-4B986D41", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-06-25", "data_usage_gb": 9.85, "call_minutes": 813, "text_messages": 349, "dropped_calls": 8, "avg_speed_mbps": 130.3, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 4.85, "data_overage_charges": 72.75}, {"network_id": "NET-AD0B59F0", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-07-25", "data_usage_gb": 3.94, "call_minutes": 709, "text_messages": 449, "dropped_calls": 11, "avg_speed_mbps": 167.4, "num_lines": 2}, {"network_id": "NET-2BDF832D", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-08-24", "data_usage_gb": 4.32, "call_minutes": 829, "text_messages": 346, "dropped_calls": 13, "avg_speed_mbps": 121.8, "num_lines": 2}, {"network_id": "NET-C664C2E6", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-09-23", "data_usage_gb": 4.54, "call_minutes": 610, "text_messages": 418, "dropped_calls": 7, "avg_speed_mbps": 103.1, "num_lines": 2}, {"network_id": "NET-6B6F74DA", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-10-23", "data_usage_gb": 4.77, "call_minutes": 822, "text_messages": 438, "dropped_calls": 5, "avg_speed_mbps": 175.2, "num_lines": 2}, {"network_id": "NET-C754E958", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-11-22", "data_usage_gb": 4.55, "call_minutes": 728, "text_messages": 324, "dropped_calls": 8, "avg_speed_mbps": 149.4, "num_lines": 2}, {"network_id": "NET-A0A6B0F4", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2024-12-22", "data_usage_gb": 4.39, "call_minutes": 773, "text_messages": 422, "dropped_calls": 6, "avg_speed_mbps": 143.8, "num_lines": 2}, {"network_id": "NET-F6A41512", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2025-01-21", "data_usage_gb": 4.08, "call_minutes": 785, "text_messages": 370, "dropped_calls": 6, "avg_speed_mbps": 136.7, "num_lines": 2}, {"network_id": "NET-31339AB4", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2025-02-20", "data_usage_gb": 4.38, "call_minutes": 729, "text_messages": 359, "dropped_calls": 9, "avg_speed_mbps": 128.8, "num_lines": 2}, {"network_id": "NET-5BDFF951", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-F9F90603", "month_start_date": "2025-03-22", "data_usage_gb": 3.82, "call_minutes": 676, "text_messages": 343, "dropped_calls": 3, "avg_speed_mbps": 128.5, "num_lines": 2}, {"network_id": "NET-BE24BEDC", "customer_id": "CUST-ADA55053", "plan_id": "PLAN-D7DE3B38", "month_start_date": "2025-04-01", "data_usage_gb": 4.43, "call_minutes": 430, "text_messages": 190, "dropped_calls": 2, "avg_speed_mbps": 81.6, "num_lines": 1}]