[{"billing_id": "BILL-F48C09A1", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-A9574081", "billing_date": "2024-04-01", "base_amount": 80, "add_on_charges": 0, "taxes": 8.62, "total_amount": 88.62, "payment_status": "Paid"}, {"billing_id": "BILL-5B16412C", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-A9574081", "billing_date": "2024-05-01", "base_amount": 80, "add_on_charges": 0, "taxes": 9.18, "total_amount": 89.18, "payment_status": "Late"}, {"billing_id": "BILL-49AE22EB", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-A9574081", "billing_date": "2024-05-31", "base_amount": 80, "add_on_charges": 0, "taxes": 9.24, "total_amount": 89.24, "payment_status": "Paid"}, {"billing_id": "BILL-DDCC6865", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-A9574081", "billing_date": "2024-06-30", "base_amount": 80, "add_on_charges": 0, "taxes": 8.71, "total_amount": 88.71, "payment_status": "Paid"}, {"billing_id": "BILL-EDA518D9", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-A9574081", "billing_date": "2024-07-30", "base_amount": 80, "add_on_charges": 0, "taxes": 7.21, "total_amount": 87.21, "payment_status": "Paid"}, {"billing_id": "BILL-7E8D62E4", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-A9574081", "billing_date": "2024-08-29", "base_amount": 80, "add_on_charges": 0, "taxes": 9.07, "total_amount": 89.07, "payment_status": "Paid"}, {"billing_id": "BILL-97189993", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-A9574081", "billing_date": "2024-09-28", "base_amount": 80, "add_on_charges": 0, "taxes": 6.71, "total_amount": 86.71, "payment_status": "Paid"}, {"billing_id": "BILL-1F71C199", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-F43374EB", "billing_date": "2024-10-01", "base_amount": 60, "add_on_charges": 34.97, "taxes": 10.99, "total_amount": 105.96, "payment_status": "Paid"}, {"billing_id": "BILL-F1B9A23B", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-F43374EB", "billing_date": "2024-10-31", "base_amount": 60, "add_on_charges": 34.97, "taxes": 9.58, "total_amount": 104.55, "payment_status": "Paid"}, {"billing_id": "BILL-4BBA6B5D", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-F43374EB", "billing_date": "2024-11-30", "base_amount": 60, "add_on_charges": 34.97, "taxes": 10.48, "total_amount": 105.45, "payment_status": "Paid"}, {"billing_id": "BILL-80DA461F", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-F43374EB", "billing_date": "2024-12-30", "base_amount": 60, "add_on_charges": 34.97, "taxes": 9.65, "total_amount": 104.62, "payment_status": "Paid"}, {"billing_id": "BILL-85BA4379", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-F43374EB", "billing_date": "2025-01-29", "base_amount": 60, "add_on_charges": 34.97, "taxes": 9.29, "total_amount": 104.26, "payment_status": "Paid"}, {"billing_id": "BILL-53702DB6", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-F43374EB", "billing_date": "2025-02-28", "base_amount": 60, "add_on_charges": 34.97, "taxes": 8.38, "total_amount": 103.35, "payment_status": "Paid"}, {"billing_id": "BILL-46B91EE4", "customer_id": "CUST-FCFA4C27", "plan_id": "PLAN-F43374EB", "billing_date": "2025-03-30", "base_amount": 60, "add_on_charges": 34.97, "taxes": 9.94, "total_amount": 104.91, "payment_status": "Paid"}]