[{"billing_id": "BILL-9B1FE4E7", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2022-07-01", "base_amount": 90, "add_on_charges": 19.98, "taxes": 10.32, "total_amount": 102.3, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-B29EB27E", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2022-07-31", "base_amount": 90, "add_on_charges": 19.98, "taxes": 10.01, "total_amount": 101.99, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-887CD32B", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2022-08-30", "base_amount": 90, "add_on_charges": 19.98, "taxes": 8.39, "total_amount": 100.37, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-52A3A993", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2022-09-29", "base_amount": 90, "add_on_charges": 19.98, "taxes": 10.88, "total_amount": 102.86, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5CA3CBC3", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2022-10-29", "base_amount": 90, "add_on_charges": 19.98, "taxes": 9.81, "total_amount": 101.79, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-11388402", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2022-11-28", "base_amount": 90, "add_on_charges": 19.98, "taxes": 8.23, "total_amount": 100.21, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5AE63221", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2022-12-28", "base_amount": 90, "add_on_charges": 19.98, "taxes": 10.85, "total_amount": 102.83, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-684740D6", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-01-27", "base_amount": 90, "add_on_charges": 19.98, "taxes": 8.95, "total_amount": 118.93, "payment_status": "Paid"}, {"billing_id": "BILL-BECA56DE", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-02-26", "base_amount": 90, "add_on_charges": 19.98, "taxes": 9.14, "total_amount": 119.12, "payment_status": "Paid"}, {"billing_id": "BILL-D8FC6F71", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-03-28", "base_amount": 90, "add_on_charges": 19.98, "taxes": 10.41, "total_amount": 120.39, "payment_status": "Paid"}, {"billing_id": "BILL-67E9FAF0", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-04-27", "base_amount": 90, "add_on_charges": 19.98, "taxes": 11.0, "total_amount": 120.98, "payment_status": "Paid"}, {"billing_id": "BILL-54323B76", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-05-27", "base_amount": 90, "add_on_charges": 19.98, "taxes": 12.79, "total_amount": 147.77, "payment_status": "Paid", "one_time_charges": 25, "one_time_description": "Activation fee"}, {"billing_id": "BILL-7F616EB2", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-06-26", "base_amount": 90, "add_on_charges": 19.98, "taxes": 12.0, "total_amount": 121.98, "payment_status": "Paid"}, {"billing_id": "BILL-B6A63D47", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-07-26", "base_amount": 90, "add_on_charges": 19.98, "taxes": 11.28, "total_amount": 121.26, "payment_status": "Paid"}, {"billing_id": "BILL-06B1A533", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-08-25", "base_amount": 90, "add_on_charges": 19.98, "taxes": 10.25, "total_amount": 120.23, "payment_status": "Paid"}, {"billing_id": "BILL-F2F62902", "customer_id": "CUST-5CE4E2CA", "plan_id": "PLAN-0A87353F", "billing_date": "2023-09-24", "base_amount": 90, "add_on_charges": 19.98, "taxes": 11.38, "total_amount": 121.36, "payment_status": "Paid"}]