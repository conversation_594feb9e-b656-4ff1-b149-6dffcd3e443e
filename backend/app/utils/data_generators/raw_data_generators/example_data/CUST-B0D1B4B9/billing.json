[{"billing_id": "BILL-D6187B29", "customer_id": "CUST-B0D1B4B9", "plan_id": "PLAN-35F60844", "billing_date": "2024-11-01", "base_amount": 60, "add_on_charges": 0, "taxes": 3.98, "total_amount": 51.98, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-4F041E56", "customer_id": "CUST-B0D1B4B9", "plan_id": "PLAN-35F60844", "billing_date": "2024-12-01", "base_amount": 60, "add_on_charges": 0, "taxes": 4.41, "total_amount": 52.41, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-04113743", "customer_id": "CUST-B0D1B4B9", "plan_id": "PLAN-35F60844", "billing_date": "2024-12-31", "base_amount": 60, "add_on_charges": 0, "taxes": 5.67, "total_amount": 53.67, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-C1EB85D1", "customer_id": "CUST-B0D1B4B9", "plan_id": "PLAN-35F60844", "billing_date": "2025-01-30", "base_amount": 60, "add_on_charges": 0, "taxes": 4.17, "total_amount": 52.17, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5C7246BF", "customer_id": "CUST-B0D1B4B9", "plan_id": "PLAN-35F60844", "billing_date": "2025-03-01", "base_amount": 60, "add_on_charges": 0, "taxes": 5.25, "total_amount": 53.25, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-9A7241D7", "customer_id": "CUST-B0D1B4B9", "plan_id": "PLAN-35F60844", "billing_date": "2025-03-31", "base_amount": 60, "add_on_charges": 0, "taxes": 5.56, "total_amount": 53.56, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}]