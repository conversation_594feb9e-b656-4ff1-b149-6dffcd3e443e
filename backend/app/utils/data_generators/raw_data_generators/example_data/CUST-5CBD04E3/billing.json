[{"billing_id": "BILL-EFD4280A", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2023-07-01", "base_amount": 110, "add_on_charges": 35.97, "taxes": 11.28, "total_amount": 135.25, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-3B2632D2", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2023-07-31", "base_amount": 110, "add_on_charges": 35.97, "taxes": 12.59, "total_amount": 136.56, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A9D5696E", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2023-08-30", "base_amount": 110, "add_on_charges": 35.97, "taxes": 12.26, "total_amount": 136.23, "payment_status": "Partial", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-9401E829", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2023-09-29", "base_amount": 110, "add_on_charges": 35.97, "taxes": 14.05, "total_amount": 138.02, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-BF703B31", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2023-10-29", "base_amount": 110, "add_on_charges": 35.97, "taxes": 14.22, "total_amount": 138.19, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5B1C902C", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2023-11-28", "base_amount": 110, "add_on_charges": 35.97, "taxes": 11.95, "total_amount": 135.92, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A96432AF", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2023-12-28", "base_amount": 110, "add_on_charges": 35.97, "taxes": 14.28, "total_amount": 138.25, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-17F0A6B5", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-01-27", "base_amount": 110, "add_on_charges": 35.97, "taxes": 15.52, "total_amount": 161.49, "payment_status": "Paid"}, {"billing_id": "BILL-776AD2DB", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-02-26", "base_amount": 110, "add_on_charges": 35.97, "taxes": 14.57, "total_amount": 160.54, "payment_status": "Late"}, {"billing_id": "BILL-41454653", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-03-27", "base_amount": 110, "add_on_charges": 35.97, "taxes": 15.01, "total_amount": 160.98, "payment_status": "Paid"}, {"billing_id": "BILL-56FAC386", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-04-26", "base_amount": 110, "add_on_charges": 35.97, "taxes": 13.22, "total_amount": 159.19, "payment_status": "Paid"}, {"billing_id": "BILL-2298DC6A", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-05-26", "base_amount": 110, "add_on_charges": 35.97, "taxes": 13.73, "total_amount": 159.7, "payment_status": "Paid"}, {"billing_id": "BILL-EC8EE57D", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-06-25", "base_amount": 110, "add_on_charges": 35.97, "taxes": 12.61, "total_amount": 158.58, "payment_status": "Paid"}, {"billing_id": "BILL-49F37DF0", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-07-25", "base_amount": 110, "add_on_charges": 35.97, "taxes": 11.89, "total_amount": 157.86, "payment_status": "Paid"}, {"billing_id": "BILL-BACDF2B1", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-08-24", "base_amount": 110, "add_on_charges": 35.97, "taxes": 16.34, "total_amount": 162.31, "payment_status": "Partial"}, {"billing_id": "BILL-1728A72B", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-09-23", "base_amount": 110, "add_on_charges": 35.97, "taxes": 12.09, "total_amount": 158.06, "payment_status": "Paid"}, {"billing_id": "BILL-9DCE3BF7", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-10-23", "base_amount": 110, "add_on_charges": 35.97, "taxes": 16.07, "total_amount": 162.04, "payment_status": "Paid"}, {"billing_id": "BILL-0B856F2A", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-11-22", "base_amount": 110, "add_on_charges": 35.97, "taxes": 12.18, "total_amount": 158.15, "payment_status": "Paid"}, {"billing_id": "BILL-008A8FAB", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2024-12-22", "base_amount": 110, "add_on_charges": 35.97, "taxes": 13.69, "total_amount": 159.66, "payment_status": "Paid"}, {"billing_id": "BILL-522A84F8", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2025-01-21", "base_amount": 110, "add_on_charges": 35.97, "taxes": 12.76, "total_amount": 158.73, "payment_status": "Paid"}, {"billing_id": "BILL-4580F726", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2025-02-20", "base_amount": 110, "add_on_charges": 35.97, "taxes": 13.95, "total_amount": 159.92, "payment_status": "Paid"}, {"billing_id": "BILL-4F284192", "customer_id": "CUST-5CBD04E3", "plan_id": "PLAN-994C448C", "billing_date": "2025-03-22", "base_amount": 110, "add_on_charges": 35.97, "taxes": 13.19, "total_amount": 159.16, "payment_status": "Paid"}]