[{"network_id": "NET-4C92AF37", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2022-10-01", "data_usage_gb": 3.87, "call_minutes": 197, "text_messages": 134, "dropped_calls": 2, "avg_speed_mbps": 93.7, "num_lines": 1}, {"network_id": "NET-40D7C7CE", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2022-10-31", "data_usage_gb": 3.87, "call_minutes": 234, "text_messages": 156, "dropped_calls": 0, "avg_speed_mbps": 86.3, "num_lines": 1}, {"network_id": "NET-0CF7EBC5", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2022-11-30", "data_usage_gb": 4.01, "call_minutes": 249, "text_messages": 178, "dropped_calls": 3, "avg_speed_mbps": 58.1, "num_lines": 1}, {"network_id": "NET-081F2CCE", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2022-12-30", "data_usage_gb": 4.11, "call_minutes": 255, "text_messages": 186, "dropped_calls": 2, "avg_speed_mbps": 93.3, "num_lines": 1}, {"network_id": "NET-B68632C7", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-01-29", "data_usage_gb": 3.85, "call_minutes": 225, "text_messages": 189, "dropped_calls": 3, "avg_speed_mbps": 95.7, "num_lines": 1}, {"network_id": "NET-4E0FCD0E", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-02-28", "data_usage_gb": 4.07, "call_minutes": 267, "text_messages": 180, "dropped_calls": 0, "avg_speed_mbps": 113.2, "num_lines": 1}, {"network_id": "NET-7DF626B8", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-03-30", "data_usage_gb": 4.47, "call_minutes": 232, "text_messages": 151, "dropped_calls": 2, "avg_speed_mbps": 87.2, "num_lines": 1}, {"network_id": "NET-B33F66F9", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-04-29", "data_usage_gb": 3.84, "call_minutes": 205, "text_messages": 132, "dropped_calls": 2, "avg_speed_mbps": 84.0, "num_lines": 1}, {"network_id": "NET-112D9A61", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-05-29", "data_usage_gb": 5.1, "call_minutes": 195, "text_messages": 149, "dropped_calls": 1, "avg_speed_mbps": 85.2, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 0.1, "data_overage_charges": 1.5}, {"network_id": "NET-ED13A4B1", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-06-28", "data_usage_gb": 3.89, "call_minutes": 271, "text_messages": 177, "dropped_calls": 3, "avg_speed_mbps": 86.6, "num_lines": 1}, {"network_id": "NET-0C8B4FFB", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-07-28", "data_usage_gb": 3.86, "call_minutes": 234, "text_messages": 146, "dropped_calls": 4, "avg_speed_mbps": 98.0, "num_lines": 1}, {"network_id": "NET-07D9B015", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-08-27", "data_usage_gb": 4.16, "call_minutes": 237, "text_messages": 181, "dropped_calls": 2, "avg_speed_mbps": 117.9, "num_lines": 1}, {"network_id": "NET-D9D5D666", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-09-26", "data_usage_gb": 4.07, "call_minutes": 242, "text_messages": 189, "dropped_calls": 3, "avg_speed_mbps": 64.4, "num_lines": 1}, {"network_id": "NET-B73A11B5", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-10-26", "data_usage_gb": 4.75, "call_minutes": 245, "text_messages": 134, "dropped_calls": 1, "avg_speed_mbps": 114.0, "num_lines": 1}, {"network_id": "NET-39CF0426", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-11-25", "data_usage_gb": 4.86, "call_minutes": 206, "text_messages": 155, "dropped_calls": 3, "avg_speed_mbps": 88.9, "num_lines": 1}, {"network_id": "NET-DE73752A", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2023-12-25", "data_usage_gb": 3.58, "call_minutes": 240, "text_messages": 143, "dropped_calls": 4, "avg_speed_mbps": 74.0, "num_lines": 1}, {"network_id": "NET-D6611079", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2024-01-24", "data_usage_gb": 4.69, "call_minutes": 205, "text_messages": 146, "dropped_calls": 2, "avg_speed_mbps": 76.7, "num_lines": 1}, {"network_id": "NET-452AE35C", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-D3A345CC", "month_start_date": "2024-02-23", "data_usage_gb": 3.62, "call_minutes": 216, "text_messages": 181, "dropped_calls": 3, "avg_speed_mbps": 109.1, "num_lines": 1}, {"network_id": "NET-4C822075", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-03-01", "data_usage_gb": 3.8, "call_minutes": 219, "text_messages": 147, "dropped_calls": 9, "avg_speed_mbps": 48.4, "num_lines": 1}, {"network_id": "NET-B64139F6", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-03-31", "data_usage_gb": 4.86, "call_minutes": 249, "text_messages": 129, "dropped_calls": 2, "avg_speed_mbps": 28.2, "num_lines": 1}, {"network_id": "NET-8D4143C0", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-04-30", "data_usage_gb": 4.12, "call_minutes": 255, "text_messages": 133, "dropped_calls": 3, "avg_speed_mbps": 41.4, "num_lines": 1}, {"network_id": "NET-D7FEA868", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-05-30", "data_usage_gb": 4.51, "call_minutes": 245, "text_messages": 152, "dropped_calls": 1, "avg_speed_mbps": 52.7, "num_lines": 1}, {"network_id": "NET-AFCAEE4B", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-06-29", "data_usage_gb": 4.68, "call_minutes": 225, "text_messages": 174, "dropped_calls": 5, "avg_speed_mbps": 55.4, "num_lines": 1}, {"network_id": "NET-86FB7AC5", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-07-29", "data_usage_gb": 4.32, "call_minutes": 270, "text_messages": 139, "dropped_calls": 4, "avg_speed_mbps": 49.3, "num_lines": 1}, {"network_id": "NET-838D53C8", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-08-28", "data_usage_gb": 4.74, "call_minutes": 198, "text_messages": 177, "dropped_calls": 1, "avg_speed_mbps": 25.7, "num_lines": 1}, {"network_id": "NET-07B53544", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-0E496E62", "month_start_date": "2024-09-27", "data_usage_gb": 3.68, "call_minutes": 281, "text_messages": 160, "dropped_calls": 1, "avg_speed_mbps": 50.9, "num_lines": 1}, {"network_id": "NET-AAA09594", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-A3C21DA1", "month_start_date": "2024-10-01", "data_usage_gb": 4.04, "call_minutes": 266, "text_messages": 183, "dropped_calls": 3, "avg_speed_mbps": 63.7, "num_lines": 1}, {"network_id": "NET-75D0A6BA", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-A3C21DA1", "month_start_date": "2024-10-31", "data_usage_gb": 5.2, "call_minutes": 277, "text_messages": 152, "dropped_calls": 3, "avg_speed_mbps": 74.9, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 0.2, "data_overage_charges": 3.0}, {"network_id": "NET-4F09176F", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-A3C21DA1", "month_start_date": "2024-11-30", "data_usage_gb": 4.7, "call_minutes": 261, "text_messages": 133, "dropped_calls": 4, "avg_speed_mbps": 84.7, "num_lines": 1}, {"network_id": "NET-D69AFA34", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-A3C21DA1", "month_start_date": "2024-12-30", "data_usage_gb": 4.49, "call_minutes": 221, "text_messages": 166, "dropped_calls": 1, "avg_speed_mbps": 80.7, "num_lines": 1}, {"network_id": "NET-7E1E8A38", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-A3C21DA1", "month_start_date": "2025-01-29", "data_usage_gb": 3.68, "call_minutes": 275, "text_messages": 150, "dropped_calls": 7, "avg_speed_mbps": 68.2, "num_lines": 1}, {"network_id": "NET-A47D9EB1", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-A3C21DA1", "month_start_date": "2025-02-28", "data_usage_gb": 4.46, "call_minutes": 226, "text_messages": 179, "dropped_calls": 5, "avg_speed_mbps": 60.4, "num_lines": 1}, {"network_id": "NET-B93A7FD3", "customer_id": "CUST-6D9A2C11", "plan_id": "PLAN-A3C21DA1", "month_start_date": "2025-03-30", "data_usage_gb": 3.6, "call_minutes": 266, "text_messages": 151, "dropped_calls": 3, "avg_speed_mbps": 77.0, "num_lines": 1}]