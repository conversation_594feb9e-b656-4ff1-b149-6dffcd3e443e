[{"billing_id": "BILL-1EFF1121", "customer_id": "CUST-5931B4DE", "plan_id": "PLAN-9F5C047C", "billing_date": "2024-12-01", "base_amount": 110, "add_on_charges": 16.98, "taxes": 10.38, "total_amount": 115.36, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-13EDAD0E", "customer_id": "CUST-5931B4DE", "plan_id": "PLAN-9F5C047C", "billing_date": "2024-12-31", "base_amount": 110, "add_on_charges": 16.98, "taxes": 9.43, "total_amount": 114.41, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-C9E0A365", "customer_id": "CUST-5931B4DE", "plan_id": "PLAN-9F5C047C", "billing_date": "2025-01-30", "base_amount": 110, "add_on_charges": 16.98, "taxes": 9.37, "total_amount": 114.35, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-F0A5FD59", "customer_id": "CUST-5931B4DE", "plan_id": "PLAN-9F5C047C", "billing_date": "2025-03-01", "base_amount": 110, "add_on_charges": 16.98, "taxes": 12.5, "total_amount": 117.48, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-1C5EB7AA", "customer_id": "CUST-5931B4DE", "plan_id": "PLAN-9F5C047C", "billing_date": "2025-03-31", "base_amount": 110, "add_on_charges": 16.98, "taxes": 9.23, "total_amount": 114.21, "payment_status": "Paid", "discount_amount": 22.0, "discount_reason": "New Customer"}]