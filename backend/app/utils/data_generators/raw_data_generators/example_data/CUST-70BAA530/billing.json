[{"billing_id": "BILL-C4AEF334", "customer_id": "CUST-70BAA530", "plan_id": "PLAN-63CCBED0", "billing_date": "2024-12-01", "base_amount": 160, "add_on_charges": 8.99, "taxes": 16.33, "total_amount": 153.32, "payment_status": "Paid", "discount_amount": 32.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-9CA7ED43", "customer_id": "CUST-70BAA530", "plan_id": "PLAN-63CCBED0", "billing_date": "2024-12-31", "base_amount": 160, "add_on_charges": 8.99, "taxes": 10.97, "total_amount": 147.96, "payment_status": "Paid", "discount_amount": 32.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-9EC8A195", "customer_id": "CUST-70BAA530", "plan_id": "PLAN-63CCBED0", "billing_date": "2025-01-30", "base_amount": 160, "add_on_charges": 8.99, "taxes": 11.26, "total_amount": 148.25, "payment_status": "Paid", "discount_amount": 32.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-8F2012E5", "customer_id": "CUST-70BAA530", "plan_id": "PLAN-63CCBED0", "billing_date": "2025-03-01", "base_amount": 160, "add_on_charges": 8.99, "taxes": 15.76, "total_amount": 152.75, "payment_status": "Paid", "discount_amount": 32.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-06688B81", "customer_id": "CUST-70BAA530", "plan_id": "PLAN-63CCBED0", "billing_date": "2025-03-31", "base_amount": 160, "add_on_charges": 8.99, "taxes": 14.93, "total_amount": 151.92, "payment_status": "Paid", "discount_amount": 32.0, "discount_reason": "New Customer"}]