[{"billing_id": "BILL-522C7186", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-05-01", "base_amount": 70, "add_on_charges": 0, "taxes": 6.59, "total_amount": 62.59, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-3DD094FF", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-05-31", "base_amount": 70, "add_on_charges": 0, "taxes": 6.1, "total_amount": 62.1, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-D16203C1", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-06-30", "base_amount": 70, "add_on_charges": 0, "taxes": 5.12, "total_amount": 61.12, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-63BC7104", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-07-30", "base_amount": 70, "add_on_charges": 0, "taxes": 4.92, "total_amount": 60.92, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-807403D1", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-08-29", "base_amount": 70, "add_on_charges": 0, "taxes": 5.96, "total_amount": 61.96, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-7D4E1106", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-09-28", "base_amount": 70, "add_on_charges": 0, "taxes": 5.43, "total_amount": 61.43, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-E6C5117B", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-10-28", "base_amount": 70, "add_on_charges": 0, "taxes": 6.42, "total_amount": 62.42, "payment_status": "Paid", "discount_amount": 14.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A15D54BB", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-11-27", "base_amount": 70, "add_on_charges": 0, "taxes": 6.81, "total_amount": 76.81, "payment_status": "Paid"}, {"billing_id": "BILL-CCB4F227", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2022-12-27", "base_amount": 70, "add_on_charges": 0, "taxes": 8.32, "total_amount": 78.32, "payment_status": "Paid"}, {"billing_id": "BILL-E6A53DF8", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-01-26", "base_amount": 70, "add_on_charges": 0, "taxes": 5.65, "total_amount": 75.65, "payment_status": "Paid"}, {"billing_id": "BILL-524EB4D3", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-02-25", "base_amount": 70, "add_on_charges": 0, "taxes": 6.99, "total_amount": 76.99, "payment_status": "Unpaid"}, {"billing_id": "BILL-306FBE86", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-03-27", "base_amount": 70, "add_on_charges": 0, "taxes": 5.99, "total_amount": 75.99, "payment_status": "Paid"}, {"billing_id": "BILL-E2C49238", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-04-26", "base_amount": 70, "add_on_charges": 0, "taxes": 7.8, "total_amount": 102.8, "payment_status": "Paid", "one_time_charges": 25, "one_time_description": "Activation fee"}, {"billing_id": "BILL-C3C3C46F", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-05-26", "base_amount": 70, "add_on_charges": 0, "taxes": 6.42, "total_amount": 76.42, "payment_status": "Paid"}, {"billing_id": "BILL-F27279A8", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-06-25", "base_amount": 70, "add_on_charges": 0, "taxes": 8.08, "total_amount": 78.08, "payment_status": "Paid"}, {"billing_id": "BILL-AE2E0E69", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-07-25", "base_amount": 70, "add_on_charges": 0, "taxes": 7.32, "total_amount": 77.32, "payment_status": "Paid"}, {"billing_id": "BILL-DA9A6625", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-08-24", "base_amount": 70, "add_on_charges": 0, "taxes": 8.15, "total_amount": 78.15, "payment_status": "Paid"}, {"billing_id": "BILL-9DC41B58", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-09-23", "base_amount": 70, "add_on_charges": 0, "taxes": 7.17, "total_amount": 77.17, "payment_status": "Paid"}, {"billing_id": "BILL-0C387EF6", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-10-23", "base_amount": 70, "add_on_charges": 0, "taxes": 5.73, "total_amount": 75.73, "payment_status": "Paid"}, {"billing_id": "BILL-84919FC3", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-11-22", "base_amount": 70, "add_on_charges": 0, "taxes": 6.63, "total_amount": 76.63, "payment_status": "Paid"}, {"billing_id": "BILL-27EC31EE", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2023-12-22", "base_amount": 70, "add_on_charges": 0, "taxes": 6.57, "total_amount": 76.57, "payment_status": "Paid"}, {"billing_id": "BILL-0FAC1A88", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2024-01-21", "base_amount": 70, "add_on_charges": 0, "taxes": 7.68, "total_amount": 77.68, "payment_status": "Paid"}, {"billing_id": "BILL-9A78040E", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2024-02-20", "base_amount": 70, "add_on_charges": 0, "taxes": 6.08, "total_amount": 76.08, "payment_status": "Paid"}, {"billing_id": "BILL-551E8804", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2024-03-21", "base_amount": 70, "add_on_charges": 0, "taxes": 7.16, "total_amount": 77.16, "payment_status": "Late"}, {"billing_id": "BILL-BDD556F1", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-6E1F6FDD", "billing_date": "2024-04-20", "base_amount": 70, "add_on_charges": 0, "taxes": 6.39, "total_amount": 76.39, "payment_status": "Paid"}, {"billing_id": "BILL-87805A8A", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-05-01", "base_amount": 80, "add_on_charges": 30.97, "taxes": 12.51, "total_amount": 125.48, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "Upgrade", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-84CA5EE7", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-05-31", "base_amount": 80, "add_on_charges": 30.97, "taxes": 8.67, "total_amount": 111.64, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-826B5B93", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-06-30", "base_amount": 80, "add_on_charges": 30.97, "taxes": 14.07, "total_amount": 137.04, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "Upgrade", "one_time_charges": 20, "one_time_description": "International usage"}, {"billing_id": "BILL-E903C925", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-07-30", "base_amount": 80, "add_on_charges": 30.97, "taxes": 10.37, "total_amount": 113.34, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-E5F659AE", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-08-29", "base_amount": 80, "add_on_charges": 30.97, "taxes": 11.44, "total_amount": 122.41, "payment_status": "Paid"}, {"billing_id": "BILL-D1A2794C", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-09-28", "base_amount": 80, "add_on_charges": 30.97, "taxes": 12.37, "total_amount": 123.34, "payment_status": "Paid"}, {"billing_id": "BILL-6D745A67", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-10-28", "base_amount": 80, "add_on_charges": 30.97, "taxes": 13.06, "total_amount": 124.03, "payment_status": "Paid"}, {"billing_id": "BILL-6B10791B", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-11-27", "base_amount": 80, "add_on_charges": 30.97, "taxes": 12.56, "total_amount": 123.53, "payment_status": "Paid"}, {"billing_id": "BILL-8C3357C2", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2024-12-27", "base_amount": 80, "add_on_charges": 30.97, "taxes": 9.39, "total_amount": 120.36, "payment_status": "Partial"}, {"billing_id": "BILL-295A5C41", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2025-01-26", "base_amount": 80, "add_on_charges": 30.97, "taxes": 10.9, "total_amount": 121.87, "payment_status": "Paid"}, {"billing_id": "BILL-7FDC0E0E", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2025-02-25", "base_amount": 80, "add_on_charges": 30.97, "taxes": 9.03, "total_amount": 120.0, "payment_status": "Paid"}, {"billing_id": "BILL-1128203B", "customer_id": "CUST-B06ED8F4", "plan_id": "PLAN-D015F9DA", "billing_date": "2025-03-27", "base_amount": 80, "add_on_charges": 30.97, "taxes": 11.34, "total_amount": 122.31, "payment_status": "Paid"}]