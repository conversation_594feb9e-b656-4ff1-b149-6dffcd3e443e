[{"billing_id": "BILL-A0A30B33", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2023-08-01", "base_amount": 90, "add_on_charges": 16.98, "taxes": 9.47, "total_amount": 98.45, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-D784596B", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2023-08-31", "base_amount": 90, "add_on_charges": 16.98, "taxes": 7.55, "total_amount": 96.53, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A5707C9C", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2023-09-30", "base_amount": 90, "add_on_charges": 16.98, "taxes": 8.49, "total_amount": 97.47, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5B0DD8ED", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2023-10-30", "base_amount": 90, "add_on_charges": 16.98, "taxes": 8.23, "total_amount": 97.21, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-C755AD6E", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2023-11-29", "base_amount": 90, "add_on_charges": 16.98, "taxes": 7.45, "total_amount": 96.43, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-E4B147EA", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2023-12-29", "base_amount": 90, "add_on_charges": 16.98, "taxes": 8.44, "total_amount": 97.42, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-33F1B249", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-01-28", "base_amount": 90, "add_on_charges": 16.98, "taxes": 8.58, "total_amount": 97.56, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-B52118A9", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-02-27", "base_amount": 90, "add_on_charges": 16.98, "taxes": 14.02, "total_amount": 131.0, "payment_status": "Paid", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-FC4CA94F", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-03-28", "base_amount": 90, "add_on_charges": 16.98, "taxes": 12.03, "total_amount": 119.01, "payment_status": "Paid"}, {"billing_id": "BILL-E56B9BB6", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-04-27", "base_amount": 90, "add_on_charges": 16.98, "taxes": 13.21, "total_amount": 140.19, "payment_status": "Paid", "one_time_charges": 20, "one_time_description": "International usage"}, {"billing_id": "BILL-8B0A0289", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-05-27", "base_amount": 90, "add_on_charges": 16.98, "taxes": 12.25, "total_amount": 119.23, "payment_status": "Paid"}, {"billing_id": "BILL-7773A34E", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-06-26", "base_amount": 90, "add_on_charges": 16.98, "taxes": 10.01, "total_amount": 116.99, "payment_status": "Paid"}, {"billing_id": "BILL-AE8BC845", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-07-26", "base_amount": 90, "add_on_charges": 16.98, "taxes": 10.37, "total_amount": 117.35, "payment_status": "Paid"}, {"billing_id": "BILL-6B9CB35E", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-08-25", "base_amount": 90, "add_on_charges": 16.98, "taxes": 12.08, "total_amount": 134.06, "payment_status": "Paid", "one_time_charges": 15, "one_time_description": "Device upgrade fee"}, {"billing_id": "BILL-14921357", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-09-24", "base_amount": 90, "add_on_charges": 16.98, "taxes": 10.86, "total_amount": 117.84, "payment_status": "Paid"}, {"billing_id": "BILL-873D5922", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-10-24", "base_amount": 90, "add_on_charges": 16.98, "taxes": 9.28, "total_amount": 116.26, "payment_status": "Paid"}, {"billing_id": "BILL-E485FD94", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-11-23", "base_amount": 90, "add_on_charges": 16.98, "taxes": 10.2, "total_amount": 117.18, "payment_status": "Paid"}, {"billing_id": "BILL-DA062031", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2024-12-23", "base_amount": 90, "add_on_charges": 16.98, "taxes": 10.5, "total_amount": 117.48, "payment_status": "Paid"}, {"billing_id": "BILL-EAF238E4", "customer_id": "CUST-064B7901", "plan_id": "PLAN-A5A43E27", "billing_date": "2025-01-22", "base_amount": 90, "add_on_charges": 16.98, "taxes": 11.21, "total_amount": 118.19, "payment_status": "Paid"}, {"billing_id": "BILL-01F4D468", "customer_id": "CUST-064B7901", "plan_id": "PLAN-FCAD5381", "billing_date": "2025-02-01", "base_amount": 90, "add_on_charges": 7.99, "taxes": 10.38, "total_amount": 108.37, "payment_status": "Paid"}, {"billing_id": "BILL-2E9733C3", "customer_id": "CUST-064B7901", "plan_id": "PLAN-FCAD5381", "billing_date": "2025-03-03", "base_amount": 90, "add_on_charges": 7.99, "taxes": 11.13, "total_amount": 109.12, "payment_status": "Paid"}, {"billing_id": "BILL-51C2D019", "customer_id": "CUST-064B7901", "plan_id": "PLAN-FCAD5381", "billing_date": "2025-04-02", "base_amount": 90, "add_on_charges": 7.99, "taxes": 11.51, "total_amount": 109.5, "payment_status": "Paid"}]