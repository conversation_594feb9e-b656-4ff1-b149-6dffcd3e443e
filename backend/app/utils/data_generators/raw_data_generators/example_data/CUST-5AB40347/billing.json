[{"billing_id": "BILL-EBD1E2E2", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-05-01", "base_amount": 40, "add_on_charges": 0, "taxes": 3.46, "total_amount": 35.46, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-D62AAD6C", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-05-31", "base_amount": 40, "add_on_charges": 0, "taxes": 3.05, "total_amount": 35.05, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-2A28E617", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-06-30", "base_amount": 40, "add_on_charges": 0, "taxes": 3.09, "total_amount": 35.09, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-8156D8EC", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-07-30", "base_amount": 40, "add_on_charges": 0, "taxes": 2.81, "total_amount": 34.81, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-8F65EA9A", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-08-29", "base_amount": 40, "add_on_charges": 0, "taxes": 5.27, "total_amount": 62.27, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "New Customer", "one_time_charges": 25, "one_time_description": "Activation fee"}, {"billing_id": "BILL-AAFD42AE", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-09-28", "base_amount": 40, "add_on_charges": 0, "taxes": 3.05, "total_amount": 35.05, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-F196EBE9", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-10-28", "base_amount": 40, "add_on_charges": 0, "taxes": 3.4, "total_amount": 35.4, "payment_status": "Paid", "discount_amount": 8.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-910B0D55", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-11-27", "base_amount": 40, "add_on_charges": 0, "taxes": 4.31, "total_amount": 44.31, "payment_status": "Paid"}, {"billing_id": "BILL-1E9F247B", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-4E4390D8", "billing_date": "2023-12-27", "base_amount": 40, "add_on_charges": 0, "taxes": 3.99, "total_amount": 43.99, "payment_status": "Paid"}, {"billing_id": "BILL-4757A92C", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-01-01", "base_amount": 40, "add_on_charges": 9.99, "taxes": 6.39, "total_amount": 66.38, "payment_status": "Paid", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-2BBE5F03", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-01-31", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.21, "total_amount": 55.2, "payment_status": "Paid"}, {"billing_id": "BILL-67B81664", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-03-01", "base_amount": 40, "add_on_charges": 9.99, "taxes": 4.18, "total_amount": 54.17, "payment_status": "Paid"}, {"billing_id": "BILL-301B6AE9", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-03-31", "base_amount": 40, "add_on_charges": 9.99, "taxes": 4.26, "total_amount": 54.25, "payment_status": "Paid"}, {"billing_id": "BILL-55EE6BE6", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-04-30", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.44, "total_amount": 55.43, "payment_status": "Paid"}, {"billing_id": "BILL-9CD55069", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-05-30", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.36, "total_amount": 55.35, "payment_status": "Paid"}, {"billing_id": "BILL-D0202FEC", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-06-29", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.13, "total_amount": 55.12, "payment_status": "Late"}, {"billing_id": "BILL-252A36AE", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-07-29", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.65, "total_amount": 55.64, "payment_status": "Paid"}, {"billing_id": "BILL-0BA2E9B0", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-08-28", "base_amount": 40, "add_on_charges": 9.99, "taxes": 6.18, "total_amount": 71.17, "payment_status": "Late", "one_time_charges": 15, "one_time_description": "Device upgrade fee"}, {"billing_id": "BILL-1A7B5A3D", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-09-27", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.62, "total_amount": 55.61, "payment_status": "Paid"}, {"billing_id": "BILL-22AFCBC5", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-10-27", "base_amount": 40, "add_on_charges": 9.99, "taxes": 4.97, "total_amount": 54.96, "payment_status": "Paid"}, {"billing_id": "BILL-6044AD49", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-11-26", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.27, "total_amount": 55.26, "payment_status": "Paid"}, {"billing_id": "BILL-1034D8D3", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2024-12-26", "base_amount": 40, "add_on_charges": 9.99, "taxes": 4.01, "total_amount": 54.0, "payment_status": "Paid"}, {"billing_id": "BILL-7E2ED3B7", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2025-01-25", "base_amount": 40, "add_on_charges": 9.99, "taxes": 4.94, "total_amount": 54.93, "payment_status": "Paid"}, {"billing_id": "BILL-DE0A4A78", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2025-02-24", "base_amount": 40, "add_on_charges": 9.99, "taxes": 5.91, "total_amount": 55.9, "payment_status": "Paid"}, {"billing_id": "BILL-0EDF7D90", "customer_id": "CUST-5AB40347", "plan_id": "PLAN-D776803F", "billing_date": "2025-03-26", "base_amount": 40, "add_on_charges": 9.99, "taxes": 4.2, "total_amount": 54.19, "payment_status": "Paid"}]