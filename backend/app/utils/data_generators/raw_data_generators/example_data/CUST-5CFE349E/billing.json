[{"billing_id": "BILL-0086A9F3", "customer_id": "CUST-5CFE349E", "plan_id": "PLAN-0DDB920D", "billing_date": "2022-04-01", "base_amount": 230, "add_on_charges": 28.97, "taxes": 24.03, "total_amount": 237.0, "payment_status": "Paid", "discount_amount": 46.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-B12A6572", "customer_id": "CUST-5CFE349E", "plan_id": "PLAN-0DDB920D", "billing_date": "2022-05-01", "base_amount": 230, "add_on_charges": 28.97, "taxes": 24.37, "total_amount": 237.34, "payment_status": "Paid", "discount_amount": 46.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-7CFD512B", "customer_id": "CUST-5CFE349E", "plan_id": "PLAN-0DDB920D", "billing_date": "2022-05-31", "base_amount": 230, "add_on_charges": 28.97, "taxes": 19.04, "total_amount": 232.01, "payment_status": "Paid", "discount_amount": 46.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-DC4FC89C", "customer_id": "CUST-5CFE349E", "plan_id": "PLAN-0DDB920D", "billing_date": "2022-06-30", "base_amount": 230, "add_on_charges": 28.97, "taxes": 20.4, "total_amount": 233.37, "payment_status": "Paid", "discount_amount": 46.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-EBDBF7E9", "customer_id": "CUST-5CFE349E", "plan_id": "PLAN-0DDB920D", "billing_date": "2022-07-30", "base_amount": 230, "add_on_charges": 28.97, "taxes": 20.3, "total_amount": 233.27, "payment_status": "Paid", "discount_amount": 46.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-8A86F7D9", "customer_id": "CUST-5CFE349E", "plan_id": "PLAN-0DDB920D", "billing_date": "2022-08-29", "base_amount": 230, "add_on_charges": 28.97, "taxes": 21.68, "total_amount": 234.65, "payment_status": "Paid", "discount_amount": 46.0, "discount_reason": "New Customer"}]