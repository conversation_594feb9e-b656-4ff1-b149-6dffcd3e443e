[{"billing_id": "BILL-6720E8A8", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-01-01", "base_amount": 90, "add_on_charges": 9.99, "taxes": 9.58, "total_amount": 91.57, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-53CA97EE", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-01-31", "base_amount": 90, "add_on_charges": 9.99, "taxes": 7.96, "total_amount": 89.95, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-56303B09", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-03-02", "base_amount": 90, "add_on_charges": 9.99, "taxes": 8.69, "total_amount": 90.68, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-12D923F7", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-04-01", "base_amount": 90, "add_on_charges": 9.99, "taxes": 7.27, "total_amount": 89.26, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-69B40D0C", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-05-01", "base_amount": 90, "add_on_charges": 9.99, "taxes": 7.88, "total_amount": 89.87, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-3EA9F5BC", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-05-31", "base_amount": 90, "add_on_charges": 9.99, "taxes": 8.16, "total_amount": 90.15, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-E97C3BE4", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-06-30", "base_amount": 90, "add_on_charges": 9.99, "taxes": 9.19, "total_amount": 91.18, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-2A01CAF7", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-07-30", "base_amount": 90, "add_on_charges": 9.99, "taxes": 8.68, "total_amount": 108.67, "payment_status": "Paid"}, {"billing_id": "BILL-F7C061C3", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-08-29", "base_amount": 90, "add_on_charges": 9.99, "taxes": 9.99, "total_amount": 109.98, "payment_status": "Paid"}, {"billing_id": "BILL-D783A2CE", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-09-28", "base_amount": 90, "add_on_charges": 9.99, "taxes": 10.28, "total_amount": 110.27, "payment_status": "Paid"}, {"billing_id": "BILL-6AFAB263", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-10-28", "base_amount": 90, "add_on_charges": 9.99, "taxes": 10.05, "total_amount": 110.04, "payment_status": "Paid"}, {"billing_id": "BILL-B33F3C41", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-11-27", "base_amount": 90, "add_on_charges": 9.99, "taxes": 8.11, "total_amount": 108.1, "payment_status": "Paid"}, {"billing_id": "BILL-5E1AF143", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2022-12-27", "base_amount": 90, "add_on_charges": 9.99, "taxes": 8.35, "total_amount": 108.34, "payment_status": "Unpaid"}, {"billing_id": "BILL-8A003BD2", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-01-26", "base_amount": 90, "add_on_charges": 9.99, "taxes": 11.09, "total_amount": 111.08, "payment_status": "Paid"}, {"billing_id": "BILL-14A2CB4A", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-02-25", "base_amount": 90, "add_on_charges": 9.99, "taxes": 9.08, "total_amount": 109.07, "payment_status": "Paid"}, {"billing_id": "BILL-3D6F2DD7", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-03-27", "base_amount": 90, "add_on_charges": 9.99, "taxes": 8.36, "total_amount": 108.35, "payment_status": "Paid"}, {"billing_id": "BILL-6BF1BD16", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-04-26", "base_amount": 90, "add_on_charges": 9.99, "taxes": 11.87, "total_amount": 111.86, "payment_status": "Paid"}, {"billing_id": "BILL-89B4429A", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-05-26", "base_amount": 90, "add_on_charges": 9.99, "taxes": 11.73, "total_amount": 111.72, "payment_status": "Paid"}, {"billing_id": "BILL-BFA30A3B", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-06-25", "base_amount": 90, "add_on_charges": 9.99, "taxes": 11.95, "total_amount": 111.94, "payment_status": "Paid"}, {"billing_id": "BILL-8A29E00D", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-07-25", "base_amount": 90, "add_on_charges": 9.99, "taxes": 9.13, "total_amount": 109.12, "payment_status": "Paid"}, {"billing_id": "BILL-84D7BC56", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-08-24", "base_amount": 90, "add_on_charges": 9.99, "taxes": 9.01, "total_amount": 109.0, "payment_status": "Paid"}, {"billing_id": "BILL-65C25781", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-E6CAFA17", "billing_date": "2023-09-23", "base_amount": 90, "add_on_charges": 9.99, "taxes": 10.07, "total_amount": 110.06, "payment_status": "Paid"}, {"billing_id": "BILL-00095A30", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2023-10-01", "base_amount": 110, "add_on_charges": 0, "taxes": 9.1, "total_amount": 108.1, "payment_status": "Paid", "discount_amount": 11.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-BBC256F7", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2023-10-31", "base_amount": 110, "add_on_charges": 0, "taxes": 9.33, "total_amount": 108.33, "payment_status": "Late", "discount_amount": 11.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-73C3BC87", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2023-11-30", "base_amount": 110, "add_on_charges": 0, "taxes": 8.43, "total_amount": 107.43, "payment_status": "Paid", "discount_amount": 11.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-4B2F768D", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2023-12-30", "base_amount": 110, "add_on_charges": 0, "taxes": 11.72, "total_amount": 110.72, "payment_status": "Paid", "discount_amount": 11.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-76809EBA", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2024-01-29", "base_amount": 110, "add_on_charges": 0, "taxes": 10.81, "total_amount": 120.81, "payment_status": "Paid"}, {"billing_id": "BILL-6855BE93", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2024-02-28", "base_amount": 110, "add_on_charges": 0, "taxes": 10.84, "total_amount": 120.84, "payment_status": "Paid"}, {"billing_id": "BILL-0949F1FE", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2024-03-29", "base_amount": 110, "add_on_charges": 0, "taxes": 9.94, "total_amount": 119.94, "payment_status": "Late"}, {"billing_id": "BILL-507E9596", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2024-04-28", "base_amount": 110, "add_on_charges": 0, "taxes": 10.92, "total_amount": 120.92, "payment_status": "Paid"}, {"billing_id": "BILL-CFDFE672", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2024-05-28", "base_amount": 110, "add_on_charges": 0, "taxes": 11.62, "total_amount": 121.62, "payment_status": "Paid"}, {"billing_id": "BILL-DF930F16", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-27C7A092", "billing_date": "2024-06-27", "base_amount": 110, "add_on_charges": 0, "taxes": 11.52, "total_amount": 121.52, "payment_status": "Paid"}, {"billing_id": "BILL-FFCBEBBC", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2024-07-01", "base_amount": 90, "add_on_charges": 0, "taxes": 8.98, "total_amount": 98.98, "payment_status": "Paid"}, {"billing_id": "BILL-5C201019", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2024-07-31", "base_amount": 90, "add_on_charges": 0, "taxes": 8.33, "total_amount": 98.33, "payment_status": "Paid"}, {"billing_id": "BILL-A46E731F", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2024-08-30", "base_amount": 90, "add_on_charges": 0, "taxes": 8.29, "total_amount": 98.29, "payment_status": "Paid"}, {"billing_id": "BILL-BE649BB5", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2024-09-29", "base_amount": 90, "add_on_charges": 0, "taxes": 7.75, "total_amount": 97.75, "payment_status": "Paid"}, {"billing_id": "BILL-032FAD52", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2024-10-29", "base_amount": 90, "add_on_charges": 0, "taxes": 8.88, "total_amount": 98.88, "payment_status": "Paid"}, {"billing_id": "BILL-3EBC9E8C", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2024-11-28", "base_amount": 90, "add_on_charges": 0, "taxes": 8.71, "total_amount": 98.71, "payment_status": "Paid"}, {"billing_id": "BILL-83C29547", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2024-12-28", "base_amount": 90, "add_on_charges": 0, "taxes": 9.04, "total_amount": 99.04, "payment_status": "Paid"}, {"billing_id": "BILL-F79D12B6", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2025-01-27", "base_amount": 90, "add_on_charges": 0, "taxes": 7.82, "total_amount": 97.82, "payment_status": "Paid"}, {"billing_id": "BILL-5FF3BD76", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2025-02-26", "base_amount": 90, "add_on_charges": 0, "taxes": 9.54, "total_amount": 99.54, "payment_status": "Paid"}, {"billing_id": "BILL-79D85BC3", "customer_id": "CUST-04B8D721", "plan_id": "PLAN-74CB42DB", "billing_date": "2025-03-28", "base_amount": 90, "add_on_charges": 0, "taxes": 10.45, "total_amount": 100.45, "payment_status": "Paid"}]