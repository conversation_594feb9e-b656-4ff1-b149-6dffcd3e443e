[{"billing_id": "BILL-6CFF472B", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2022-10-01", "base_amount": 100, "add_on_charges": 0, "taxes": 6.64, "total_amount": 86.64, "payment_status": "Paid", "discount_amount": 20.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-AE4245DE", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2022-10-31", "base_amount": 100, "add_on_charges": 0, "taxes": 9.14, "total_amount": 94.14, "payment_status": "Paid", "discount_amount": 20.0, "discount_reason": "New Customer", "one_time_charges": 5, "one_time_description": "Paper bill fee"}, {"billing_id": "BILL-36DD77F1", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2022-11-30", "base_amount": 100, "add_on_charges": 0, "taxes": 6.5, "total_amount": 86.5, "payment_status": "Paid", "discount_amount": 20.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-DD89F19C", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2022-12-30", "base_amount": 100, "add_on_charges": 0, "taxes": 7.3, "total_amount": 87.3, "payment_status": "Paid", "discount_amount": 20.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-E6C7EAA8", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2023-01-29", "base_amount": 100, "add_on_charges": 0, "taxes": 6.63, "total_amount": 86.63, "payment_status": "Paid", "discount_amount": 20.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-DB0D7A86", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2023-02-28", "base_amount": 100, "add_on_charges": 0, "taxes": 7.25, "total_amount": 87.25, "payment_status": "Paid", "discount_amount": 20.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-621C4F68", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2023-03-30", "base_amount": 100, "add_on_charges": 0, "taxes": 8.29, "total_amount": 88.29, "payment_status": "Paid", "discount_amount": 20.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-C28946BD", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2023-04-29", "base_amount": 100, "add_on_charges": 0, "taxes": 8.79, "total_amount": 108.79, "payment_status": "Paid"}, {"billing_id": "BILL-7B26DC8D", "customer_id": "CUST-FE97915A", "plan_id": "PLAN-F76AD312", "billing_date": "2023-05-29", "base_amount": 100, "add_on_charges": 0, "taxes": 8.9, "total_amount": 108.9, "payment_status": "Paid"}]