[{"billing_id": "BILL-75DEC541", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-04-01", "base_amount": 170, "add_on_charges": 16.98, "taxes": 16.79, "total_amount": 169.77, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-4F5400AA", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-05-01", "base_amount": 170, "add_on_charges": 16.98, "taxes": 18.27, "total_amount": 171.25, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-97025896", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-05-31", "base_amount": 170, "add_on_charges": 16.98, "taxes": 14.11, "total_amount": 167.09, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-B884D209", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-06-30", "base_amount": 170, "add_on_charges": 16.98, "taxes": 14.71, "total_amount": 167.69, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-66676374", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-07-30", "base_amount": 170, "add_on_charges": 16.98, "taxes": 14.83, "total_amount": 187.81, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer", "one_time_charges": 20, "one_time_description": "International usage"}, {"billing_id": "BILL-7C30171A", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-08-29", "base_amount": 170, "add_on_charges": 16.98, "taxes": 16.31, "total_amount": 169.29, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-483DBE5B", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-09-28", "base_amount": 170, "add_on_charges": 16.98, "taxes": 13.44, "total_amount": 166.42, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-6BE6E5D3", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-10-28", "base_amount": 170, "add_on_charges": 16.98, "taxes": 16.61, "total_amount": 203.59, "payment_status": "Paid"}, {"billing_id": "BILL-64C3E0F9", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-96C69969", "billing_date": "2024-11-27", "base_amount": 170, "add_on_charges": 16.98, "taxes": 22.23, "total_amount": 224.21, "payment_status": "Paid", "one_time_charges": 15, "one_time_description": "Device upgrade fee"}, {"billing_id": "BILL-AD74B7A6", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-E6B30CA5", "billing_date": "2024-12-01", "base_amount": 125, "add_on_charges": 0, "taxes": 12.5, "total_amount": 137.5, "payment_status": "Paid"}, {"billing_id": "BILL-E91F0F4C", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-E6B30CA5", "billing_date": "2024-12-31", "base_amount": 125, "add_on_charges": 0, "taxes": 10.43, "total_amount": 135.43, "payment_status": "Paid"}, {"billing_id": "BILL-4995C044", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-E6B30CA5", "billing_date": "2025-01-30", "base_amount": 125, "add_on_charges": 0, "taxes": 13.64, "total_amount": 138.64, "payment_status": "Paid"}, {"billing_id": "BILL-BFF28211", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-E6B30CA5", "billing_date": "2025-03-01", "base_amount": 125, "add_on_charges": 0, "taxes": 14.54, "total_amount": 139.54, "payment_status": "Paid"}, {"billing_id": "BILL-6CAADDE8", "customer_id": "CUST-5C001D6D", "plan_id": "PLAN-E6B30CA5", "billing_date": "2025-03-31", "base_amount": 125, "add_on_charges": 0, "taxes": 12.92, "total_amount": 137.92, "payment_status": "Paid"}]