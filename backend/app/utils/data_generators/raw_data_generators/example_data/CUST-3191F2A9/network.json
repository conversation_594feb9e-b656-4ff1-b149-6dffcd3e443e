[{"network_id": "NET-E5E4C2F5", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2022-10-01", "data_usage_gb": 4.98, "call_minutes": 1229, "text_messages": 589, "dropped_calls": 13, "avg_speed_mbps": 32.9, "num_lines": 2}, {"network_id": "NET-A99CD829", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2022-10-31", "data_usage_gb": 4.75, "call_minutes": 1319, "text_messages": 677, "dropped_calls": 15, "avg_speed_mbps": 53.3, "num_lines": 2}, {"network_id": "NET-743EB41D", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2022-11-30", "data_usage_gb": 4.65, "call_minutes": 1046, "text_messages": 700, "dropped_calls": 8, "avg_speed_mbps": 29.9, "num_lines": 2}, {"network_id": "NET-DB3E1B45", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2022-12-30", "data_usage_gb": 11.88, "call_minutes": 1018, "text_messages": 569, "dropped_calls": 11, "avg_speed_mbps": 42.1, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 6.88, "data_overage_charges": 103.2}, {"network_id": "NET-7C7F7DC3", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-01-29", "data_usage_gb": 4.56, "call_minutes": 1190, "text_messages": 630, "dropped_calls": 10, "avg_speed_mbps": 53.2, "num_lines": 2}, {"network_id": "NET-1E5A6F8D", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-02-28", "data_usage_gb": 4.18, "call_minutes": 1414, "text_messages": 719, "dropped_calls": 14, "avg_speed_mbps": 42.5, "num_lines": 2}, {"network_id": "NET-859C4FF8", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-03-30", "data_usage_gb": 4.7, "call_minutes": 1317, "text_messages": 611, "dropped_calls": 15, "avg_speed_mbps": 41.9, "num_lines": 2}, {"network_id": "NET-6B4F7AAE", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-04-29", "data_usage_gb": 11.65, "call_minutes": 1151, "text_messages": 484, "dropped_calls": 10, "avg_speed_mbps": 47.8, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 6.65, "data_overage_charges": 99.75}, {"network_id": "NET-523B8F54", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-05-29", "data_usage_gb": 13.77, "call_minutes": 1033, "text_messages": 551, "dropped_calls": 12, "avg_speed_mbps": 54.7, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 8.77, "data_overage_charges": 131.55}, {"network_id": "NET-3D601AB5", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-06-28", "data_usage_gb": 4.04, "call_minutes": 1006, "text_messages": 568, "dropped_calls": 12, "avg_speed_mbps": 48.9, "num_lines": 2}, {"network_id": "NET-9017E05D", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-07-28", "data_usage_gb": 3.64, "call_minutes": 1162, "text_messages": 568, "dropped_calls": 14, "avg_speed_mbps": 34.4, "num_lines": 2}, {"network_id": "NET-6654E707", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-08-27", "data_usage_gb": 4.47, "call_minutes": 1302, "text_messages": 703, "dropped_calls": 12, "avg_speed_mbps": 33.9, "num_lines": 2}, {"network_id": "NET-CE82D3C4", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-09-26", "data_usage_gb": 10.81, "call_minutes": 1104, "text_messages": 716, "dropped_calls": 8, "avg_speed_mbps": 46.0, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 5.81, "data_overage_charges": 87.15}, {"network_id": "NET-D97101D4", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-0DCAE2FA", "month_start_date": "2023-10-26", "data_usage_gb": 4.33, "call_minutes": 1400, "text_messages": 603, "dropped_calls": 13, "avg_speed_mbps": 48.3, "num_lines": 2}, {"network_id": "NET-5186C16D", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2023-11-01", "data_usage_gb": 14.15, "call_minutes": 1130, "text_messages": 620, "dropped_calls": 10, "avg_speed_mbps": 40.3, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 9.15, "data_overage_charges": 137.25}, {"network_id": "NET-92615770", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2023-12-01", "data_usage_gb": 12.81, "call_minutes": 1364, "text_messages": 490, "dropped_calls": 9, "avg_speed_mbps": 54.1, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 7.81, "data_overage_charges": 117.15}, {"network_id": "NET-8F268959", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2023-12-31", "data_usage_gb": 14.17, "call_minutes": 1175, "text_messages": 673, "dropped_calls": 10, "avg_speed_mbps": 58.4, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 9.17, "data_overage_charges": 137.55}, {"network_id": "NET-F1FAB7CC", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2024-01-30", "data_usage_gb": 4.97, "call_minutes": 1084, "text_messages": 641, "dropped_calls": 10, "avg_speed_mbps": 40.4, "num_lines": 2}, {"network_id": "NET-021C5C5E", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2024-02-29", "data_usage_gb": 4.19, "call_minutes": 1154, "text_messages": 637, "dropped_calls": 13, "avg_speed_mbps": 54.5, "num_lines": 2}, {"network_id": "NET-3BB511F4", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2024-03-30", "data_usage_gb": 4.54, "call_minutes": 1422, "text_messages": 642, "dropped_calls": 16, "avg_speed_mbps": 23.3, "num_lines": 2}, {"network_id": "NET-71B50640", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2024-04-29", "data_usage_gb": 3.78, "call_minutes": 972, "text_messages": 595, "dropped_calls": 12, "avg_speed_mbps": 44.8, "num_lines": 2}, {"network_id": "NET-B7B319A3", "customer_id": "CUST-3191F2A9", "plan_id": "PLAN-FF6D7660", "month_start_date": "2024-05-29", "data_usage_gb": 3.63, "call_minutes": 1329, "text_messages": 515, "dropped_calls": 16, "avg_speed_mbps": 42.3, "num_lines": 2}]