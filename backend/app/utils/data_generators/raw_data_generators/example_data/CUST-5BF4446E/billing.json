[{"billing_id": "BILL-8521A11A", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2024-08-01", "base_amount": 130, "add_on_charges": 0, "taxes": 11.69, "total_amount": 115.69, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-34D4F15F", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2024-08-31", "base_amount": 130, "add_on_charges": 0, "taxes": 9.55, "total_amount": 113.55, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-53D58D65", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2024-09-30", "base_amount": 130, "add_on_charges": 0, "taxes": 12.2, "total_amount": 141.2, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer", "one_time_charges": 25, "one_time_description": "Activation fee"}, {"billing_id": "BILL-5FFC7324", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2024-10-30", "base_amount": 130, "add_on_charges": 0, "taxes": 12.14, "total_amount": 116.14, "payment_status": "Late", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-288A6E2A", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2024-11-29", "base_amount": 130, "add_on_charges": 0, "taxes": 9.48, "total_amount": 113.48, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-597C13A5", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2024-12-29", "base_amount": 130, "add_on_charges": 0, "taxes": 10.0, "total_amount": 114.0, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-2222241E", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2025-01-28", "base_amount": 130, "add_on_charges": 0, "taxes": 10.02, "total_amount": 114.02, "payment_status": "Paid", "discount_amount": 26.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A641BC18", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2025-02-27", "base_amount": 130, "add_on_charges": 0, "taxes": 15.07, "total_amount": 145.07, "payment_status": "Paid"}, {"billing_id": "BILL-50D4F4A2", "customer_id": "CUST-5BF4446E", "plan_id": "PLAN-6EBCBAD7", "billing_date": "2025-03-29", "base_amount": 130, "add_on_charges": 0, "taxes": 14.05, "total_amount": 144.05, "payment_status": "Paid"}]