[{"network_id": "NET-97DD73A9", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-01-01", "data_usage_gb": 3.76, "call_minutes": 1122, "text_messages": 654, "dropped_calls": 42, "avg_speed_mbps": 109.3, "num_lines": 3}, {"network_id": "NET-0C2BA479", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-01-31", "data_usage_gb": 4.84, "call_minutes": 1254, "text_messages": 699, "dropped_calls": 8, "avg_speed_mbps": 88.7, "num_lines": 3}, {"network_id": "NET-955B8AB8", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-03-02", "data_usage_gb": 3.53, "call_minutes": 1175, "text_messages": 504, "dropped_calls": 11, "avg_speed_mbps": 95.3, "num_lines": 3}, {"network_id": "NET-835278CC", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-04-01", "data_usage_gb": 16.31, "call_minutes": 999, "text_messages": 646, "dropped_calls": 19, "avg_speed_mbps": 81.6, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 11.31, "data_overage_charges": 169.65}, {"network_id": "NET-DBF6C23C", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-05-01", "data_usage_gb": 17.24, "call_minutes": 972, "text_messages": 538, "dropped_calls": 8, "avg_speed_mbps": 109.5, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 12.24, "data_overage_charges": 183.6}, {"network_id": "NET-599B0404", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-05-31", "data_usage_gb": 16.63, "call_minutes": 1161, "text_messages": 654, "dropped_calls": 13, "avg_speed_mbps": 65.9, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 11.63, "data_overage_charges": 174.45}, {"network_id": "NET-5A6F4888", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-06-30", "data_usage_gb": 4.0, "call_minutes": 960, "text_messages": 551, "dropped_calls": 5, "avg_speed_mbps": 105.9, "num_lines": 3}, {"network_id": "NET-6B769B6C", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-07-30", "data_usage_gb": 12.97, "call_minutes": 1044, "text_messages": 535, "dropped_calls": 10, "avg_speed_mbps": 110.7, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 7.97, "data_overage_charges": 119.55}, {"network_id": "NET-E1679BD2", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-08-29", "data_usage_gb": 4.35, "call_minutes": 1218, "text_messages": 681, "dropped_calls": 13, "avg_speed_mbps": 115.8, "num_lines": 3}, {"network_id": "NET-D27945A4", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "month_start_date": "2023-09-28", "data_usage_gb": 16.25, "call_minutes": 953, "text_messages": 670, "dropped_calls": 6, "avg_speed_mbps": 106.6, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 11.25, "data_overage_charges": 168.75}, {"network_id": "NET-B1D2E283", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "month_start_date": "2023-10-01", "data_usage_gb": 4.29, "call_minutes": 880, "text_messages": 582, "dropped_calls": 10, "avg_speed_mbps": 83.0, "num_lines": 3}, {"network_id": "NET-25862653", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "month_start_date": "2023-10-31", "data_usage_gb": 17.56, "call_minutes": 1108, "text_messages": 577, "dropped_calls": 4, "avg_speed_mbps": 69.2, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 12.56, "data_overage_charges": 188.4}, {"network_id": "NET-2979E5DB", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "month_start_date": "2023-11-30", "data_usage_gb": 3.75, "call_minutes": 1243, "text_messages": 556, "dropped_calls": 10, "avg_speed_mbps": 70.1, "num_lines": 3}, {"network_id": "NET-6B200487", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "month_start_date": "2023-12-30", "data_usage_gb": 3.75, "call_minutes": 992, "text_messages": 621, "dropped_calls": 11, "avg_speed_mbps": 67.2, "num_lines": 3}, {"network_id": "NET-53C9AC78", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "month_start_date": "2024-01-29", "data_usage_gb": 4.21, "call_minutes": 896, "text_messages": 687, "dropped_calls": 11, "avg_speed_mbps": 69.5, "num_lines": 3}]