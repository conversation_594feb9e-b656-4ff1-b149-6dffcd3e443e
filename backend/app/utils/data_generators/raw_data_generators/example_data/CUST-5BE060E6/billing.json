[{"billing_id": "BILL-D7286C3A", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-01-01", "base_amount": 170, "add_on_charges": 0, "taxes": 13.78, "total_amount": 149.78, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-E613DA37", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-01-31", "base_amount": 170, "add_on_charges": 0, "taxes": 16.32, "total_amount": 152.32, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-372867CF", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-03-02", "base_amount": 170, "add_on_charges": 0, "taxes": 12.78, "total_amount": 148.78, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-F3626A33", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-04-01", "base_amount": 170, "add_on_charges": 0, "taxes": 14.69, "total_amount": 150.69, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-4F5F9C9E", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-05-01", "base_amount": 170, "add_on_charges": 0, "taxes": 12.72, "total_amount": 148.72, "payment_status": "Unpaid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-470503D7", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-05-31", "base_amount": 170, "add_on_charges": 0, "taxes": 16.19, "total_amount": 152.19, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-A4861E2A", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-06-30", "base_amount": 170, "add_on_charges": 0, "taxes": 12.43, "total_amount": 148.43, "payment_status": "Paid", "discount_amount": 34.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-4D41AE5D", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-07-30", "base_amount": 170, "add_on_charges": 0, "taxes": 20.27, "total_amount": 190.27, "payment_status": "Paid"}, {"billing_id": "BILL-142D30A2", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-08-29", "base_amount": 170, "add_on_charges": 0, "taxes": 16.59, "total_amount": 186.59, "payment_status": "Paid"}, {"billing_id": "BILL-4D813907", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-5425AC12", "billing_date": "2023-09-28", "base_amount": 170, "add_on_charges": 0, "taxes": 17.83, "total_amount": 187.83, "payment_status": "Paid"}, {"billing_id": "BILL-DBBB1AA5", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "billing_date": "2023-10-01", "base_amount": 125, "add_on_charges": 0, "taxes": 12.05, "total_amount": 137.05, "payment_status": "Paid"}, {"billing_id": "BILL-60A2D57D", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "billing_date": "2023-10-31", "base_amount": 125, "add_on_charges": 0, "taxes": 14.05, "total_amount": 139.05, "payment_status": "Paid"}, {"billing_id": "BILL-C772EAA2", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "billing_date": "2023-11-30", "base_amount": 125, "add_on_charges": 0, "taxes": 16.1, "total_amount": 151.1, "payment_status": "Paid", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-922D29F5", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "billing_date": "2023-12-30", "base_amount": 125, "add_on_charges": 0, "taxes": 12.71, "total_amount": 137.71, "payment_status": "Paid"}, {"billing_id": "BILL-394744DE", "customer_id": "CUST-5BE060E6", "plan_id": "PLAN-CAE83869", "billing_date": "2024-01-29", "base_amount": 125, "add_on_charges": 0, "taxes": 10.35, "total_amount": 135.35, "payment_status": "Paid"}]