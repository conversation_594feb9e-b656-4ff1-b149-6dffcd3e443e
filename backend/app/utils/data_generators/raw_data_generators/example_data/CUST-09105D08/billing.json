[{"billing_id": "BILL-8C961D6D", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2024-07-01", "base_amount": 90, "add_on_charges": 22.98, "taxes": 10.61, "total_amount": 123.59, "payment_status": "Paid"}, {"billing_id": "BILL-62B83E6D", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2024-07-31", "base_amount": 90, "add_on_charges": 22.98, "taxes": 11.7, "total_amount": 124.68, "payment_status": "Paid"}, {"billing_id": "BILL-FEAD2CAC", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2024-08-30", "base_amount": 90, "add_on_charges": 22.98, "taxes": 12.5, "total_amount": 125.48, "payment_status": "Paid"}, {"billing_id": "BILL-398C9566", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2024-09-29", "base_amount": 90, "add_on_charges": 22.98, "taxes": 13.02, "total_amount": 126.0, "payment_status": "Paid"}, {"billing_id": "BILL-818BD0AE", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2024-10-29", "base_amount": 90, "add_on_charges": 22.98, "taxes": 12.52, "total_amount": 125.5, "payment_status": "Paid"}, {"billing_id": "BILL-7CA7B4B5", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2024-11-28", "base_amount": 90, "add_on_charges": 22.98, "taxes": 11.39, "total_amount": 124.37, "payment_status": "Paid"}, {"billing_id": "BILL-C90AB065", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2024-12-28", "base_amount": 90, "add_on_charges": 22.98, "taxes": 9.77, "total_amount": 122.75, "payment_status": "Paid"}, {"billing_id": "BILL-D85D8438", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2025-01-27", "base_amount": 90, "add_on_charges": 22.98, "taxes": 12.32, "total_amount": 125.3, "payment_status": "Paid"}, {"billing_id": "BILL-1DF78049", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2025-02-26", "base_amount": 90, "add_on_charges": 22.98, "taxes": 9.29, "total_amount": 122.27, "payment_status": "Paid"}, {"billing_id": "BILL-821DFB2D", "customer_id": "CUST-09105D08", "plan_id": "PLAN-11C6AA7E", "billing_date": "2025-03-28", "base_amount": 90, "add_on_charges": 22.98, "taxes": 12.47, "total_amount": 125.45, "payment_status": "Paid"}]