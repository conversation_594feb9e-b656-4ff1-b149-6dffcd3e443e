[{"billing_id": "BILL-3BF7D6CE", "customer_id": "CUST-CB7DE98E", "plan_id": "PLAN-C7BB3F18", "billing_date": "2024-10-01", "base_amount": 60, "add_on_charges": 0, "taxes": 4.24, "total_amount": 52.24, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-0674DE75", "customer_id": "CUST-CB7DE98E", "plan_id": "PLAN-C7BB3F18", "billing_date": "2024-10-31", "base_amount": 60, "add_on_charges": 0, "taxes": 4.66, "total_amount": 52.66, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-901B06A9", "customer_id": "CUST-CB7DE98E", "plan_id": "PLAN-C7BB3F18", "billing_date": "2024-11-30", "base_amount": 60, "add_on_charges": 0, "taxes": 4.05, "total_amount": 52.05, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-C3684DD0", "customer_id": "CUST-CB7DE98E", "plan_id": "PLAN-C7BB3F18", "billing_date": "2024-12-30", "base_amount": 60, "add_on_charges": 0, "taxes": 5.85, "total_amount": 58.85, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer", "one_time_charges": 5, "one_time_description": "Paper bill fee"}, {"billing_id": "BILL-D88FB708", "customer_id": "CUST-CB7DE98E", "plan_id": "PLAN-C7BB3F18", "billing_date": "2025-01-29", "base_amount": 60, "add_on_charges": 0, "taxes": 4.72, "total_amount": 62.72, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-91B61DF4", "customer_id": "CUST-CB7DE98E", "plan_id": "PLAN-C7BB3F18", "billing_date": "2025-02-28", "base_amount": 60, "add_on_charges": 0, "taxes": 4.98, "total_amount": 52.98, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-791A057E", "customer_id": "CUST-CB7DE98E", "plan_id": "PLAN-C7BB3F18", "billing_date": "2025-03-30", "base_amount": 60, "add_on_charges": 0, "taxes": 5.58, "total_amount": 53.58, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}]