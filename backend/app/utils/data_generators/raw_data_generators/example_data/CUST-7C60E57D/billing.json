[{"billing_id": "BILL-A13ABF95", "customer_id": "CUST-7C60E57D", "plan_id": "PLAN-714DD3FE", "billing_date": "2022-09-01", "base_amount": 90, "add_on_charges": 7.99, "taxes": 6.84, "total_amount": 86.83, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5312A349", "customer_id": "CUST-7C60E57D", "plan_id": "PLAN-714DD3FE", "billing_date": "2022-10-01", "base_amount": 90, "add_on_charges": 7.99, "taxes": 8.54, "total_amount": 88.53, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-3BA34A35", "customer_id": "CUST-7C60E57D", "plan_id": "PLAN-714DD3FE", "billing_date": "2022-10-31", "base_amount": 90, "add_on_charges": 7.99, "taxes": 8.97, "total_amount": 88.96, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-281D3EE8", "customer_id": "CUST-7C60E57D", "plan_id": "PLAN-714DD3FE", "billing_date": "2022-11-30", "base_amount": 90, "add_on_charges": 7.99, "taxes": 7.11, "total_amount": 87.1, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-AE07E6BA", "customer_id": "CUST-7C60E57D", "plan_id": "PLAN-714DD3FE", "billing_date": "2022-12-30", "base_amount": 90, "add_on_charges": 7.99, "taxes": 6.9, "total_amount": 86.89, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}]