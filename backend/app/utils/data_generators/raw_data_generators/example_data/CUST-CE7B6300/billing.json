[{"billing_id": "BILL-D07D8925", "customer_id": "CUST-CE7B6300", "plan_id": "PLAN-7DF73B23", "billing_date": "2024-12-01", "base_amount": 90, "add_on_charges": 0, "taxes": 8.18, "total_amount": 80.18, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-7CED6A09", "customer_id": "CUST-CE7B6300", "plan_id": "PLAN-7DF73B23", "billing_date": "2024-12-31", "base_amount": 90, "add_on_charges": 0, "taxes": 7.07, "total_amount": 79.07, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-5E21AD69", "customer_id": "CUST-CE7B6300", "plan_id": "PLAN-7DF73B23", "billing_date": "2025-01-30", "base_amount": 90, "add_on_charges": 0, "taxes": 8.4, "total_amount": 80.4, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-86832D29", "customer_id": "CUST-CE7B6300", "plan_id": "PLAN-7DF73B23", "billing_date": "2025-03-01", "base_amount": 90, "add_on_charges": 0, "taxes": 7.45, "total_amount": 79.45, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-8811E4B6", "customer_id": "CUST-CE7B6300", "plan_id": "PLAN-7DF73B23", "billing_date": "2025-03-31", "base_amount": 90, "add_on_charges": 0, "taxes": 7.18, "total_amount": 79.18, "payment_status": "Paid", "discount_amount": 18.0, "discount_reason": "New Customer"}]