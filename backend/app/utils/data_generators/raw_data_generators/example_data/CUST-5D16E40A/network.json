[{"network_id": "NET-D4B0CEC0", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-05-01", "data_usage_gb": 4.64, "call_minutes": 302, "text_messages": 2504, "dropped_calls": 1, "avg_speed_mbps": 51.1, "num_lines": 3}, {"network_id": "NET-5758BCA3", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-05-31", "data_usage_gb": 4.33, "call_minutes": 373, "text_messages": 3341, "dropped_calls": 3, "avg_speed_mbps": 40.2, "num_lines": 3}, {"network_id": "NET-533FCD87", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-06-30", "data_usage_gb": 40.02, "call_minutes": 368, "text_messages": 3591, "dropped_calls": 5, "avg_speed_mbps": 50.4, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 35.02, "data_overage_charges": 525.3}, {"network_id": "NET-19861C1D", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-07-30", "data_usage_gb": 3.9, "call_minutes": 309, "text_messages": 2600, "dropped_calls": 6, "avg_speed_mbps": 46.0, "num_lines": 3}, {"network_id": "NET-26F31FBD", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-08-29", "data_usage_gb": 34.0, "call_minutes": 391, "text_messages": 2994, "dropped_calls": 4, "avg_speed_mbps": 24.7, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 29.0, "data_overage_charges": 435.0}, {"network_id": "NET-1F0242B3", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-09-28", "data_usage_gb": 34.5, "call_minutes": 342, "text_messages": 3434, "dropped_calls": 2, "avg_speed_mbps": 48.6, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 29.5, "data_overage_charges": 442.5}, {"network_id": "NET-6368101A", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-10-28", "data_usage_gb": 3.95, "call_minutes": 374, "text_messages": 3234, "dropped_calls": 6, "avg_speed_mbps": 43.6, "num_lines": 3}, {"network_id": "NET-B6217AAC", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-11-27", "data_usage_gb": 33.4, "call_minutes": 379, "text_messages": 3561, "dropped_calls": 6, "avg_speed_mbps": 46.5, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 28.4, "data_overage_charges": 426.0}, {"network_id": "NET-09D84655", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2022-12-27", "data_usage_gb": 4.9, "call_minutes": 316, "text_messages": 3596, "dropped_calls": 2, "avg_speed_mbps": 39.8, "num_lines": 3}, {"network_id": "NET-8AF05A26", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-01-26", "data_usage_gb": 4.7, "call_minutes": 407, "text_messages": 2482, "dropped_calls": 3, "avg_speed_mbps": 55.9, "num_lines": 3}, {"network_id": "NET-9BD2EDEE", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-02-25", "data_usage_gb": 3.88, "call_minutes": 310, "text_messages": 2907, "dropped_calls": 2, "avg_speed_mbps": 49.0, "num_lines": 3}, {"network_id": "NET-6EC0A19E", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-03-27", "data_usage_gb": 4.89, "call_minutes": 413, "text_messages": 2901, "dropped_calls": 15, "avg_speed_mbps": 59.4, "num_lines": 3}, {"network_id": "NET-BFCCA7E0", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-04-26", "data_usage_gb": 4.87, "call_minutes": 303, "text_messages": 2697, "dropped_calls": 5, "avg_speed_mbps": 43.3, "num_lines": 3}, {"network_id": "NET-CBE61AF0", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-05-26", "data_usage_gb": 4.47, "call_minutes": 427, "text_messages": 2797, "dropped_calls": 5, "avg_speed_mbps": 23.9, "num_lines": 3}, {"network_id": "NET-F025460A", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-06-25", "data_usage_gb": 4.53, "call_minutes": 319, "text_messages": 2848, "dropped_calls": 5, "avg_speed_mbps": 53.6, "num_lines": 3}, {"network_id": "NET-DE57471D", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-07-25", "data_usage_gb": 30.97, "call_minutes": 357, "text_messages": 2754, "dropped_calls": 3, "avg_speed_mbps": 41.7, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 25.97, "data_overage_charges": 389.55}, {"network_id": "NET-A48A0B7B", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-08-24", "data_usage_gb": 4.01, "call_minutes": 400, "text_messages": 3025, "dropped_calls": 2, "avg_speed_mbps": 42.0, "num_lines": 3}, {"network_id": "NET-DEA460FA", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-09-23", "data_usage_gb": 3.57, "call_minutes": 390, "text_messages": 2427, "dropped_calls": 1, "avg_speed_mbps": 42.2, "num_lines": 3}, {"network_id": "NET-1655A03B", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-EB7030F0", "month_start_date": "2023-10-23", "data_usage_gb": 42.19, "call_minutes": 362, "text_messages": 3062, "dropped_calls": 6, "avg_speed_mbps": 48.5, "num_lines": 3, "data_limit_exceeded": true, "data_overage_gb": 37.19, "data_overage_charges": 557.85}, {"network_id": "NET-F90505EA", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2023-11-01", "data_usage_gb": 3.78, "call_minutes": 127, "text_messages": 958, "dropped_calls": 0, "avg_speed_mbps": 57.7, "num_lines": 1}, {"network_id": "NET-49BC85AE", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2023-12-01", "data_usage_gb": 3.82, "call_minutes": 109, "text_messages": 1081, "dropped_calls": 1, "avg_speed_mbps": 51.5, "num_lines": 1}, {"network_id": "NET-D0201937", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2023-12-31", "data_usage_gb": 3.8, "call_minutes": 101, "text_messages": 1068, "dropped_calls": 2, "avg_speed_mbps": 59.3, "num_lines": 1}, {"network_id": "NET-DFDD1D4D", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-01-30", "data_usage_gb": 3.67, "call_minutes": 112, "text_messages": 983, "dropped_calls": 0, "avg_speed_mbps": 53.5, "num_lines": 1}, {"network_id": "NET-544B5D73", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-02-29", "data_usage_gb": 4.51, "call_minutes": 111, "text_messages": 895, "dropped_calls": 2, "avg_speed_mbps": 29.3, "num_lines": 1}, {"network_id": "NET-C6374FDB", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-03-30", "data_usage_gb": 4.13, "call_minutes": 119, "text_messages": 1014, "dropped_calls": 1, "avg_speed_mbps": 58.6, "num_lines": 1}, {"network_id": "NET-18BEFEE5", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-04-29", "data_usage_gb": 3.78, "call_minutes": 136, "text_messages": 1047, "dropped_calls": 2, "avg_speed_mbps": 59.0, "num_lines": 1}, {"network_id": "NET-AAD6C727", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-05-29", "data_usage_gb": 4.34, "call_minutes": 127, "text_messages": 963, "dropped_calls": 0, "avg_speed_mbps": 54.2, "num_lines": 1}, {"network_id": "NET-DA3D1A0A", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-06-28", "data_usage_gb": 4.69, "call_minutes": 106, "text_messages": 813, "dropped_calls": 0, "avg_speed_mbps": 45.8, "num_lines": 1}, {"network_id": "NET-68F76B3B", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-07-28", "data_usage_gb": 12.32, "call_minutes": 119, "text_messages": 1004, "dropped_calls": 1, "avg_speed_mbps": 45.4, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 7.32, "data_overage_charges": 109.8}, {"network_id": "NET-2C268EE4", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-08-27", "data_usage_gb": 4.64, "call_minutes": 132, "text_messages": 976, "dropped_calls": 1, "avg_speed_mbps": 48.5, "num_lines": 1}, {"network_id": "NET-46D9A70A", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-09-26", "data_usage_gb": 4.81, "call_minutes": 97, "text_messages": 905, "dropped_calls": 3, "avg_speed_mbps": 41.7, "num_lines": 1}, {"network_id": "NET-1132F82E", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-10-26", "data_usage_gb": 10.16, "call_minutes": 106, "text_messages": 1095, "dropped_calls": 0, "avg_speed_mbps": 57.2, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.16, "data_overage_charges": 77.4}, {"network_id": "NET-412957DB", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-11-25", "data_usage_gb": 4.68, "call_minutes": 135, "text_messages": 1122, "dropped_calls": 2, "avg_speed_mbps": 50.6, "num_lines": 1}, {"network_id": "NET-A0CC1035", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2024-12-25", "data_usage_gb": 10.41, "call_minutes": 110, "text_messages": 1029, "dropped_calls": 0, "avg_speed_mbps": 50.3, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 5.41, "data_overage_charges": 81.15}, {"network_id": "NET-319ED00F", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2025-01-24", "data_usage_gb": 4.63, "call_minutes": 115, "text_messages": 823, "dropped_calls": 2, "avg_speed_mbps": 53.1, "num_lines": 1}, {"network_id": "NET-F65F1A22", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2025-02-23", "data_usage_gb": 3.98, "call_minutes": 107, "text_messages": 1137, "dropped_calls": 1, "avg_speed_mbps": 42.8, "num_lines": 1}, {"network_id": "NET-3E0C12F7", "customer_id": "CUST-5D16E40A", "plan_id": "PLAN-56436799", "month_start_date": "2025-03-25", "data_usage_gb": 3.61, "call_minutes": 134, "text_messages": 1107, "dropped_calls": 3, "avg_speed_mbps": 55.0, "num_lines": 1}]