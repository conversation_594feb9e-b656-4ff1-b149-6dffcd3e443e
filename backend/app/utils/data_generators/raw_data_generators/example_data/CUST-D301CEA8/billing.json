[{"billing_id": "BILL-6AD0FC2D", "customer_id": "CUST-D301CEA8", "plan_id": "PLAN-1E3D8BF3", "billing_date": "2024-12-01", "base_amount": 60, "add_on_charges": 19.99, "taxes": 7.66, "total_amount": 75.65, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-147C362A", "customer_id": "CUST-D301CEA8", "plan_id": "PLAN-1E3D8BF3", "billing_date": "2024-12-31", "base_amount": 60, "add_on_charges": 19.99, "taxes": 6.57, "total_amount": 74.56, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-254A1F65", "customer_id": "CUST-D301CEA8", "plan_id": "PLAN-1E3D8BF3", "billing_date": "2025-01-30", "base_amount": 60, "add_on_charges": 19.99, "taxes": 6.04, "total_amount": 74.03, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-35B488D9", "customer_id": "CUST-D301CEA8", "plan_id": "PLAN-1E3D8BF3", "billing_date": "2025-03-01", "base_amount": 60, "add_on_charges": 19.99, "taxes": 7.56, "total_amount": 75.55, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-33EE0774", "customer_id": "CUST-D301CEA8", "plan_id": "PLAN-1E3D8BF3", "billing_date": "2025-03-31", "base_amount": 60, "add_on_charges": 19.99, "taxes": 5.92, "total_amount": 73.91, "payment_status": "Paid", "discount_amount": 12.0, "discount_reason": "New Customer"}]