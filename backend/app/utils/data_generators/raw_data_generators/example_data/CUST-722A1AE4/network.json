[{"network_id": "NET-BBEEE97D", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2023-10-01", "data_usage_gb": 4.92, "call_minutes": 269, "text_messages": 579, "dropped_calls": 3, "avg_speed_mbps": 62.9, "num_lines": 1}, {"network_id": "NET-B9BD2488", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2023-10-31", "data_usage_gb": 4.65, "call_minutes": 200, "text_messages": 598, "dropped_calls": 0, "avg_speed_mbps": 60.9, "num_lines": 1}, {"network_id": "NET-7CB9B6F8", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2023-11-30", "data_usage_gb": 4.97, "call_minutes": 259, "text_messages": 585, "dropped_calls": 5, "avg_speed_mbps": 78.5, "num_lines": 1}, {"network_id": "NET-714E5D76", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2023-12-30", "data_usage_gb": 6.62, "call_minutes": 263, "text_messages": 532, "dropped_calls": 3, "avg_speed_mbps": 86.1, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 1.62, "data_overage_charges": 24.3}, {"network_id": "NET-6F5D6252", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-01-29", "data_usage_gb": 3.68, "call_minutes": 211, "text_messages": 562, "dropped_calls": 1, "avg_speed_mbps": 64.6, "num_lines": 1}, {"network_id": "NET-60C31601", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-02-28", "data_usage_gb": 4.39, "call_minutes": 243, "text_messages": 528, "dropped_calls": 1, "avg_speed_mbps": 71.5, "num_lines": 1}, {"network_id": "NET-AB1ABF85", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-03-29", "data_usage_gb": 8.91, "call_minutes": 205, "text_messages": 581, "dropped_calls": 2, "avg_speed_mbps": 89.1, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.91, "data_overage_charges": 58.65}, {"network_id": "NET-7180E701", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-04-28", "data_usage_gb": 4.01, "call_minutes": 214, "text_messages": 514, "dropped_calls": 4, "avg_speed_mbps": 72.8, "num_lines": 1}, {"network_id": "NET-20C4B38A", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-05-28", "data_usage_gb": 3.89, "call_minutes": 278, "text_messages": 480, "dropped_calls": 3, "avg_speed_mbps": 77.1, "num_lines": 1}, {"network_id": "NET-D7F70CB1", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-06-27", "data_usage_gb": 8.89, "call_minutes": 285, "text_messages": 406, "dropped_calls": 2, "avg_speed_mbps": 71.1, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.89, "data_overage_charges": 58.35}, {"network_id": "NET-85E7B976", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-07-27", "data_usage_gb": 4.49, "call_minutes": 215, "text_messages": 587, "dropped_calls": 0, "avg_speed_mbps": 47.5, "num_lines": 1}, {"network_id": "NET-8E852A02", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-08-26", "data_usage_gb": 8.23, "call_minutes": 206, "text_messages": 552, "dropped_calls": 2, "avg_speed_mbps": 79.3, "num_lines": 1, "data_limit_exceeded": true, "data_overage_gb": 3.23, "data_overage_charges": 48.45}, {"network_id": "NET-D69FE8D3", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-09-25", "data_usage_gb": 3.87, "call_minutes": 282, "text_messages": 498, "dropped_calls": 6, "avg_speed_mbps": 64.9, "num_lines": 1}, {"network_id": "NET-2E7FEA3F", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-10-25", "data_usage_gb": 4.49, "call_minutes": 222, "text_messages": 480, "dropped_calls": 5, "avg_speed_mbps": 74.9, "num_lines": 1}, {"network_id": "NET-9B93D141", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-11-24", "data_usage_gb": 4.99, "call_minutes": 225, "text_messages": 445, "dropped_calls": 6, "avg_speed_mbps": 77.3, "num_lines": 1}, {"network_id": "NET-9B500BAE", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2024-12-24", "data_usage_gb": 4.0, "call_minutes": 251, "text_messages": 567, "dropped_calls": 1, "avg_speed_mbps": 62.1, "num_lines": 1}, {"network_id": "NET-51FD49EB", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2025-01-23", "data_usage_gb": 3.56, "call_minutes": 220, "text_messages": 436, "dropped_calls": 0, "avg_speed_mbps": 89.9, "num_lines": 1}, {"network_id": "NET-C715DF28", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2025-02-22", "data_usage_gb": 3.5, "call_minutes": 218, "text_messages": 486, "dropped_calls": 2, "avg_speed_mbps": 88.8, "num_lines": 1}, {"network_id": "NET-39DA2ABA", "customer_id": "CUST-722A1AE4", "plan_id": "PLAN-413BFF60", "month_start_date": "2025-03-24", "data_usage_gb": 4.98, "call_minutes": 280, "text_messages": 576, "dropped_calls": 4, "avg_speed_mbps": 74.1, "num_lines": 1}]