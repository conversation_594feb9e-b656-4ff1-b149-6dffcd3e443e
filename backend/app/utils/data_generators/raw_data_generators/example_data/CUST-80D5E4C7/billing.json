[{"billing_id": "BILL-B3F2D00F", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2022-10-01", "base_amount": 80, "add_on_charges": 0, "taxes": 8.53, "total_amount": 103.53, "payment_status": "Paid", "one_time_charges": 15, "one_time_description": "Device upgrade fee"}, {"billing_id": "BILL-37A56AC8", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2022-10-31", "base_amount": 80, "add_on_charges": 0, "taxes": 6.89, "total_amount": 86.89, "payment_status": "Paid"}, {"billing_id": "BILL-655D31CC", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2022-11-30", "base_amount": 80, "add_on_charges": 0, "taxes": 8.49, "total_amount": 88.49, "payment_status": "Late"}, {"billing_id": "BILL-7584EE5F", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2022-12-30", "base_amount": 80, "add_on_charges": 0, "taxes": 9.13, "total_amount": 89.13, "payment_status": "Paid"}, {"billing_id": "BILL-737CB662", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-01-29", "base_amount": 80, "add_on_charges": 0, "taxes": 8.38, "total_amount": 88.38, "payment_status": "Paid"}, {"billing_id": "BILL-793FD71D", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-02-28", "base_amount": 80, "add_on_charges": 0, "taxes": 9.57, "total_amount": 89.57, "payment_status": "Paid"}, {"billing_id": "BILL-13E7ADED", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-03-30", "base_amount": 80, "add_on_charges": 0, "taxes": 8.95, "total_amount": 88.95, "payment_status": "Paid"}, {"billing_id": "BILL-9449B242", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-04-29", "base_amount": 80, "add_on_charges": 0, "taxes": 6.51, "total_amount": 86.51, "payment_status": "Paid"}, {"billing_id": "BILL-36DB323D", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-05-29", "base_amount": 80, "add_on_charges": 0, "taxes": 8.26, "total_amount": 88.26, "payment_status": "Paid"}, {"billing_id": "BILL-DD2AD777", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-06-28", "base_amount": 80, "add_on_charges": 0, "taxes": 8.99, "total_amount": 88.99, "payment_status": "Paid"}, {"billing_id": "BILL-E9C5718A", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-07-28", "base_amount": 80, "add_on_charges": 0, "taxes": 8.91, "total_amount": 88.91, "payment_status": "Paid"}, {"billing_id": "BILL-73B1F55F", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-08-27", "base_amount": 80, "add_on_charges": 0, "taxes": 8.82, "total_amount": 88.82, "payment_status": "Paid"}, {"billing_id": "BILL-CA013528", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-09-26", "base_amount": 80, "add_on_charges": 0, "taxes": 9.2, "total_amount": 89.2, "payment_status": "Paid"}, {"billing_id": "BILL-ACDE8278", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-10-26", "base_amount": 80, "add_on_charges": 0, "taxes": 7.19, "total_amount": 87.19, "payment_status": "Paid"}, {"billing_id": "BILL-BE5D99EF", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-11-25", "base_amount": 80, "add_on_charges": 0, "taxes": 8.29, "total_amount": 88.29, "payment_status": "Paid"}, {"billing_id": "BILL-241465DF", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-975D9D87", "billing_date": "2023-12-25", "base_amount": 80, "add_on_charges": 0, "taxes": 9.0, "total_amount": 89.0, "payment_status": "Paid"}, {"billing_id": "BILL-4ED0EF55", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-01-01", "base_amount": 90, "add_on_charges": 7.99, "taxes": 11.97, "total_amount": 120.96, "payment_status": "Late", "discount_amount": 9.0, "discount_reason": "Upgrade", "one_time_charges": 20, "one_time_description": "International usage"}, {"billing_id": "BILL-55CC7769", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-01-31", "base_amount": 90, "add_on_charges": 7.99, "taxes": 9.42, "total_amount": 98.41, "payment_status": "Paid", "discount_amount": 9.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-36B2D428", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-03-01", "base_amount": 90, "add_on_charges": 7.99, "taxes": 10.08, "total_amount": 99.07, "payment_status": "Paid", "discount_amount": 9.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-0FEDD9F5", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-03-31", "base_amount": 90, "add_on_charges": 7.99, "taxes": 9.8, "total_amount": 98.79, "payment_status": "Paid", "discount_amount": 9.0, "discount_reason": "Upgrade"}, {"billing_id": "BILL-2BB615EE", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-04-30", "base_amount": 90, "add_on_charges": 7.99, "taxes": 11.29, "total_amount": 109.28, "payment_status": "Paid"}, {"billing_id": "BILL-C8A0062C", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-05-30", "base_amount": 90, "add_on_charges": 7.99, "taxes": 10.9, "total_amount": 108.89, "payment_status": "Late"}, {"billing_id": "BILL-49F6C7A0", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-06-29", "base_amount": 90, "add_on_charges": 7.99, "taxes": 9.38, "total_amount": 107.37, "payment_status": "Paid"}, {"billing_id": "BILL-D00382D8", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-FCB74E51", "billing_date": "2024-07-29", "base_amount": 90, "add_on_charges": 7.99, "taxes": 10.24, "total_amount": 108.23, "payment_status": "Paid"}, {"billing_id": "BILL-0F5BD6A2", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2024-08-01", "base_amount": 130, "add_on_charges": 15.98, "taxes": 14.93, "total_amount": 141.41, "payment_status": "Paid", "discount_amount": 19.5, "discount_reason": "Bundle"}, {"billing_id": "BILL-548BD555", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2024-08-31", "base_amount": 130, "add_on_charges": 15.98, "taxes": 11.35, "total_amount": 137.83, "payment_status": "Paid", "discount_amount": 19.5, "discount_reason": "Bundle"}, {"billing_id": "BILL-4370A7DE", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2024-09-30", "base_amount": 130, "add_on_charges": 15.98, "taxes": 11.12, "total_amount": 137.6, "payment_status": "Paid", "discount_amount": 19.5, "discount_reason": "Bundle"}, {"billing_id": "BILL-FFB003E1", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2024-10-30", "base_amount": 130, "add_on_charges": 15.98, "taxes": 11.68, "total_amount": 138.16, "payment_status": "Paid", "discount_amount": 19.5, "discount_reason": "Bundle"}, {"billing_id": "BILL-FD99E696", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2024-11-29", "base_amount": 130, "add_on_charges": 15.98, "taxes": 10.93, "total_amount": 137.41, "payment_status": "Paid", "discount_amount": 19.5, "discount_reason": "Bundle"}, {"billing_id": "BILL-8FEC9E1B", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2024-12-29", "base_amount": 130, "add_on_charges": 15.98, "taxes": 12.06, "total_amount": 138.54, "payment_status": "Paid", "discount_amount": 19.5, "discount_reason": "Bundle"}, {"billing_id": "BILL-B58E719E", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2025-01-28", "base_amount": 130, "add_on_charges": 15.98, "taxes": 14.5, "total_amount": 140.98, "payment_status": "Paid", "discount_amount": 19.5, "discount_reason": "Bundle"}, {"billing_id": "BILL-E77A368B", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2025-02-27", "base_amount": 130, "add_on_charges": 15.98, "taxes": 14.99, "total_amount": 160.97, "payment_status": "Paid"}, {"billing_id": "BILL-03E16ABA", "customer_id": "CUST-80D5E4C7", "plan_id": "PLAN-66A9781C", "billing_date": "2025-03-29", "base_amount": 130, "add_on_charges": 15.98, "taxes": 13.67, "total_amount": 159.65, "payment_status": "Paid"}]