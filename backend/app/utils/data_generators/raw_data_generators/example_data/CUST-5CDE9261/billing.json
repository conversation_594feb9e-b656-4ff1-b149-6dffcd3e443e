[{"billing_id": "BILL-1D10B78F", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "billing_date": "2024-10-01", "base_amount": 140, "add_on_charges": 25.98, "taxes": 12.92, "total_amount": 150.9, "payment_status": "Paid", "discount_amount": 28.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-34107440", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "billing_date": "2024-10-31", "base_amount": 140, "add_on_charges": 25.98, "taxes": 14.77, "total_amount": 152.75, "payment_status": "Paid", "discount_amount": 28.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-88928AAE", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "billing_date": "2024-11-30", "base_amount": 140, "add_on_charges": 25.98, "taxes": 15.77, "total_amount": 153.75, "payment_status": "Paid", "discount_amount": 28.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-B22FBA05", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "billing_date": "2024-12-30", "base_amount": 140, "add_on_charges": 25.98, "taxes": 13.83, "total_amount": 151.81, "payment_status": "Paid", "discount_amount": 28.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-C2EC5183", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "billing_date": "2025-01-29", "base_amount": 140, "add_on_charges": 25.98, "taxes": 15.93, "total_amount": 153.91, "payment_status": "Paid", "discount_amount": 28.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-6226828C", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "billing_date": "2025-02-28", "base_amount": 140, "add_on_charges": 25.98, "taxes": 16.81, "total_amount": 174.79, "payment_status": "Paid", "discount_amount": 28.0, "discount_reason": "New Customer", "one_time_charges": 20, "one_time_description": "International usage"}, {"billing_id": "BILL-84974474", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "billing_date": "2025-03-30", "base_amount": 140, "add_on_charges": 25.98, "taxes": 11.76, "total_amount": 149.74, "payment_status": "Paid", "discount_amount": 28.0, "discount_reason": "New Customer"}]