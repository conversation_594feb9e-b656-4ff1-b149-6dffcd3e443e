[{"network_id": "NET-9CB86081", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "month_start_date": "2024-10-01", "data_usage_gb": 4.3, "call_minutes": 705, "text_messages": 576, "dropped_calls": 10, "avg_speed_mbps": 106.7, "num_lines": 2}, {"network_id": "NET-A8DEF93D", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "month_start_date": "2024-10-31", "data_usage_gb": 4.77, "call_minutes": 545, "text_messages": 568, "dropped_calls": 7, "avg_speed_mbps": 112.4, "num_lines": 2}, {"network_id": "NET-CF329402", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "month_start_date": "2024-11-30", "data_usage_gb": 9.68, "call_minutes": 501, "text_messages": 542, "dropped_calls": 8, "avg_speed_mbps": 91.6, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 4.68, "data_overage_charges": 70.2}, {"network_id": "NET-7C1D83AE", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "month_start_date": "2024-12-30", "data_usage_gb": 3.52, "call_minutes": 704, "text_messages": 474, "dropped_calls": 4, "avg_speed_mbps": 100.7, "num_lines": 2}, {"network_id": "NET-C39178D3", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "month_start_date": "2025-01-29", "data_usage_gb": 8.78, "call_minutes": 487, "text_messages": 460, "dropped_calls": 4, "avg_speed_mbps": 93.5, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 3.78, "data_overage_charges": 56.7}, {"network_id": "NET-491A2857", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "month_start_date": "2025-02-28", "data_usage_gb": 4.53, "call_minutes": 570, "text_messages": 547, "dropped_calls": 24, "avg_speed_mbps": 109.3, "num_lines": 2}, {"network_id": "NET-9F35E001", "customer_id": "CUST-5CDE9261", "plan_id": "PLAN-CF7EAE38", "month_start_date": "2025-03-30", "data_usage_gb": 8.61, "call_minutes": 583, "text_messages": 429, "dropped_calls": 6, "avg_speed_mbps": 109.0, "num_lines": 2, "data_limit_exceeded": true, "data_overage_gb": 3.61, "data_overage_charges": 54.15}]