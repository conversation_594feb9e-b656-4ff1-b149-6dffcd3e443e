[{"billing_id": "BILL-73FC8D91", "customer_id": "CUST-CECAEC5A", "plan_id": "PLAN-8CCD3B35", "billing_date": "2023-12-01", "base_amount": 175, "add_on_charges": 0, "taxes": 15.05, "total_amount": 165.05, "payment_status": "Paid", "discount_amount": 35.0, "discount_reason": "New Customer", "one_time_charges": 10, "one_time_description": "Late payment fee"}, {"billing_id": "BILL-88F98094", "customer_id": "CUST-CECAEC5A", "plan_id": "PLAN-8CCD3B35", "billing_date": "2023-12-31", "base_amount": 175, "add_on_charges": 0, "taxes": 14.96, "total_amount": 154.96, "payment_status": "Paid", "discount_amount": 35.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-DF4C1CD8", "customer_id": "CUST-CECAEC5A", "plan_id": "PLAN-8CCD3B35", "billing_date": "2024-01-30", "base_amount": 175, "add_on_charges": 0, "taxes": 15.69, "total_amount": 155.69, "payment_status": "Paid", "discount_amount": 35.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-E7B61E06", "customer_id": "CUST-CECAEC5A", "plan_id": "PLAN-8CCD3B35", "billing_date": "2024-02-29", "base_amount": 175, "add_on_charges": 0, "taxes": 13.36, "total_amount": 153.36, "payment_status": "Paid", "discount_amount": 35.0, "discount_reason": "New Customer"}, {"billing_id": "BILL-C9BB65D1", "customer_id": "CUST-CECAEC5A", "plan_id": "PLAN-8CCD3B35", "billing_date": "2024-03-30", "base_amount": 175, "add_on_charges": 0, "taxes": 16.91, "total_amount": 166.91, "payment_status": "Paid", "discount_amount": 35.0, "discount_reason": "New Customer", "one_time_charges": 10, "one_time_description": "Late payment fee"}]