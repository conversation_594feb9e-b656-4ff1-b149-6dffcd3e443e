"""
Customer generator for creating synthetic customer demographic data.

This module provides utilities to generate realistic customer demographic data
for wireless customers.
"""

import uuid
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime, timedelta
import logging
import random
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import date utilities
from date_utils import standardize_to_first_of_month, format_date

# Set up logging
logger = logging.getLogger(__name__)

# Country-specific configurations
COUNTRY_CONFIGS = {
    "US": {
        "currency": "USD",
        "currency_symbol": "$",
        "regions": ["Northeast", "Southeast", "Midwest", "Southwest", "West"],
        "region_probs": [0.20, 0.20, 0.20, 0.15, 0.25],
        "income_ranges": {
            "low": ("<$30k", 15000, 30000),
            "lower_mid": ("$30k-$50k", 30000, 50000),
            "mid": ("$50k-$75k", 50000, 75000),
            "upper_mid": ("$75k-$100k", 75000, 100000),
            "high": ("$100k-$150k", 100000, 150000),
            "very_high": (">$150k", 150000, 250000)
        },
        "topup_channels": [
            "Walmart", "CVS Pharmacy", "Walgreens", "7-Eleven", "Dollar General",
            "Family Dollar", "Target", "Best Buy", "AT&T Store", "Verizon Store",
            "T-Mobile Store", "Online", "Mobile App", "Gas Station", "Grocery Store"
        ],
        "channel_probs": [
            0.15, 0.12, 0.10, 0.08, 0.08,  # Major retailers
            0.06, 0.05, 0.04, 0.06, 0.06,  # More retailers and carrier stores
            0.05, 0.08, 0.05, 0.04, 0.02   # Online, app, and others
        ],
        "topup_amounts": {
            "daily": (3, 10),
            "weekly": (10, 25),
            "monthly_low": (15, 35),
            "monthly_mid": (20, 45),
            "monthly_high": (25, 50),
            "monthly_premium": (35, 65),
            "irregular": (5, 40)
        }
    },
    "ID": {  # Indonesia
        "currency": "IDR",
        "currency_symbol": "Rp",
        "regions": ["Java", "Sumatra", "Kalimantan", "Sulawesi", "Papua", "Other Islands"],
        "region_probs": [0.55, 0.20, 0.08, 0.07, 0.05, 0.05],
        "income_ranges": {
            "low": ("<Rp3M", 1000000, 3000000),      # <$70 USD
            "lower_mid": ("Rp3M-5M", 3000000, 5000000),  # $70-$350 USD
            "mid": ("Rp5M-8M", 5000000, 8000000),        # $350-$560 USD
            "upper_mid": ("Rp8M-15M", 8000000, ********), # $560-$1050 USD
            "high": ("Rp15M-25M", ********, ********),    # $1050-$1750 USD
            "very_high": (">Rp25M", ********, ********)   # >$1750 USD
        },
        "topup_channels": [
            "Indomaret", "Alfamart", "Warung", "Bank BRI", "Bank Mandiri",
            "Bank BCA", "OVO", "GoPay", "DANA", "ShopeePay",
            "Tokopedia", "Online Banking", "Mobile Banking", "ATM", "Pulsa Counter"
        ],
        "channel_probs": [
            0.20, 0.18, 0.15, 0.08, 0.07,  # Major convenience stores and banks
            0.06, 0.08, 0.06, 0.05, 0.03,  # Digital wallets
            0.02, 0.01, 0.01, 0.01, 0.01   # Online and others
        ],
        "topup_amounts": {
            "daily": (5000, 15000),      # $0.35-$1 USD
            "weekly": (15000, 50000),    # $1-$3.5 USD
            "monthly_low": (25000, 75000),    # $1.75-$5.25 USD
            "monthly_mid": (50000, 150000),   # $3.5-$10.5 USD
            "monthly_high": (100000, 250000), # $7-$17.5 USD
            "monthly_premium": (200000, 500000), # $14-$35 USD
            "irregular": (10000, 100000)  # $0.7-$7 USD
        }
    }
}

class CustomerGenerator:
    """Generate synthetic customer demographic data."""
    
    def __init__(
        self,
        random_seed: Optional[int] = None,
        noise_level: float = 1.0,
        current_date: Optional[str] = None,
        country: str = "US"
    ):
        """
        Initialize the customer generator.

        Args:
            random_seed: Seed for reproducibility
            noise_level: Amount of random noise to add (0.0=none, 2.0=double)
            current_date: Reference date for date calculations, format YYYY-MM-DD
            country: Country code for localization ("US" or "ID")
        """
        self.noise_level = noise_level

        # Set country configuration
        if country not in COUNTRY_CONFIGS:
            raise ValueError(f"Unsupported country: {country}. Supported countries: {list(COUNTRY_CONFIGS.keys())}")
        self.country = country
        self.config = COUNTRY_CONFIGS[country]

        # Set the current date or default to today
        if current_date:
            self.current_date = datetime.strptime(current_date, '%Y-%m-%d')
        else:
            self.current_date = datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0
            )

        # Set random seed for reproducibility if provided
        if random_seed is not None:
            np.random.seed(random_seed)
            random.seed(random_seed)
    
    def generate_customer(self) -> Dict[str, Any]:
        """
        Generate a single customer with demographic data.
        
        Returns:
            Dictionary with customer demographic data
        """
        customer = {
            'customer_id': f"CUST-{uuid.uuid4().hex[:8].upper()}",
            'created_date': self._generate_created_date(),
        }
        
        # Add demographic information
        customer.update(self._generate_demographics())
        
        # Add location information
        customer.update(self._generate_location())
        
        # Add household information
        customer.update(self._generate_household())
        
        # Add financial information
        customer.update(self._generate_financial())

        # Add prepaid-specific information
        customer.update(self._generate_prepaid_usage(customer))

        return customer
    
    def _generate_created_date(self) -> datetime:
        """Generate a realistic customer creation date."""
        # Generate a date between 5 years ago and today
        max_days_ago = 365 * 5
        days_ago = random.randint(0, max_days_ago)
        created_date = self.current_date - timedelta(days=days_ago)
        
        # Standardize to first day of month
        return standardize_to_first_of_month(created_date)
    
    def _generate_demographics(self) -> Dict[str, Any]:
        """Generate demographic information for a customer."""
        # Age: normal distribution between 18 and 95
        age = int(np.clip(np.random.normal(42, 15), 18, 95))
        
        # Gender
        gender_options = ["Male", "Female", "Non-binary", "Prefer not to say"]
        gender_probs = [0.48, 0.48, 0.02, 0.02]
        gender = np.random.choice(gender_options, p=gender_probs)
        
        # Education level
        education_options = ["High School", "Associate's", "Bachelor's", "Master's", "Doctorate", "Other"]
        education_probs = [0.30, 0.15, 0.35, 0.15, 0.03, 0.02]
        education = np.random.choice(education_options, p=education_probs)
        
        # Marital status
        marital_options = ["Single", "Married", "Divorced", "Widowed", "Domestic Partnership"]
        marital_probs = [0.30, 0.45, 0.15, 0.05, 0.05]
        marital_status = np.random.choice(marital_options, p=marital_probs)
        
        # Occupation
        occupation_options = [
            "Professional", "Technical", "Sales", "Administrative", 
            "Service", "Manual Labor", "Student", "Retired", "Unemployed", "Other"
        ]
        occupation_probs = [0.25, 0.15, 0.12, 0.12, 0.10, 0.08, 0.05, 0.05, 0.03, 0.05]
        occupation = np.random.choice(occupation_options, p=occupation_probs)
        
        # Employment duration: lognormal distribution
        if occupation not in ["Student", "Retired", "Unemployed"]:
            employment_duration = max(0, min(45, np.random.lognormal(1.5, 0.8)))
            employment_duration = round(employment_duration, 1)
        else:
            employment_duration = 0
        
        return {
            'age': age,
            'gender': gender,
            'education_level': education,
            'marital_status': marital_status,
            'occupation': occupation,
            'employment_duration': employment_duration
        }
    
    def _generate_location(self) -> Dict[str, Any]:
        """Generate location information for a customer."""
        # Region based on country
        region_options = self.config["regions"]
        region_probs = self.config["region_probs"]
        region = np.random.choice(region_options, p=region_probs)
        
        # City type
        city_options = ["Urban", "Suburban", "Rural"]
        city_probs = [0.40, 0.45, 0.15]
        city_type = np.random.choice(city_options, p=city_probs)
        
        # ZIP code (just a placeholder, not realistic by region)
        zip_code = f"{random.randint(10000, 99999)}"
        
        return {
            'region': region,
            'city_type': city_type,
            'zip_code': zip_code
        }
    
    def _generate_household(self) -> Dict[str, Any]:
        """Generate household information for a customer."""
        # Housing type depends on city type
        housing_probs = {
            "Urban": [0.50, 0.25, 0.10, 0.10, 0.01, 0.04],  # More apartments in urban areas
            "Suburban": [0.15, 0.15, 0.50, 0.15, 0.02, 0.03],  # More houses in suburbs
            "Rural": [0.05, 0.05, 0.65, 0.05, 0.15, 0.05]  # More houses & mobile homes in rural
        }
        
        city_type = "Urban"  # Default
        if 'city_type' in self.__dict__:
            city_type = self.city_type
        
        housing_options = ["Apartment", "Condo", "House", "Townhouse", "Mobile Home", "Other"]
        housing_type = np.random.choice(housing_options, p=housing_probs.get(city_type, housing_probs["Urban"]))
        
        # Ownership status
        ownership_options = ["Own", "Mortgage", "Rent", "Living with Family", "Other"]
        
        # Adjust probabilities based on housing type
        if housing_type in ["Apartment", "Condo"]:
            ownership_probs = [0.05, 0.15, 0.70, 0.05, 0.05]  # More likely to rent
        elif housing_type == "House":
            ownership_probs = [0.30, 0.50, 0.10, 0.05, 0.05]  # More likely to own/mortgage
        else:
            ownership_probs = [0.25, 0.35, 0.30, 0.05, 0.05]  # Default
            
        ownership_status = np.random.choice(ownership_options, p=ownership_probs)
        
        # Household size: poisson distribution
        household_size = max(1, np.random.poisson(2.5))
        
        # Dependents: poisson distribution, but less than household size
        dependents = min(household_size - 1, max(0, np.random.poisson(1.2)))
        
        # Years at address: lognormal distribution
        years_at_address = max(0, min(50, np.random.lognormal(1.6, 0.8)))
        years_at_address = round(years_at_address, 1)
        
        return {
            'housing_type': housing_type,
            'ownership_status': ownership_status,
            'household_size': household_size,
            'dependents': dependents,
            'years_at_address': years_at_address
        }
    
    def _generate_financial(self) -> Dict[str, Any]:
        """Generate financial information for a customer."""
        # Get income ranges for the country
        income_ranges = self.config["income_ranges"]

        # Generate income based on country-specific distribution
        if self.country == "US":
            # US income: lognormal distribution
            income = max(15000, min(250000, np.random.lognormal(11, 0.7)))
            income = round(income, -3)  # Round to nearest thousand
        else:  # Indonesia
            # Indonesia income: different distribution, lower overall
            income = max(1000000, min(********, np.random.lognormal(15.5, 0.8)))
            income = round(income, -5)  # Round to nearest 100k IDR

        # Determine income range based on exact income
        income_range = None
        for range_key, (range_label, min_val, max_val) in income_ranges.items():
            if min_val <= income < max_val:
                income_range = range_label
                break

        # Handle edge case for very high income
        if income_range is None:
            income_range = income_ranges["very_high"][0]

        return {
            'income': income,
            'income_range': income_range
        }

    def _generate_prepaid_usage(self, customer: Dict[str, Any]) -> Dict[str, Any]:
        """Generate prepaid-specific usage and behavior data."""
        # Device type based on age and income
        age = customer['age']
        income = customer['income']

        # Older customers and lower income more likely to have basic phones
        if age > 65 or income < 30000:
            device_probs = [0.4, 0.6]  # [basic phone, smartphone]
        elif age > 50 or income < 50000:
            device_probs = [0.2, 0.8]
        else:
            device_probs = [0.05, 0.95]

        device_options = ["Basic Phone", "Smartphone"]
        device_type = np.random.choice(device_options, p=device_probs)

        # Top-up frequency based on income and device type
        if income < 30000:
            frequency_probs = [0.1, 0.3, 0.5, 0.1]  # [daily, weekly, monthly, not recurring]
        elif income < 50000:
            frequency_probs = [0.05, 0.2, 0.6, 0.15]
        elif income < 100000:
            frequency_probs = [0.02, 0.1, 0.7, 0.18]
        else:
            frequency_probs = [0.01, 0.05, 0.75, 0.19]

        frequency_options = ["Daily", "Weekly", "Monthly", "Not Recurring"]
        topup_frequency = np.random.choice(frequency_options, p=frequency_probs)

        # Top-up amount based on frequency, income, and country
        topup_amounts = self.config["topup_amounts"]

        if topup_frequency == "Daily":
            min_amt, max_amt = topup_amounts["daily"]
            topup_amount_avg = round(random.uniform(min_amt, max_amt), 2)
        elif topup_frequency == "Weekly":
            min_amt, max_amt = topup_amounts["weekly"]
            topup_amount_avg = round(random.uniform(min_amt, max_amt), 2)
        elif topup_frequency == "Monthly":
            # Determine income tier for monthly amounts
            income_ranges = self.config["income_ranges"]
            if self.country == "US":
                if income < 30000:
                    min_amt, max_amt = topup_amounts["monthly_low"]
                elif income < 50000:
                    min_amt, max_amt = topup_amounts["monthly_mid"]
                elif income < 100000:
                    min_amt, max_amt = topup_amounts["monthly_high"]
                else:
                    min_amt, max_amt = topup_amounts["monthly_premium"]
            else:  # Indonesia
                if income < 3000000:
                    min_amt, max_amt = topup_amounts["monthly_low"]
                elif income < 8000000:
                    min_amt, max_amt = topup_amounts["monthly_mid"]
                elif income < ********:
                    min_amt, max_amt = topup_amounts["monthly_high"]
                else:
                    min_amt, max_amt = topup_amounts["monthly_premium"]
            topup_amount_avg = round(random.uniform(min_amt, max_amt), 2)
        else:  # Not recurring
            min_amt, max_amt = topup_amounts["irregular"]
            topup_amount_avg = round(random.uniform(min_amt, max_amt), 2)

        # Calculate total top-up for last 3 months based on frequency and amount
        if topup_frequency == "Daily":
            total_topup_3m = round(topup_amount_avg * 90 * random.uniform(0.8, 1.2), 2)
        elif topup_frequency == "Weekly":
            total_topup_3m = round(topup_amount_avg * 12 * random.uniform(0.8, 1.2), 2)
        elif topup_frequency == "Monthly":
            total_topup_3m = round(topup_amount_avg * 3 * random.uniform(0.8, 1.2), 2)
        else:  # Not recurring
            # Random number of top-ups in 3 months
            num_topups = random.randint(1, 8)
            total_topup_3m = round(topup_amount_avg * num_topups * random.uniform(0.7, 1.3), 2)

        # Data usage based on device type and income
        if device_type == "Basic Phone":
            data_usage_last_30d = round(random.uniform(0.1, 2.0), 2)  # Very low data usage
        else:  # Smartphone
            if income < 30000:
                data_usage_last_30d = round(random.uniform(1.0, 8.0), 2)
            elif income < 50000:
                data_usage_last_30d = round(random.uniform(3.0, 15.0), 2)
            elif income < 100000:
                data_usage_last_30d = round(random.uniform(5.0, 25.0), 2)
            else:
                data_usage_last_30d = round(random.uniform(8.0, 40.0), 2)

        # Call minutes based on age and device type
        if device_type == "Basic Phone":
            # Basic phone users tend to make more voice calls
            if age > 65:
                call_minutes_last_30d = random.randint(200, 800)
            elif age > 50:
                call_minutes_last_30d = random.randint(150, 600)
            else:
                call_minutes_last_30d = random.randint(100, 400)
        else:  # Smartphone
            # Smartphone users tend to use more messaging/data, fewer voice calls
            if age > 65:
                call_minutes_last_30d = random.randint(100, 400)
            elif age > 50:
                call_minutes_last_30d = random.randint(50, 300)
            else:
                call_minutes_last_30d = random.randint(20, 200)

        # Last top-up channel - country-specific
        channel_options = self.config["topup_channels"]
        channel_probs = self.config["channel_probs"]
        # Normalize probabilities to ensure they sum to exactly 1.0
        channel_probs = [p / sum(channel_probs) for p in channel_probs]
        last_topup_channel = np.random.choice(channel_options, p=channel_probs)

        # Days since last activity (consistent with top-up frequency)
        if topup_frequency == "Daily":
            days_since_last_activity = random.randint(0, 3)
        elif topup_frequency == "Weekly":
            days_since_last_activity = random.randint(0, 10)
        elif topup_frequency == "Monthly":
            days_since_last_activity = random.randint(0, 35)
        else:  # Not recurring
            days_since_last_activity = random.randint(1, 90)

        return {
            'topup_frequency': topup_frequency,
            'topup_amount_avg': topup_amount_avg,
            'total_topup_3m': total_topup_3m,
            'data_usage_last_30d': data_usage_last_30d,
            'call_minutes_last_30d': call_minutes_last_30d,
            'last_topup_channel': last_topup_channel,
            'days_since_last_activity': days_since_last_activity,
            'device_type': device_type
        }
