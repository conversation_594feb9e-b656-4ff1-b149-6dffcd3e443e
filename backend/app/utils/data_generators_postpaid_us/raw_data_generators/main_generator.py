"""
Main generator for creating synthetic wireless customer data.

This module provides a coordinated approach to generate synthetic data for wireless customers,
including demographic data, plan data, billing data, network data, and churn behavior.
"""

import uuid
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime, timedelta
import logging
import random
import json
import os
from pathlib import Path

# Fix imports for direct script execution
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import directly from the current directory
from customer_generator import CustomerGenerator
from plan_generator import PlanGenerator
from billing_generator import BillingGenerator
from network_generator import NetworkGenerator
from churn_generator import ChurnGenerator

# Set up logging
logger = logging.getLogger(__name__)

class WirelessCustomerGenerator:
    """Generate synthetic wireless customer data."""
    
    def __init__(
        self,
        random_seed: Optional[int] = None,
        current_date: Optional[str] = None,
        output_dir: Optional[str] = None,
        baseline_churn_rate: float = 0.008,
        min_date: Optional[str] = None,
        country: str = "US"
    ):
        """
        Initialize the wireless customer generator.

        Args:
            random_seed: Seed for reproducibility
            current_date: Reference date for date calculations, format YYYY-MM-DD
            output_dir: Directory to save generated data
            baseline_churn_rate: Base monthly churn probability (default: 0.008 or 0.8%)
            min_date: Minimum date for all customers (earliest date for billing, plans, network data)
            country: Country code for localization ("US" or "ID")
        """
        # Set the current date or default to today
        if current_date:
            self.current_date = datetime.strptime(current_date, '%Y-%m-%d')
        else:
            self.current_date = datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        
        # Set minimum date or default to 5 years ago
        if min_date:
            self.min_date = datetime.strptime(min_date, '%Y-%m-%d')
        else:
            self.min_date = self.current_date - timedelta(days=365 * 5)
        
        # Set output directory
        self.output_dir = output_dir
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # Initialize generators
        self.customer_generator = CustomerGenerator(
            random_seed=random_seed,
            current_date=current_date,
            country=country
        )

        self.plan_generator = PlanGenerator(
            random_seed=random_seed,
            current_date=current_date
        )

        self.billing_generator = BillingGenerator(
            random_seed=random_seed,
            current_date=current_date,
            country=country
        )
        
        self.network_generator = NetworkGenerator(
            random_seed=random_seed,
            current_date=current_date
        )
        
        self.churn_generator = ChurnGenerator(
            random_seed=random_seed,
            baseline_churn_rate=baseline_churn_rate,
            current_date=current_date,
            min_date=min_date
        )
    
    def generate_customer(self) -> Dict[str, Any]:
        """
        Generate a single customer with all related data.
        
        Returns:
            Dictionary with customer data and related records
        """
        # Generate customer demographic data
        customer = self.customer_generator.generate_customer()
        customer_id = customer['customer_id']
        
        logger.info(f"Generating data for customer {customer_id}")
        
        # Ensure customer created date is not before minimum date
        created_date = customer['created_date']
        if isinstance(created_date, str):
            created_date = datetime.strptime(created_date, '%Y-%m-%d %H:%M:%S')
        
        if created_date < self.min_date:
            # Adjust created date to be within allowed range
            customer['created_date'] = self.min_date + timedelta(
                days=random.randint(0, (self.current_date - self.min_date).days // 2)
            )
        
        # Initialize churn tracking
        customer['churned'] = False
        customer['churn_date'] = None
        
        # Generate plan data
        plans = self.plan_generator.generate_customer_plans(
            customer_id=customer_id,
            customer_created_date=customer['created_date'],
            household_size=customer['household_size'],
            income=customer['income']
        )
        
        # Generate billing data (pass customer data for consistency)
        billing_records = self.billing_generator.generate_customer_billing(
            customer_id=customer_id,
            plans=plans,
            customer_data=customer  # Pass customer data to ensure consistent top-up amounts
        )
        
        # Generate network data
        network_records = self.network_generator.generate_customer_network_data(
            customer_id=customer_id,
            plans=plans,
            age=customer['age'],
            occupation=customer['occupation']
        )
        
        # Process monthly data to determine churn
        customer_data = self._process_monthly_data(customer, plans, billing_records, network_records)
        
        # Save data if output directory is specified
        if self.output_dir:
            self._save_customer_data(customer_data)
        
        return customer_data
    
    def _process_monthly_data(
        self,
        customer: Dict[str, Any],
        plans: List[Dict[str, Any]],
        billing_records: List[Dict[str, Any]],
        network_records: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Process customer data month by month to determine churn.
        
        Args:
            customer: Dictionary with customer data
            plans: List of plan dictionaries
            billing_records: List of billing record dictionaries
            network_records: List of network record dictionaries
            
        Returns:
            Dictionary with processed customer data and related records
        """
        # Get customer created date
        created_date = customer['created_date']
        if isinstance(created_date, str):
            created_date = datetime.strptime(created_date, '%Y-%m-%d %H:%M:%S')
        
        # Initialize active events that affect churn
        active_events = []
        
        # Process each month from creation to current date
        current_month = created_date.replace(day=1)
        end_month = self.current_date.replace(day=1)
        
        # Track processed records
        processed_billing = []
        processed_network = []
        
        while current_month <= end_month and not customer['churned']:
            # Get records for this month
            month_billing = [b for b in billing_records
                            if datetime.strptime(b['transaction_date'], '%Y-%m-%d').replace(day=1) == current_month]
            month_network = [n for n in network_records 
                            if datetime.strptime(n['month_start_date'], '%Y-%m-%d').replace(day=1) == current_month]
            
            # Add to processed records
            processed_billing.extend(month_billing)
            processed_network.extend(month_network)
            
            # Update active events: remove expired events
            active_events = [
                {**event, 'months_active': event.get('months_active', 0) + 1}
                for event in active_events
                if event.get('months_active', 0) < event.get('duration', 1)
            ]
            
            # Detect new events
            new_events = self.churn_generator.detect_events(
                customer, plans, processed_billing, processed_network, current_month
            )
            active_events.extend(new_events)
            
            # Determine if customer churns this month
            churned, churn_date = self.churn_generator.determine_churn(customer, active_events)
            
            if churned:
                customer['churned'] = True
                customer['churn_date'] = churn_date
                break
            
            # Move to next month
            current_month = (current_month + timedelta(days=32)).replace(day=1)
        
        # Return processed data
        return {
            'customer': customer,
            'plans': plans,
            'billing': processed_billing,
            'network': processed_network
        }
    
    def generate_customers(self, num_customers: int) -> List[Dict[str, Any]]:
        """
        Generate multiple customers with all related data.
        
        Args:
            num_customers: Number of customers to generate
            
        Returns:
            List of dictionaries with customer data and related records
        """
        customers = []
        
        for _ in range(num_customers):
            customer_data = self.generate_customer()
            customers.append(customer_data)
        
        return customers
    
    def _save_customer_data(self, customer_data: Dict[str, Any]) -> None:
        """
        Save customer data to files.
        
        Args:
            customer_data: Dictionary with customer data and related records
        """
        customer_id = customer_data['customer']['customer_id']
        
        # Create customer directory
        customer_dir = os.path.join(self.output_dir, customer_id)
        os.makedirs(customer_dir, exist_ok=True)
        
        # Save customer demographic data
        with open(os.path.join(customer_dir, 'customer.json'), 'w') as f:
            json.dump(customer_data['customer'], f, indent=2, default=str)
        
        # Save plan data
        with open(os.path.join(customer_dir, 'plans.json'), 'w') as f:
            json.dump(customer_data['plans'], f, indent=2, default=str)
        
        # Save billing data
        with open(os.path.join(customer_dir, 'billing.json'), 'w') as f:
            json.dump(customer_data['billing'], f, indent=2, default=str)
        
        # Save network data
        with open(os.path.join(customer_dir, 'network.json'), 'w') as f:
            json.dump(customer_data['network'], f, indent=2, default=str)
        
        # Save all data as CSV files for easier analysis
        self._save_as_csv(customer_data, customer_dir)
    
    def _save_as_csv(self, customer_data: Dict[str, Any], customer_dir: str) -> None:
        """
        Save customer data as CSV files.
        
        Args:
            customer_data: Dictionary with customer data and related records
            customer_dir: Directory to save CSV files
        """
        # Create CSV directory
        csv_dir = os.path.join(customer_dir, 'csv')
        os.makedirs(csv_dir, exist_ok=True)
        
        # Save customer demographic data
        customer_df = pd.DataFrame([customer_data['customer']])
        customer_df.to_csv(os.path.join(csv_dir, 'customer.csv'), index=False)
        
        # Save plan data
        if customer_data['plans']:
            # Extract add-ons to separate dataframe
            add_ons = []
            for plan in customer_data['plans']:
                plan_add_ons = plan.pop('add_ons', [])
                for add_on in plan_add_ons:
                    add_ons.append(add_on)
            
            # Save plans
            plans_df = pd.DataFrame(customer_data['plans'])
            plans_df.to_csv(os.path.join(csv_dir, 'plans.csv'), index=False)
            
            # Save add-ons if any
            if add_ons:
                add_ons_df = pd.DataFrame(add_ons)
                add_ons_df.to_csv(os.path.join(csv_dir, 'add_ons.csv'), index=False)
        
        # Save billing data
        if customer_data['billing']:
            billing_df = pd.DataFrame(customer_data['billing'])
            billing_df.to_csv(os.path.join(csv_dir, 'billing.csv'), index=False)
        
        # Save network data
        if customer_data['network']:
            network_df = pd.DataFrame(customer_data['network'])
            network_df.to_csv(os.path.join(csv_dir, 'network.csv'), index=False)


def generate_customer(
    random_seed: Optional[int] = None,
    current_date: Optional[str] = None,
    output_dir: Optional[str] = None,
    baseline_churn_rate: float = 0.008,
    min_date: Optional[str] = None
) -> Dict[str, Any]:
    """
    Generate a single customer with all related data.
    
    Args:
        random_seed: Seed for reproducibility
        current_date: Reference date for date calculations, format YYYY-MM-DD
        output_dir: Directory to save generated data
        baseline_churn_rate: Base monthly churn probability (default: 0.008 or 0.8%)
        min_date: Minimum date for all customers (earliest date for billing, plans, network data)
        
    Returns:
        Dictionary with customer data and related records
    """
    generator = WirelessCustomerGenerator(
        random_seed=random_seed,
        current_date=current_date,
        output_dir=output_dir,
        baseline_churn_rate=baseline_churn_rate,
        min_date=min_date
    )
    
    return generator.generate_customer()


def generate_customers(
    num_customers: int,
    random_seed: Optional[int] = None,
    current_date: Optional[str] = None,
    output_dir: Optional[str] = None,
    baseline_churn_rate: float = 0.008,
    min_date: Optional[str] = None,
    country: str = "US"
) -> List[Dict[str, Any]]:
    """
    Generate multiple customers with all related data.

    Args:
        num_customers: Number of customers to generate
        random_seed: Seed for reproducibility
        current_date: Reference date for date calculations, format YYYY-MM-DD
        output_dir: Directory to save generated data
        baseline_churn_rate: Base monthly churn probability (default: 0.008 or 0.8%)
        min_date: Minimum date for all customers (earliest date for billing, plans, network data)
        country: Country code for localization ("US" or "ID")

    Returns:
        List of dictionaries with customer data and related records
    """
    generator = WirelessCustomerGenerator(
        random_seed=random_seed,
        current_date=current_date,
        output_dir=output_dir,
        baseline_churn_rate=baseline_churn_rate,
        min_date=min_date,
        country=country
    )

    return generator.generate_customers(num_customers)


def generate_customers_parallel(
    num_customers: int,
    random_seed: Optional[int] = None,
    current_date: Optional[str] = None,
    output_dir: Optional[str] = None,
    baseline_churn_rate: float = 0.008,
    min_date: Optional[str] = None,
    use_ray: bool = False,
    num_cpus: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Generate multiple customers with all related data, optionally using Ray for parallelization.
    
    Args:
        num_customers: Number of customers to generate
        random_seed: Seed for reproducibility
        current_date: Reference date for date calculations, format YYYY-MM-DD
        output_dir: Directory to save generated data
        baseline_churn_rate: Base monthly churn probability (default: 0.008 or 0.8%)
        min_date: Minimum date for all customers
        use_ray: Whether to use Ray for parallel processing
        num_cpus: Number of CPUs to use with Ray (defaults to all available)
        
    Returns:
        List of dictionaries with customer data and related records
    """
    if not use_ray:
        # Original sequential implementation
        generator = WirelessCustomerGenerator(
            random_seed=random_seed,
            current_date=current_date,
            output_dir=output_dir,
            baseline_churn_rate=baseline_churn_rate,
            min_date=min_date
        )
        return generator.generate_customers(num_customers)
    
    else:
        # Ray parallel implementation
        try:
            # Try absolute import first
            try:
                from ray_generator import generate_customers_ray
            except ImportError:
                # Fall back to relative import
                from .ray_generator import generate_customers_ray
        except ImportError:
            raise ImportError(
                "Ray is not installed. Install it with 'pip install ray' "
                "or set use_ray=False."
            )
        
        return generate_customers_ray(
            num_customers=num_customers,
            random_seed=random_seed,
            current_date=current_date,
            output_dir=output_dir,
            baseline_churn_rate=baseline_churn_rate,
            min_date=min_date,
            num_cpus=num_cpus
        )
