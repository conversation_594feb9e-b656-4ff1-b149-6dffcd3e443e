import pandas as pd
import datetime
import io
import base64
import matplotlib.pyplot as plt
from graphviz import Digraph
import os
import numpy as np
import json
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core import config
from app.utils.serialization import convert_numpy_types
from app.db.models.segmentation import SegmentationAnalysis
from app.db.models.data_dictionary import DataDictionary
from datetime import datetime
from app.core.logger import get_logger

# Import segmentation agents
from app.agents.opportunity_group_agents.segmentation import (
    SegmentationInterpretationAgent,
    SegmentationTitlingAgent,
    SegmentationDescriptionAgent,
    SegmentationVerificationAgent
)
from app.agents.opportunity_group_agents.segmentation import SegmentInterpretationInput
from app.agents.opportunity_group_agents.segmentation import SegmentTitlingInput
from app.agents.opportunity_group_agents.segmentation import SegmentDescriptionInput
from app.agents.opportunity_group_agents.segmentation import SegmentVerificationInput
from app.schemas.segmentation import InterpretedSegment, InterpretedCharacteristic

logger = get_logger(__name__)


class SegmentationService:
    """
    Service for segmentation analysis.
    """

    def __init__(self, db: AsyncSession = None):
        """
        Initialize the service.
        Note: db is optional and only kept for backward compatibility.
        All methods should accept db as an explicit parameter.
        """
        self.db = db  # This will be deprecated

    async def analyze_segments(
            self,
            db: AsyncSession,  # Pass db explicitly
            model_id: int,
            version_id: int,
            user_id: int,
            params: Dict[str, Any] = {}
    ) -> Dict[str, Any]:
        """
        Analyze customer segments based on dataset and parameters.
        
        Args:
            db: Database session
            model_id: ID of the model
            version_id: ID of the model version
            user_id: ID of the user requesting the analysis
            params: Additional parameters for segmentation
            
        Returns:
            Segmentation analysis results
        """
        logger.info(f"Analyzing customer segments for model {model_id}, version {version_id}")

        try:
            # Log the received parameters
            logger.info(f"Analyzing segments for model {model_id}, version {version_id} with params: {params}")

            # Always use SHAP data for segmentation (numeric values work better with tree algorithm)
            logger.info("Loading SHAP data for segmentation")
            df = await self._load_shap_data(db, model_id, version_id)
            
            if df is None or df.empty:
                logger.error("No SHAP data available for segmentation")
                raise ValueError("SHAP data is required for segmentation. Please ensure SHAP values have been computed for this model.")
            
            logger.info(f"Using SHAP data with {len(df)} rows and {len(df.columns)} columns for segmentation")

            # Load original model features
            model_features = await self._load_model_features(db, model_id, version_id)

            # Check if we're using new hyperlocal-style parameters or legacy parameters
            if "min_prospects" in params:
                # New hyperlocal style - use absolute numbers
                logger.info("Using hyperlocal-style parameters")
                min_prospects = params["min_prospects"]
                reuse_features = params.get("reuse_features", False)
                drop_columns = params.get("drop_columns", [])
                use_hyperlocal = True
            else:
                # Legacy style - convert to hyperlocal
                logger.info("Using legacy-style parameters, converting to hyperlocal")
                min_prospects_percent = params.get("min_prospects_percent", 0.05)
                if min_prospects_percent > 1:
                    logger.info(f"Converting min_prospects_percent from {min_prospects_percent} to {min_prospects_percent/100}")
                    min_prospects_percent = min_prospects_percent / 100
                
                # Convert percentage to absolute number
                min_prospects = max(int(len(df) * min_prospects_percent), 2)
                reuse_features = params.get("reuse_features", True)
                drop_columns = []
                use_hyperlocal = False
                
                # Log ignored parameters
                if "max_segments" in params:
                    logger.warning(f"max_segments={params['max_segments']} is ignored in hyperlocal segmentation")
                if "max_depth" in params and params["max_depth"] != 3:
                    logger.warning(f"max_depth={params['max_depth']} is ignored in hyperlocal segmentation")
            
            # Extract feature columns if specified
            feature_columns = params.get("feature_columns") or params.get("numerical_columns")

            # If feature_columns not provided, use model features or detect from data
            if not feature_columns:
                # First check if we have original_ columns in the data
                original_columns = [col for col in df.columns if col.startswith('original_')]

                if original_columns:
                    # Use original feature columns from SHAP data
                    logger.info(f"Using {len(original_columns)} original columns from SHAP data")
                    feature_columns = original_columns
                elif model_features:
                    # Use features from model metadata
                    logger.info(f"Using {len(model_features)} features from model metadata")
                    feature_columns = model_features
                else:
                    # Fall back to detecting numerical columns
                    feature_columns = self._get_numerical_columns(df)
                    logger.info(f"No features specified, detected {len(feature_columns)} numerical columns")

            # Log data info before segmentation
            logger.info(f"Data shape before segmentation: {df.shape}")
            logger.info(f"Feature columns for segmentation: {feature_columns}")
            
            # Check for problematic values in feature columns
            for col in feature_columns:
                if col in df.columns and df[col].dtype == 'object':
                    sample_values = df[col].dropna().head(5).tolist()
                    logger.info(f"Sample values for categorical column '{col}': {sample_values}")
            
            # Always use tree-based segmentation
            logger.info(f"Tree segmentation params: min_prospects={min_prospects}, reuse_features={reuse_features}")
            logger.info(f"Number of numeric features available: {len(feature_columns) if feature_columns else 'auto-detect'}")
            
            if use_hyperlocal:
                # Use new hyperlocal function directly
                tree_result = self._segment_by_tree_hyperlocal(
                    df,
                    min_prospects=min_prospects,
                    reuse_features=reuse_features,
                    drop_columns=drop_columns
                )
            else:
                # Use legacy function for backward compatibility
                tree_result = self._segment_by_tree(
                    df,
                    feature_columns,
                    min_prospects_percent=params.get("min_prospects_percent", 0.05),
                    max_depth=params.get("max_depth", 3),
                    max_segments=params.get("max_segments", 10),
                    reuse_features=reuse_features,
                    target_column=params.get("target_column")
                )
            
            # tree_result already has segments under "segments" key from tree_segmentation
            segments = tree_result
            
            # Extract tree if present for visualization
            tree_root = tree_result.get('tree', None)
            
            logger.info(f"Tree segmentation returned {len(segments.get('segments', {}))} segments")

            # Simple post-processing to remove row_index from results
            if 'groups' in segments:
                for group_id, group_data in segments['groups'].items():
                    # Remove row_index from feature distributions if present
                    if 'feature_distributions' in group_data and 'row_index' in group_data['feature_distributions']:
                        del group_data['feature_distributions']['row_index']

            # Calculate metrics BEFORE removing data from segments
            metrics = self._calculate_segment_metrics(df, segments)

            # Calculate feature importance
            feature_importance = self._calculate_feature_importance(df, segments, model_id, version_id)

            # Prepare segment summaries
            summaries = self._prepare_segment_summary(segments, metrics)

            # Prepare visualization data
            visualization = self._prepare_visualization_data(df, segments)

            # Log the structure of segments to help debug
            logger.info(f"Segments structure: {json.dumps(segments, default=str)[:500]}...")
            logger.info(f"Segments keys: {list(segments.keys())}")
            if "segments" in segments:
                logger.info(f"Segment names: {list(segments['segments'].keys())}")

            # Standardize the format for frontend consumption
            standardized_segments = {}
            
            # Now segments are properly nested under "segments" key
            if "segments" in segments:
                for segment_name, segment_info in segments["segments"].items():
                    standardized_characteristics = []
                    if "characteristics" in segment_info and segment_info["characteristics"]:
                        for char in segment_info["characteristics"]:
                            if isinstance(char, dict):
                                standardized_characteristics.append(char)
                            else:  # String format like "high_feature: value" or "low_feature: value"
                                parts = char.split(":")
                                feature = parts[0].strip()
                                value_str = parts[1].strip() if len(parts) > 1 else "0"
                                try:
                                    value_float = float(value_str)
                                except ValueError:
                                    value_float = 0
                                standardized_characteristics.append({
                                    "feature": feature,
                                    "value": value_float
                                })

                    # Get count, excluding the 'data' field which contains DataFrame
                    count = segment_info.get("count", 0) or segment_info.get("prospect_count", 0)
                    
                    standardized_segments[segment_name] = {
                        "characteristics": standardized_characteristics,
                        "count": count
                    }
                    
                    logger.debug(f"Segment {segment_name}: {len(standardized_characteristics)} characteristics, {count} customers")
            
            logger.info(f"Standardized {len(standardized_segments)} segments from tree segmentation")

            # Convert NumPy types to Python native types and ensure no DataFrame objects
            result = {
                "segments": {
                    "method": segments.get("method", "tree_based"),
                    "segments": convert_numpy_types(standardized_segments)
                },
                "metrics": convert_numpy_types(metrics),
                "feature_importance": convert_numpy_types(feature_importance),
                "summaries": summaries,
                "visualization": convert_numpy_types(visualization)
            }

            # Store tree root for later use (won't be serialized)
            if tree_root is not None:
                result['_tree_root'] = tree_root
                
                # Generate tree visualization
                try:
                    from app.utils.tree_segmentation_viz import generate_tree_image
                    import base64
                    
                    # Generate tree in multiple formats
                    tree_visualizations = {}
                    
                    # Generate PNG
                    try:
                        png_bytes = generate_tree_image(tree_root, segment_interpretations=None, format='png')
                        tree_visualizations['png'] = base64.b64encode(png_bytes).decode('utf-8')
                    except Exception as e:
                        logger.error(f"Failed to generate PNG: {str(e)}")
                    
                    # Generate SVG
                    try:
                        svg_bytes = generate_tree_image(tree_root, segment_interpretations=None, format='svg')
                        tree_visualizations['svg'] = base64.b64encode(svg_bytes).decode('utf-8')
                    except Exception as e:
                        logger.error(f"Failed to generate SVG: {str(e)}")
                    
                    if tree_visualizations:
                        result['tree_visualization'] = {
                            'formats': tree_visualizations,
                            'default_format': 'png' if 'png' in tree_visualizations else 'svg'
                        }
                        logger.info(f"Generated tree visualization in formats: {list(tree_visualizations.keys())}")
                    else:
                        logger.error("Failed to generate tree visualization in any format (PNG, SVG)")
                except Exception as e:
                    logger.error(f"Error generating tree visualization: {str(e)}")
                    # Don't fail the entire analysis if visualization fails
            
            # Final check to ensure no DataFrame objects remain
            result = self._ensure_serializable(result)

            # Remove internal fields before returning
            if '_tree_root' in result:
                del result['_tree_root']

            # Log the final structure
            logger.info(f"Final result structure: {json.dumps({k: type(v).__name__ for k, v in result.items()})}")
            logger.info(f"Final segments count: {len(result['segments']['segments'])}")
            logger.info(f"Final segment names: {list(result['segments']['segments'].keys())}")

            return result
        except Exception as e:
            logger.error(f"Error analyzing segments: {str(e)}", exc_info=True)
            raise ValueError(f"Error analyzing segments: {str(e)}")

    async def analyze_segments_with_interpretation(
            self,
            db: AsyncSession,
            model_id: int,
            version_id: int,
            user_id: int,
            params: Dict[str, Any] = {},
            llm_service = None,
            llm_model_id: Optional[str] = None,
            interpret: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze customer segments with optional LLM interpretation.
        
        Args:
            db: Database session
            model_id: ID of the model
            version_id: ID of the model version
            user_id: ID of the user requesting the analysis
            params: Additional parameters for segmentation
            llm_service: LLM service instance for interpretation
            llm_model_id: Model ID to use for LLM interpretation
            interpret: Whether to run interpretation pipeline
            
        Returns:
            Segmentation analysis results with interpretations
        """
        # First, run the standard segmentation analysis
        result = await self.analyze_segments(db, model_id, version_id, user_id, params)
        
        # If interpretation is requested and LLM service is available
        if interpret and llm_service and llm_model_id:
            try:
                logger.info("Running interpretation pipeline on segmentation results")
                
                # Get feature definitions from data dictionary
                feature_definitions = await self._get_feature_definitions(db)
                
                # Run interpretation pipeline
                interpreted_segments = await self._interpret_segments(
                    result,
                    llm_service,
                    llm_model_id,
                    feature_definitions
                )
                
                # Add interpretations to result
                result["interpretations"] = interpreted_segments
                result["interpretation_metadata"] = {
                    "interpreted_at": datetime.utcnow().isoformat(),
                    "llm_model_id": llm_model_id,
                    "features_with_definitions": len(feature_definitions)
                }
                
                # Regenerate tree visualization with interpretations if tree exists
                if '_tree_root' in result:
                    try:
                        from app.utils.tree_segmentation_viz import generate_tree_image
                        import base64
                        
                        # Generate tree in multiple formats with interpretations
                        tree_visualizations = {}
                        
                        # Generate PNG
                        try:
                            png_bytes = generate_tree_image(
                                result['_tree_root'], 
                                segment_interpretations=interpreted_segments,
                                format='png'
                            )
                            tree_visualizations['png'] = base64.b64encode(png_bytes).decode('utf-8')
                        except Exception as e:
                            logger.warning(f"Failed to generate PNG with interpretations: {str(e)}")
                        
                        # Generate SVG
                        try:
                            svg_bytes = generate_tree_image(
                                result['_tree_root'], 
                                segment_interpretations=interpreted_segments,
                                format='svg'
                            )
                            tree_visualizations['svg'] = base64.b64encode(svg_bytes).decode('utf-8')
                        except Exception as e:
                            logger.warning(f"Failed to generate SVG with interpretations: {str(e)}")
                        
                        if tree_visualizations:
                            result['tree_visualization'] = {
                                'formats': tree_visualizations,
                                'default_format': 'png' if 'png' in tree_visualizations else 'svg',
                                'has_interpretations': True
                            }
                            logger.info(f"Regenerated tree visualization with interpretations in formats: {list(tree_visualizations.keys())}")
                    except Exception as e:
                        logger.error(f"Error regenerating tree visualization: {str(e)}")
                
            except Exception as e:
                logger.error(f"Error during interpretation: {str(e)}")
                # Don't fail the entire analysis if interpretation fails
                result["interpretation_error"] = str(e)
        
        # Remove internal fields before returning
        if '_tree_root' in result:
            del result['_tree_root']
        
        return result

    async def _get_feature_definitions_for_segments(self, segments: List[Dict[str, Any]]) -> Dict[str, Dict[str, str]]:
        """Get feature definitions for features used in segments."""
        # Extract unique feature names from all segments
        feature_names = set()
        for segment in segments:
            for char in segment.get("characteristics", []):
                feature = char.get("feature", "")
                # Extract base feature name
                base_feature = feature.replace("high_", "").replace("low_", "")
                feature_names.add(base_feature)
        
        # Get definitions from the in-memory definitions (temporary solution)
        # In production, this should query the database
        from app.utils.populate_data_dictionary import FEATURE_DEFINITIONS
        
        definitions = {}
        for feature_def in FEATURE_DEFINITIONS:
            if feature_def["feature_name"] in feature_names:
                definitions[feature_def["feature_name"]] = {
                    "marketing_definition": feature_def.get("marketing_definition", ""),
                    "technical_definition": feature_def.get("technical_definition", ""),
                    "higher_presence_desc": feature_def.get("higher_presence_desc", ""),
                    "lower_presence_desc": feature_def.get("lower_presence_desc", ""),
                    "category": feature_def.get("category", ""),
                    "unit": feature_def.get("unit", "")
                }
        
        return definitions
    
    async def _get_feature_definitions(self, db: AsyncSession) -> Dict[str, Dict[str, str]]:
        """Get feature definitions from the data dictionary."""
        try:
            query = select(DataDictionary)
            result = await db.execute(query)
            entries = result.scalars().all()
            
            definitions = {}
            for entry in entries:
                definitions[entry.feature_name] = {
                    "marketing_definition": entry.marketing_definition,
                    "technical_definition": entry.technical_definition or "",
                    "higher_presence_desc": entry.higher_presence_desc or "",
                    "lower_presence_desc": entry.lower_presence_desc or "",
                    "category": entry.category or "",
                    "unit": entry.unit or ""  # Convert None to empty string
                }
            
            logger.info(f"Loaded {len(definitions)} feature definitions from data dictionary")
            return definitions
            
        except Exception as e:
            logger.error(f"Error loading feature definitions: {str(e)}")
            return {}

    async def _interpret_segments(
        self,
        segmentation_result: Dict[str, Any],
        llm_service,
        llm_model_id: str,
        feature_definitions: Dict[str, Dict[str, str]]
    ) -> Dict[str, InterpretedSegment]:
        """Run the interpretation pipeline on segments."""
        interpreted_segments = {}
        
        # Extract segments from result
        segments_data = segmentation_result.get("segments", {}).get("segments", {})
        
        if not segments_data:
            logger.warning("No segments found to interpret")
            return interpreted_segments
        
        # Prepare segments for interpretation
        segments_for_interpretation = []
        for segment_name, segment_info in segments_data.items():
            # Convert characteristics to expected format
            characteristics = []
            for char in segment_info.get("characteristics", []):
                if isinstance(char, dict) and "feature" in char:
                    characteristics.append(char)
                else:
                    # Handle legacy format
                    logger.warning(f"Unexpected characteristic format: {char}")
            
            segments_for_interpretation.append({
                "name": segment_name,
                "characteristics": characteristics,
                "count": segment_info.get("count", 0)
            })
            
            logger.debug(f"Segment {segment_name} prepared with {len(characteristics)} characteristics")
        
        # 1. Interpret feature names
        interpretation_agent = SegmentationInterpretationAgent(llm_service, llm_model_id)
        interpretation_input = SegmentInterpretationInput(
            segments=segments_for_interpretation,
            feature_definitions=feature_definitions
        )
        interpretation_output = await interpretation_agent.process(interpretation_input)
        
        # 2. Generate descriptions
        description_agent = SegmentationDescriptionAgent(llm_service, llm_model_id)
        
        # Get feature definitions from data dictionary
        feature_definitions = await self._get_feature_definitions_for_segments(segments_for_interpretation)
        
        # Prepare segments with both interpreted features and characteristics
        segments_for_description = {}
        for segment in segments_for_interpretation:
            segment_name = segment["name"]
            segments_for_description[segment_name] = {
                "interpreted_features": interpretation_output.interpreted_segments.get(segment_name, {}),
                "segment_characteristics": segment["characteristics"],
                "size": segment.get("count", 0),
                "percentage": segment.get("count", 0) / segmentation_result.get("metrics", {}).get("total_samples", 1) if segmentation_result.get("metrics", {}).get("total_samples", 1) > 0 else 0,
                "metrics": {},  # Add any available metrics here
                "feature_definitions": feature_definitions  # Add feature definitions
            }
        
        description_input = SegmentDescriptionInput(
            segments=segments_for_description,
            include_metrics=True
        )
        description_output = await description_agent.process(description_input)
        
        # 3. Create marketing titles
        titling_agent = SegmentationTitlingAgent(llm_service, llm_model_id)
        titling_input = SegmentTitlingInput(segments=segments_for_description)
        titling_output = await titling_agent.process(titling_input)
        
        # 4. Verify titles
        verification_agent = SegmentationVerificationAgent(llm_service, llm_model_id)
        segments_to_verify = {}
        for segment_name, title in titling_output.segment_titles.items():
            segments_to_verify[segment_name] = {
                "title": title,
                "description": description_output.segment_descriptions.get(segment_name, "")
            }
        
        verification_input = SegmentVerificationInput(segments=segments_to_verify)
        verification_output = await verification_agent.process(verification_input)
        
        # 5. Compile final interpreted segments
        for segment_name, segment_info in segments_data.items():
            # Get interpretation results
            interpretation = interpretation_output.interpreted_segments.get(segment_name, {})
            description = description_output.segment_descriptions.get(segment_name, "")
            title = titling_output.segment_titles.get(segment_name, segment_name)
            verification = verification_output.verified_segments.get(segment_name)
            
            # Use verified title if available
            if verification and not verification.is_valid and verification.proposed_title:
                final_title = verification.proposed_title
            else:
                final_title = title
            
            # Build interpreted characteristics
            interpreted_chars = []
            for char in segment_info.get("characteristics", []):
                feature = char.get("feature", "")
                base_feature = feature.replace("high_", "").replace("low_", "")
                interpreted_name = interpretation.get("interpreted_features", {}).get(base_feature, feature)
                
                interpreted_chars.append(InterpretedCharacteristic(
                    feature=feature,
                    operator=char.get("operator", "=="),
                    value=char.get("value"),
                    interpreted_name=interpreted_name,
                    description=feature_definitions.get(base_feature, {}).get("marketing_definition")
                ))
            
            # Create interpreted segment
            interpreted_segment = InterpretedSegment(
                technical_name=segment_name,
                marketing_title=final_title,
                description=description,
                characteristics=interpreted_chars,
                size=segment_info.get("count", 0),
                percentage=segment_info.get("count", 0) / segmentation_result.get("metrics", {}).get("total_samples", 1),
                metrics=segment_info.get("metrics", {}),
                actionable_insights=self._generate_actionable_insights(segment_info, interpretation),
                verification_status={
                    "is_valid": verification.is_valid if verification else True,
                    "reasoning": verification.reasoning if verification and not verification.is_valid else None,
                    "original_title": title if verification and not verification.is_valid else None
                }
            )
            
            interpreted_segments[segment_name] = interpreted_segment.dict()
        
        return interpreted_segments

    def _generate_actionable_insights(self, segment_info: Dict[str, Any], interpretation: Dict[str, Any]) -> List[str]:
        """Generate actionable insights for a segment based on its characteristics."""
        insights = []
        characteristics = segment_info.get("characteristics", [])
        size = segment_info.get("count", 0)
        percentage = segment_info.get("percentage", 0.0)
        
        # Analyze all characteristics to understand the segment profile
        feature_map = {}
        for char in characteristics:
            feature = char.get("feature", "")
            operator = char.get("operator", "")
            value = char.get("value", "")
            
            # Extract base feature name
            base_feature = feature.replace("high_", "").replace("low_", "")
            feature_map[base_feature] = {
                "is_high": feature.startswith("high_"),
                "is_low": feature.startswith("low_"),
                "operator": operator,
                "value": value,
                "original": feature
            }
        
        # Generate insights based on specific feature combinations
        
        # Contract-based insights
        if "Contract" in feature_map:
            contract_value = str(feature_map["Contract"]["value"]).lower()
            if "month-to-month" in contract_value or "month to month" in contract_value:
                insights.append("Offer incentives to switch to annual contracts for improved retention")
                insights.append("Implement proactive outreach programs to prevent month-to-month churn")
            elif "two year" in contract_value or "two-year" in contract_value:
                insights.append("Reward loyalty with exclusive benefits for long-term contract holders")
                insights.append("Create VIP support channels for committed customers")
            elif "one year" in contract_value:
                insights.append("Target with two-year contract upgrades before renewal period")
        
        # Multiple Lines insights
        if "Multiple Lines" in feature_map:
            lines_value = str(feature_map["Multiple Lines"]["value"]).lower()
            if "yes" in lines_value:
                insights.append("Promote family plan discounts and shared data benefits")
                insights.append("Highlight multi-line security features and parental controls")
            elif "no" in lines_value:
                insights.append("Target with 'add a line' promotions and family plan benefits")
                insights.append("Emphasize cost savings of bundling multiple lines")
        
        # Tenure-based insights
        if "Tenure Months" in feature_map:
            if feature_map["Tenure Months"]["is_low"]:
                insights.append("Focus on onboarding experience and early engagement programs")
                insights.append("Implement 90-day satisfaction guarantee to build confidence")
            elif feature_map["Tenure Months"]["is_high"]:
                insights.append("Recognize loyalty with anniversary rewards and exclusive perks")
                insights.append("Leverage as brand ambassadors for referral programs")
        
        # Monthly Charges insights
        if "Monthly Charges" in feature_map:
            if feature_map["Monthly Charges"]["is_high"]:
                insights.append("Ensure premium service experience matches premium pricing")
                insights.append("Offer bundled services to increase perceived value")
            elif feature_map["Monthly Charges"]["is_low"]:
                insights.append("Focus on usage-based upselling opportunities")
                insights.append("Highlight value-added services within budget constraints")
        
        # Senior Citizen insights
        if "Senior Citizen" in feature_map:
            senior_value = str(feature_map["Senior Citizen"]["value"]).lower()
            if "yes" in senior_value or senior_value == "1":
                insights.append("Provide specialized senior support with simplified interfaces")
                insights.append("Offer senior discounts and healthcare-related service bundles")
            else:
                insights.append("Target with technology-forward features and digital services")
                insights.append("Emphasize mobile app capabilities and self-service options")
        
        # Internet Service insights
        if "Internet Service" in feature_map:
            service_value = str(feature_map["Internet Service"]["value"]).lower()
            if "fiber" in service_value:
                insights.append("Highlight premium streaming and gaming capabilities")
                insights.append("Promote smart home integration possibilities")
            elif "dsl" in service_value:
                insights.append("Target for fiber upgrade with speed comparison demos")
                insights.append("Address satisfaction with current speeds before competitors do")
        
        # Payment Method insights
        if "Payment Method" in feature_map:
            payment_value = str(feature_map["Payment Method"]["value"]).lower()
            if "electronic check" in payment_value or "bank transfer" in payment_value:
                insights.append("Reward automated payment setup with monthly discounts")
            elif "mailed check" in payment_value:
                insights.append("Encourage digital payment adoption with incentives")
                insights.append("Provide easy transition support to automated payments")
        
        # Size-based insights
        # Check if percentage is already multiplied by 100
        if percentage > 1:
            # Already a percentage (e.g., 15 for 15%)
            if percentage > 15:  # More than 15% of customers
                insights.append(f"This significant segment ({percentage:.1f}%) warrants dedicated marketing campaigns")
            elif percentage < 5:  # Less than 5%
                insights.append(f"Consider personalized retention strategies for this niche segment")
        else:
            # Decimal format (e.g., 0.15 for 15%)
            if percentage > 0.15:  # More than 15% of customers
                insights.append(f"This significant segment ({percentage:.1%}) warrants dedicated marketing campaigns")
            elif percentage < 0.05:  # Less than 5%
                insights.append(f"Consider personalized retention strategies for this niche segment")
        
        # Ensure we have at least one insight
        if not insights:
            insights.append("Develop targeted retention strategies based on segment characteristics")
            insights.append("Monitor segment behavior patterns for early churn indicators")
        
        # Return top 5 most relevant insights (increased from 3)
        return insights[:5]

    def _get_numerical_columns(self, df: pd.DataFrame) -> List[str]:
        """Get numerical columns from DataFrame"""
        return df.select_dtypes(include=['number']).columns.tolist()

    def _segment_customers(
            self,
            df: pd.DataFrame,
            numerical_columns: List[str],
            category_col: Optional[str] = None,
            top: int = 3,
            method: str = "tree",
            min_prospects_percent: float = 0.05,
            max_depth: int = 3,
            reuse_features: bool = False,
            target_column: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Segment customers using various methods
        """
        logger.info(f"Segmenting customers using method: {method}")

        # If method is tree, use tree-based segmentation
        if method == "tree":
            return self._segment_by_tree(
                df,
                numerical_columns,
                min_prospects_percent,
                max_depth,
                reuse_features,
                target_column
            )

    def _segment_by_tree_hyperlocal(
            self,
            df: pd.DataFrame,
            min_prospects: int,
            reuse_features: bool = False,
            drop_columns: List[str] = None
    ) -> Dict[str, Any]:
        """
        Segment data using hyperlocal tree approach.

        Args:
            df: DataFrame to segment
            min_prospects: Minimum number of prospects in each segment
            reuse_features: Whether to allow reusing features
            drop_columns: Columns to exclude from segmentation
        
        Returns:
            Segmentation results
        """
        from app.utils.tree_segmentation import segment_data_with_tree
        
        return segment_data_with_tree(
            df=df,
            min_prospects=min_prospects,
            reuse_features=reuse_features,
            drop_columns=drop_columns
        )
    
    def _segment_by_tree(
            self,
            df: pd.DataFrame,
            feature_columns: List[str],
            min_prospects_percent: float = 0.05,
            max_depth: int = 3,
            max_segments: int = 10,
            reuse_features: bool = False,
            target_column: str = None
    ) -> Dict[str, Any]:
        """
        Legacy segment data using a decision tree approach.
        This now calls the hyperlocal version internally.

        Args:
            df: DataFrame to segment
            feature_columns: List of feature columns to use for segmentation
            min_prospects_percent: Minimum percentage of prospects in each leaf
            max_depth: Maximum tree depth (ignored)
            reuse_features: Whether to allow reusing features
            target_column: Column to use as target (Prospect)

        Returns:
            Dictionary with segmentation results
        """
        # Filter out non-feature columns
        excluded_columns = ['row_index', 'row index', 'Prospect', 'location_count']
        feature_columns = [col for col in feature_columns if col not in excluded_columns]
        
        # Further filter to only include numeric columns for tree-based segmentation
        numeric_feature_columns = []
        for col in feature_columns:
            if col in df.columns:
                # Check if column is numeric
                if pd.api.types.is_numeric_dtype(df[col]):
                    numeric_feature_columns.append(col)
                else:
                    logger.info(f"Excluding non-numeric column from tree segmentation: {col} (dtype: {df[col].dtype})")
            else:
                logger.warning(f"Feature column '{col}' not found in dataframe")
        
        feature_columns = numeric_feature_columns

        # Log the feature columns being used
        logger.info(f"Using {len(feature_columns)} numeric feature columns for tree segmentation: {feature_columns}")
        
        # Check if we have any numeric features left
        if not feature_columns:
            logger.error("No numeric feature columns available for tree segmentation")
            raise ValueError("No numeric features available for tree-based segmentation. Tree segmentation requires numeric features.")

        # Ensure row_index is present but not used as a feature
        if 'row_index' not in df.columns:
            df['row_index'] = range(len(df))

        # Log the parameters being used
        logger.info(f"Segmentation parameters: min_prospects_percent={min_prospects_percent}, max_depth={max_depth}")
        logger.info(f"DataFrame shape: {df.shape}")

        # Import tree segmentation utilities
        from app.utils.tree_segmentation import segment_data_with_tree_legacy

        # Use the legacy function that maintains backward compatibility
        segmentation_result = segment_data_with_tree_legacy(
            df=df,
            feature_columns=feature_columns,
            min_prospects_percent=min_prospects_percent,
            max_depth=max_depth,
            max_segments=max_segments,
            reuse_features=reuse_features,
            target_column=target_column
        )

        return segmentation_result

    def _calculate_segment_metrics(
            self,
            df: pd.DataFrame,
            segments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Calculate metrics for all segments.

        Args:
            df: DataFrame with customer data
            segments: Segmentation results

        Returns:
            Dictionary with metrics for each segment
        """
        metrics = {
            "total_samples": len(df)  # Add total samples count
        }

        # For tree-based segmentation
        if segments["method"] == "tree_based":
            # Check if we have the expected structure
            if "segments" in segments:
                for segment_name, segment_info in segments["segments"].items():
                    # Use the pre-computed segment data if available
                    if "data" in segment_info:
                        segment_data = segment_info["data"]
                        if isinstance(segment_data, pd.DataFrame):
                            segment_df = segment_data
                            logger.info(f"Using pre-computed DataFrame for {segment_name} with {len(segment_df)} rows")
                        else:
                            logger.info(f"Segment data for {segment_name} is not a DataFrame, type: {type(segment_data)}")
                            # Fallback to recreating from characteristics
                            segment_df = None
                    else:
                        logger.info(f"No 'data' field found for {segment_name}")
                        segment_df = None
                        
                    if segment_df is None:
                        # Fallback: recreate segment from characteristics
                        logger.info(f"Recreating segment {segment_name} from characteristics")
                        mask = pd.Series(True, index=df.index)

                        # Check if characteristics exist in the segment info
                        if "characteristics" in segment_info:
                            for char in segment_info["characteristics"]:
                                # Handle both string format and dict format
                                if isinstance(char, dict):
                                    feature = char["feature"]
                                    value = char["value"]
                                else:  # String format like "high_feature: value"
                                    parts = char.split(":")
                                    feature = parts[0].strip()
                                    value = float(parts[1].strip()) if len(parts) > 1 else 0

                                # Parse the feature name to determine the condition
                                if feature.startswith("high_"):
                                    feat_name = feature[5:]  # Remove "high_" prefix
                                    if feat_name in df.columns:
                                        mask &= (df[feat_name] >= value)
                                        logger.debug(f"Applied condition: {feat_name} >= {value}")
                                    else:
                                        logger.warning(f"Feature {feat_name} not found in dataframe columns: {list(df.columns)[:10]}...")
                                elif feature.startswith("low_"):
                                    feat_name = feature[4:]  # Remove "low_" prefix
                                    if feat_name in df.columns:
                                        mask &= (df[feat_name] < value)
                                        logger.debug(f"Applied condition: {feat_name} < {value}")
                                    else:
                                        logger.warning(f"Feature {feat_name} not found in dataframe columns: {list(df.columns)[:10]}...")
                                else:
                                    # Direct comparison for categorical features
                                    if feature in df.columns:
                                        mask &= (df[feature] == value)
                                        logger.debug(f"Applied condition: {feature} == {value}")
                                    else:
                                        logger.warning(f"Feature {feature} not found in dataframe columns: {list(df.columns)[:10]}...")

                        segment_df = df[mask]
                        logger.info(f"Recreated segment {segment_name} with {len(segment_df)} rows from {len(df)} total rows")
                    
                    # Compute metrics for this segment
                    metrics[segment_name] = self._compute_segment_metrics(segment_df, df)
            else:
                logger.warning("Segments dictionary does not contain 'segments' key")
        elif segments["method"] == "category":
            # For category-based segmentation
            category_col = segments["category_column"]
            for segment, count in segments["segment_counts"].items():
                segment_df = df[df[category_col] == segment]
                metrics[segment] = self._compute_segment_metrics(segment_df, df)
        else:
            logger.warning(f"Unknown segmentation method: {segments['method']}")

        # Convert NumPy types to Python native types
        from app.utils.serialization import convert_numpy_types
        return convert_numpy_types(metrics)

    def _compute_segment_metrics(self, segment_df: pd.DataFrame, full_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Compute metrics for a single segment.

        Args:
            segment_df: DataFrame with segment data
            full_df: Full DataFrame with all customers

        Returns:
            Dictionary with segment metrics
        """
        # Skip if segment is empty
        if len(segment_df) == 0:
            return {"size": 0, "size_percent": 0}

        # Basic metrics
        metrics = {
            "size": int(len(segment_df)),  # Convert to int to avoid numpy.int64
            "size_percent": float(round(len(segment_df) / len(full_df) * 100, 2))  # Convert to float
        }

        # Calculate metrics for numerical columns
        numerical_cols = segment_df.select_dtypes(include=['number']).columns
        for col in numerical_cols:
            # Skip special columns
            if col in ['row_index', 'location_count', 'Prospect']:
                continue
                
            # Calculate statistics
            segment_mean = segment_df[col].mean()
            segment_median = segment_df[col].median()
            segment_std = segment_df[col].std()
            overall_mean = full_df[col].mean()
            overall_median = full_df[col].median()
            overall_std = full_df[col].std()

            # Only include if not NaN
            if not pd.isna(segment_mean) and not pd.isna(overall_mean):
                metrics[col] = {
                    "type": "numerical",
                    "mean": float(round(segment_mean, 2)),
                    "median": float(round(segment_median, 2)),
                    "std": float(round(segment_std, 2)) if not pd.isna(segment_std) else 0,
                    "overall_mean": float(round(overall_mean, 2)),
                    "overall_median": float(round(overall_median, 2)),
                    "overall_std": float(round(overall_std, 2)) if not pd.isna(overall_std) else 0,
                    "difference": float(round(segment_mean - overall_mean, 2)),
                    "percent_difference": float(
                        round((segment_mean / overall_mean - 1) * 100, 2) if overall_mean != 0 else 0
                    )
                }

        # Calculate metrics for categorical columns
        categorical_cols = segment_df.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            # Get value counts
            segment_value_counts = segment_df[col].value_counts()
            overall_value_counts = full_df[col].value_counts()
            
            # Get most common value (mode)
            segment_mode = segment_value_counts.index[0] if len(segment_value_counts) > 0 else None
            overall_mode = overall_value_counts.index[0] if len(overall_value_counts) > 0 else None
            
            # Calculate percentages for the mode
            segment_mode_pct = (segment_value_counts[segment_mode] / len(segment_df) * 100) if segment_mode else 0
            overall_mode_pct = (overall_value_counts[overall_mode] / len(full_df) * 100) if overall_mode else 0

            metrics[col] = {
                "type": "categorical",
                "mode": str(segment_mode) if segment_mode else None,
                "mode_count": int(segment_value_counts[segment_mode]) if segment_mode else 0,
                "mode_percentage": float(round(segment_mode_pct, 2)),
                "overall_mode": str(overall_mode) if overall_mode else None,
                "overall_mode_percentage": float(round(overall_mode_pct, 2)),
                "unique_values": int(segment_df[col].nunique()),
                "categories": {}
            }
            
            # Store top categories with their percentages
            for category in segment_value_counts.index[:5]:  # Top 5 categories
                if pd.isna(category) or category is None:
                    continue
                    
                segment_cat_pct = segment_value_counts[category] / len(segment_df) * 100
                overall_cat_pct = (overall_value_counts.get(category, 0) / len(full_df) * 100) if category in overall_value_counts else 0
                
                metrics[col]["categories"][str(category)] = {
                    "segment_percentage": float(round(segment_cat_pct, 2)),
                    "overall_percentage": float(round(overall_cat_pct, 2)),
                    "difference": float(round(segment_cat_pct - overall_cat_pct, 2))
                }

        return metrics

    def _prepare_visualization_data(
            self,
            df: pd.DataFrame,
            segments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Prepare data for visualizations.

        Args:
            df: DataFrame with customer data
            segments: Segmentation results

        Returns:
            Dictionary with visualization data
        """
        visualization_data = {
            "segment_sizes": {},
            "feature_comparison": {},
            "segment_radar": {}
        }

        # Segment sizes for pie chart
        if segments["method"] == "tree_based":
            # Check if we have the expected structure
            if "segments" in segments:
                # Extract segment counts
                segment_counts = {}
                for segment_name, segment_info in segments["segments"].items():
                    if "prospect_count" in segment_info:
                        segment_counts[segment_name] = int(segment_info["prospect_count"])
                    elif "count" in segment_info:
                        segment_counts[segment_name] = int(segment_info["count"])
                    else:
                        # Fallback to data length if available
                        segment_counts[segment_name] = int(len(segment_info.get("data", [])))

                visualization_data["segment_sizes"] = segment_counts
            else:
                # Fallback to segment_counts if available
                if "segment_counts" in segments:
                    visualization_data["segment_sizes"] = {k: int(v) for k, v in segments["segment_counts"].items()}
                else:
                    logger.warning("Segments dictionary does not contain expected keys for segment sizes")
        elif segments["method"] == "category":
            # Convert to Python dict with native types
            visualization_data["segment_sizes"] = {k: int(v) for k, v in segments["segment_counts"].items()}
        else:
            logger.warning(f"Unknown segmentation method: {segments['method']}")

        # Feature comparison for bar charts
        numerical_cols = df.select_dtypes(include=['number']).columns
        for col in numerical_cols:
            if segments["method"] == "tree_based":
                # For tree-based segmentation, calculate mean values for each segment
                feature_data = {}

                # Check if we have the expected structure
                if "segments" in segments:
                    for segment_name, segment_info in segments["segments"].items():
                        # Create a mask to filter the dataframe based on segment characteristics
                        mask = pd.Series(True, index=df.index)

                        # Check if characteristics exist in the segment info
                        if "characteristics" in segment_info:
                            for char in segment_info["characteristics"]:
                                # Handle both string format and dict format
                                if isinstance(char, dict):
                                    feature = char["feature"]
                                    value = char["value"]
                                else:  # String format like "high_feature: value"
                                    parts = char.split(":")
                                    feature = parts[0].strip()
                                    value = float(parts[1].strip()) if len(parts) > 1 else 0

                                # Parse the feature name to determine the condition
                                if feature.startswith("high_"):
                                    feat_name = feature[5:]  # Remove "high_" prefix
                                    mask &= (df[feat_name] >= value)
                                elif feature.startswith("low_"):
                                    feat_name = feature[4:]  # Remove "low_" prefix
                                    mask &= (df[feat_name] < value)
                                else:
                                    # Direct comparison for categorical features
                                    mask &= (df[feature] == value)

                        segment_df = df[mask]
                        feature_data[segment_name] = float(segment_df[col].mean())  # Convert to float
                else:
                    logger.warning("Segments dictionary does not contain 'segments' key for feature comparison")

                visualization_data["feature_comparison"][col] = feature_data
            elif segments["method"] == "category":
                category_col = segments["category_column"]
                feature_data = df.groupby(category_col)[col].mean().to_dict()
                # Convert to Python dict with native types
                visualization_data["feature_comparison"][col] = {k: float(v) for k, v in feature_data.items()}

        # Radar chart data using top features from segmentation
        if segments["method"] == "tree_based":
            # Use top features from segment characteristics
            radar_data = {}

            # Check if we have the expected structure
            if "segments" in segments:
                for segment_name, segment_info in segments["segments"].items():
                    # Check if characteristics exist in the segment info
                    if "characteristics" in segment_info:
                        # Handle both string format and dict format
                        features_dict = {}
                        for char in segment_info["characteristics"][:5]:  # Top 5 features
                            if isinstance(char, dict):
                                feature = char["feature"]
                                value = float(char["value"])
                            else:  # String format like "high_feature: value"
                                parts = char.split(":")
                                feature = parts[0].strip()
                                value = float(parts[1].strip()) if len(parts) > 1 else 0

                            features_dict[feature] = value

                        radar_data[segment_name] = features_dict
            else:
                logger.warning("Segments dictionary does not contain 'segments' key for radar chart")

            visualization_data["segment_radar"] = radar_data
        elif segments["method"] == "category":
            # Use top features from segment characteristics
            radar_data = {}
            for segment, features in segments["segment_characteristics"].items():
                radar_data[segment] = {f["feature"]: float(f["value"]) for f in features[:5]}  # Convert to float
            visualization_data["segment_radar"] = radar_data

        # Convert any remaining NumPy types
        from app.utils.serialization import convert_numpy_types
        return convert_numpy_types(visualization_data)

    async def _load_full_dataset(self, db: AsyncSession, model_id: int, version_id: int) -> Optional[pd.DataFrame]:
        """
        Load the full dataset used for training the model.
        
        Args:
            db: Database session
            model_id: Model ID
            version_id: Version ID
            
        Returns:
            DataFrame with full dataset or None if not available
        """
        try:
            # Get model version from database
            from app.db.models.model import ModelVersion
            from app.db.models.dataset import Dataset
            
            # Get model version
            query = select(ModelVersion).where(
                ModelVersion.model_id == model_id,
                ModelVersion.id == version_id
            )
            result = await db.execute(query)
            version = result.scalar_one_or_none()
            
            if not version:
                logger.error(f"Model version not found: model_id={model_id}, version_id={version_id}")
                return None
                
            # Get dataset
            query = select(Dataset).where(Dataset.id == version.dataset_id)
            result = await db.execute(query)
            dataset = result.scalar_one_or_none()
            
            if not dataset:
                logger.error(f"Dataset not found: dataset_id={version.dataset_id}")
                return None
                
            # Load dataset from file or database
            if dataset.file_path and os.path.exists(dataset.file_path):
                logger.info(f"Loading full dataset from file: {dataset.file_path}")
                try:
                    df = pd.read_csv(dataset.file_path)
                    logger.info(f"Loaded full dataset with shape: {df.shape}")
                    
                    # Log column types to help debug
                    logger.info(f"Column dtypes: {df.dtypes.to_dict()}")
                    
                    # Check for any problematic values in categorical columns
                    for col in df.columns:
                        if df[col].dtype == 'object':
                            unique_values = df[col].unique()
                            if any(isinstance(val, str) and len(val) > 100 for val in unique_values):
                                logger.warning(f"Column '{col}' contains very long string values, sample: {unique_values[0][:100]}...")
                    
                    # Only keep the features used by the model
                    if version.features:
                        available_features = [col for col in version.features if col in df.columns]
                        df = df[available_features]
                        logger.info(f"Filtered to model features, new shape: {df.shape}")
                        
                    return df
                except Exception as e:
                    logger.error(f"Error reading CSV file {dataset.file_path}: {str(e)}")
                    raise
            elif dataset.connection_id:
                # Dataset is from a database connection
                logger.info(f"Loading dataset from database connection: {dataset.connection_id}")
                from app.services.database_dataset_service import DatabaseDatasetService
                
                db_service = DatabaseDatasetService()
                df = await db_service.preview_dataset(
                    db=db,
                    dataset_id=dataset.id,
                    user_id=version.user_id,
                    limit=None  # Get all rows
                )
                
                if df is not None and not df.empty:
                    logger.info(f"Loaded dataset from database with shape: {df.shape}")
                    # Only keep the features used by the model
                    if version.features:
                        available_features = [col for col in version.features if col in df.columns]
                        df = df[available_features]
                        logger.info(f"Filtered to model features, new shape: {df.shape}")
                    return df
                else:
                    logger.warning("Failed to load dataset from database connection")
                    return None
            else:
                logger.warning(f"Dataset has no file path or connection: dataset_id={dataset.id}")
                return None
                
        except Exception as e:
            logger.error(f"Error loading full dataset: {str(e)}", exc_info=True)
            return None

    async def _load_model_features(self, db: AsyncSession, model_id: int, version_id: int) -> List[str]:
        """
        Load the original features used by the model.

        Args:
            db: Database session
            model_id: Model ID
            version_id: Version ID

        Returns:
            List of feature names used by the model
        """
        try:
            # Get model version from database
            from app.db.models.model import ModelVersion

            query = select(ModelVersion).where(
                ModelVersion.model_id == model_id,
                ModelVersion.id == version_id
            )
            result = await db.execute(query)
            model_version = result.scalars().first()

            if not model_version:
                logger.warning(f"Model version {version_id} not found for model {model_id}")
                return []

            # Get features from model version
            features = []
            if hasattr(model_version, 'features') and model_version.features:
                features = model_version.features
            elif hasattr(model_version, 'metadata') and model_version.metadata:
                # Try to get features from metadata
                metadata = model_version.metadata
                if isinstance(metadata, dict) and 'features' in metadata:
                    features = metadata['features']

            logger.info(f"Loaded {len(features)} features from model version: {features}")
            return features
        except Exception as e:
            logger.error(f"Error loading model features: {str(e)}")
            return []

    async def _load_shap_data(self, db: AsyncSession, model_id: int, version_id: int) -> pd.DataFrame:
        """
        Load SHAP data from database or CSV file.

        Args:
            db: Database session
            model_id: ID of the model
            version_id: ID of the model version

        Returns:
            DataFrame containing SHAP data
        """
        # Try to load from database first
        try:
            from app.services.shap_service.core import get_shap_values_from_db

            logger.info(f"Attempting to load SHAP data from database for model {model_id}, version {version_id}")

            shap_records = await get_shap_values_from_db(db, model_id, version_id)

            if shap_records:
                logger.info(f"Found {len(shap_records)} SHAP records in database")

                # Convert to DataFrame
                rows = []
                for record in shap_records:
                    row = {"row_index": record["row_index"]}

                    # Add SHAP values with _shap suffix
                    for feature, value in record["shap_values"].items():
                        row[f"{feature}_shap"] = value

                    # Add original feature values with appropriate suffixes
                    for feature, value in record["feature_values"].items():
                        # Check if feature is encoded based on feature_metadata
                        is_encoded = False
                        if record.get("feature_metadata") and feature in record["feature_metadata"]:
                            is_encoded = record["feature_metadata"][feature].get("is_categorical", False)

                        # Add suffix based on whether feature is encoded
                        if is_encoded:
                            row[f"{feature}_encoded"] = value
                        else:
                            row[f"{feature}_original"] = value

                    rows.append(row)

                df = pd.DataFrame(rows)
                logger.info(f"Created DataFrame from database records with shape: {df.shape}")

                # Extract original feature columns with new naming convention
                original_cols = [col for col in df.columns if col.endswith("_original") or col.endswith("_encoded")]
                if original_cols:
                    # Create a new DataFrame with just the original values
                    original_df = df[["row_index"] + original_cols].copy()

                    # Rename columns to remove suffixes
                    rename_dict = {}
                    for col in original_cols:
                        if col.endswith("_original"):
                            rename_dict[col] = col.replace("_original", "")
                        elif col.endswith("_encoded"):
                            rename_dict[col] = col.replace("_encoded", "")

                    original_df = original_df.rename(columns=rename_dict)

                    logger.info(
                        f"Using original feature values from database with columns: {original_df.columns.tolist()}")

                    # Drop row_index column
                    if 'row_index' in original_df.columns:
                        logger.info("Dropping row_index column from DataFrame")
                        original_df = original_df.drop(columns=['row_index'])
                    if 'row index' in original_df.columns:
                        logger.info("Dropping 'row index' column from DataFrame")
                        original_df = original_df.drop(columns=['row index'])

                    return original_df
                else:
                    # Drop row_index column
                    if 'row_index' in df.columns:
                        logger.info("Dropping row_index column from DataFrame")
                        df = df.drop(columns=['row_index'])
                    if 'row index' in df.columns:
                        logger.info("Dropping 'row index' column from DataFrame")
                        df = df.drop(columns=['row index'])

                    return df
        except Exception as e:
            logger.warning(f"Could not load SHAP data from database: {str(e)}")
            logger.info("Falling back to CSV file")

        # Fall back to CSV file if database loading fails
        # Use the configured upload directory from config
        upload_dir = config.UPLOAD_DIR
        shap_dir = os.path.join(upload_dir, "shap_data")

        logger.info(f"Upload directory from config: {upload_dir}")
        logger.info(f"SHAP data directory: {shap_dir}")

        # Ensure directory exists
        os.makedirs(shap_dir, exist_ok=True)

        # Define the specific file pattern
        file_name = f"shap_data_{model_id}_{version_id}_class_1.csv"
        file_path = os.path.join(shap_dir, file_name)

        # Log the file we're looking for
        logger.info(f"Looking for SHAP data file: {file_path}")

        # Check if the file exists
        if os.path.exists(file_path):
            logger.info(f"Loading SHAP data from {file_path}")
            try:
                df = pd.read_csv(file_path)

                # Filter for original feature columns if they exist
                original_columns = [col for col in df.columns if col.startswith('original_')]

                if original_columns:
                    logger.info(f"Found {len(original_columns)} original feature columns")
                    # Create a new DataFrame with original features but without the prefix
                    original_df = df[['row_index'] + original_columns].copy()
                    # Rename columns to remove 'original_' prefix
                    rename_dict = {col: col.replace('original_', '') for col in original_columns}
                    original_df = original_df.rename(columns=rename_dict)

                    # Add any non-original columns that don't have corresponding original_ versions
                    # (like target variables or metadata)
                    non_shap_cols = [col for col in df.columns if not col.startswith('original_')
                                     and not any(col == c.replace('original_', '') for c in original_columns)]

                    if non_shap_cols:
                        for col in non_shap_cols:
                            if col != 'row_index':  # Skip row_index as we already have it
                                original_df[col] = df[col]

                    logger.info(
                        f"Using original feature values for segmentation with columns: {original_df.columns.tolist()}")

                    # Drop row_index column
                    if 'row_index' in original_df.columns:
                        logger.info("Dropping row_index column from DataFrame")
                        original_df = original_df.drop(columns=['row_index'])
                    if 'row index' in original_df.columns:
                        logger.info("Dropping 'row index' column from DataFrame")
                        original_df = original_df.drop(columns=['row index'])

                    return original_df
                else:
                    logger.info("No original feature columns found, using raw data")

                    # Drop row_index column
                    if 'row_index' in df.columns:
                        logger.info("Dropping row_index column from DataFrame")
                        df = df.drop(columns=['row_index'])
                    if 'row index' in df.columns:
                        logger.info("Dropping 'row index' column from DataFrame")
                        df = df.drop(columns=['row index'])

                    return df
            except Exception as e:
                logger.error(f"Error loading SHAP data from {file_path}: {str(e)}")
                raise ValueError(f"Error loading SHAP data from {file_path}: {str(e)}")
        else:
            # List files in directory for debugging
            try:
                files_in_dir = os.listdir(shap_dir)
                logger.error(f"File {file_name} not found. Files in directory: {files_in_dir}")
            except Exception as e:
                logger.error(f"Error listing files in directory {shap_dir}: {str(e)}")

            # Raise error with specific file information
            raise ValueError(f"SHAP data file {file_name} not found in directory {shap_dir}")

    def _ensure_serializable(self, obj: Any) -> Any:
        """
        Ensure all objects are serializable by converting any non-serializable objects
        to their string representation.

        Args:
            obj: Object to make serializable

        Returns:
            Serializable object
        """
        if isinstance(obj, pd.DataFrame):
            # Convert DataFrame to dict
            return obj.to_dict(orient="records")
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return obj.item()
        elif isinstance(obj, dict):
            return {k: self._ensure_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._ensure_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self._ensure_serializable(item) for item in obj)
        elif hasattr(obj, "__dict__"):
            # For custom objects, convert to dict
            return {k: self._ensure_serializable(v) for k, v in obj.__dict__.items()}
        else:
            # For any other non-serializable objects, convert to string
            try:
                import json
                json.dumps(obj)
                return obj
            except (TypeError, OverflowError):
                return str(obj)

    def _calculate_feature_importance(
            self,
            df: pd.DataFrame,
            segments: Dict[str, Any],
            model_id: Optional[int] = None,
            version_id: Optional[int] = None
    ) -> Dict[str, float]:
        """
        Calculate feature importance scores for segmentation.
        
        Args:
            df: DataFrame with data
            segments: Segmentation results
            model_id: Optional model ID to fetch SHAP values
            version_id: Optional version ID to fetch SHAP values
            
        Returns:
            Dictionary of feature importance scores
        """
        feature_importance = {}
        
        # Try to get feature importance from model if available
        if model_id and version_id:
            try:
                from app.db.models import ModelVersion
                from app.db.session import get_db
                from sqlalchemy import select
                
                # This would need to be async in production
                # For now, we'll just use a placeholder
                logger.info(f"Would fetch feature importance for model {model_id} version {version_id}")
            except Exception as e:
                logger.warning(f"Could not fetch model feature importance: {e}")
        
        # Calculate importance based on how well features separate segments
        if "segments" in segments and segments["segments"]:
            numerical_cols = df.select_dtypes(include=['number']).columns
            
            for col in numerical_cols:
                if col in ['row_index', 'location_count', 'Prospect']:
                    continue
                    
                # Calculate variance ratio between segments
                segment_means = []
                for segment_name, segment_info in segments["segments"].items():
                    # Get segment data
                    mask = pd.Series(True, index=df.index)
                    if "characteristics" in segment_info:
                        for char in segment_info["characteristics"]:
                            if isinstance(char, dict):
                                feature = char["feature"]
                                value = char["value"]
                            else:
                                parts = char.split(":")
                                feature = parts[0].strip()
                                value = float(parts[1].strip()) if len(parts) > 1 else 0
                            
                            if feature.startswith("high_"):
                                feat_name = feature[5:]
                                if feat_name in df.columns:
                                    mask &= (df[feat_name] >= value)
                            elif feature.startswith("low_"):
                                feat_name = feature[4:]
                                if feat_name in df.columns:
                                    mask &= (df[feat_name] < value)
                    
                    segment_df = df[mask]
                    if len(segment_df) > 0:
                        segment_means.append(segment_df[col].mean())
                
                # Calculate between-segment variance
                if len(segment_means) > 1:
                    between_var = np.var(segment_means)
                    total_var = df[col].var()
                    if total_var > 0:
                        # Variance ratio as importance score
                        feature_importance[col] = float(between_var / total_var)
                    else:
                        feature_importance[col] = 0.0
                else:
                    feature_importance[col] = 0.0
        
        # Normalize importance scores to sum to 1
        total_importance = sum(feature_importance.values())
        if total_importance > 0:
            feature_importance = {k: v/total_importance for k, v in feature_importance.items()}
        
        return feature_importance

    def _prepare_segment_summary(
            self,
            segments: Dict[str, Any],
            metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Prepare a user-friendly summary of segment characteristics.

        Args:
            segments: Segmentation results
            metrics: Calculated metrics for each segment

        Returns:
            Dictionary with segment summaries
        """
        summaries = {}

        # For tree-based segmentation
        if segments["method"] == "tree_based" and "segments" in segments:
            for segment_name, segment_info in segments["segments"].items():
                # Skip if no metrics available for this segment
                if segment_name not in metrics:
                    continue

                segment_metrics = metrics[segment_name]

                # Get basic segment info
                summary = {
                    "name": segment_name,
                    "count": segment_metrics.get("size", 0),
                    "percent": segment_metrics.get("size_percent", 0),
                    "key_features": []
                }

                # Get tree split order from segment characteristics
                tree_split_order = []
                if "characteristics" in segment_info:
                    for char in segment_info["characteristics"]:
                        if isinstance(char, dict):
                            feature = char.get("feature", "")
                        else:
                            # String format like "high_feature: value"
                            feature = char.split(":")[0].strip()

                        # Extract base feature name
                        base_feature = feature.replace("high_", "").replace("low_", "")
                        if base_feature and base_feature not in tree_split_order:
                            tree_split_order.append(base_feature)

                # Process features from the metrics structure
                feature_list = []
                feature_dict = {}

                for feature_name, feature_data in segment_metrics.items():
                    if feature_name in ["size", "size_percent", "count"]:
                        continue

                    if isinstance(feature_data, dict) and "type" in feature_data:
                        if feature_data["type"] == "numerical":
                            # Extract numerical feature data
                            feature_info = {
                                "name": feature_name,
                                "segment_value": feature_data.get("mean", 0),
                                "overall_value": feature_data.get("overall_mean", 0),
                                "diff": feature_data.get("difference", 0),
                                "diff_percent": feature_data.get("percent_difference", 0),
                                "is_categorical": False
                            }
                            feature_dict[feature_name] = (abs(feature_data.get("percent_difference", 0)), feature_info)
                        elif feature_data["type"] == "categorical":
                            # For categorical features, use the mode
                            if feature_data.get("mode"):
                                feature_info = {
                                    "name": f"{feature_name} = {feature_data['mode']}",
                                    "segment_value": feature_data.get("mode_percentage", 0),
                                    "overall_value": feature_data.get("overall_mode_percentage", 0),
                                    "diff": feature_data.get("mode_percentage", 0) - feature_data.get("overall_mode_percentage", 0),
                                    "diff_percent": 0,
                                    "is_categorical": True
                                }
                                feature_dict[feature_name] = (abs(feature_info["diff"]), feature_info)

                # Order features by tree split order first, then by importance
                ordered_features = []
                
                # First, add features in tree split order
                for feature_name in tree_split_order:
                    if feature_name in feature_dict:
                        ordered_features.append(feature_dict[feature_name])
                        del feature_dict[feature_name]

                # Then add remaining features sorted by importance
                remaining_features = list(feature_dict.values())
                remaining_features.sort(key=lambda x: x[0], reverse=True)
                ordered_features.extend(remaining_features)

                # Take top 10 features and extract the feature info
                for _, feature_info in ordered_features[:10]:
                    summary["key_features"].append(feature_info)

                summaries[segment_name] = summary
        elif segments["method"] == "category":
            # Similar logic for category-based segmentation
            for segment_name, segment_metrics in metrics.items():
                # Get basic segment info
                summary = {
                    "name": segment_name,
                    "count": segment_metrics.get("size", 0),
                    "percent": segment_metrics.get("size_percent", 0),
                    "key_features": []
                }

                summaries[segment_name] = summary

        return summaries

    async def save_segmentation_results(
            self,
            db: AsyncSession,  # Pass db explicitly
            model_id: int,
            version_id: int,
            user_id: int,
            results: Dict[str, Any],
            name: Optional[str] = None,
            segmentation_run_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Save segmentation analysis results to the database.

        Args:
            db: Database session
            model_id: ID of the model
            version_id: ID of the model version
            user_id: ID of the user who ran the analysis
            results: Segmentation analysis results
            name: Optional name for the saved analysis
            segmentation_run_id: Optional run ID that ties to the segmentation parameters

        Returns:
            Saved analysis record with ID
        """
        logger.info(f"Saving segmentation results for model {model_id}, version {version_id}")

        try:
            # Validate input parameters
            if not name or name.strip() == "":
                logger.warning("Empty name provided for segmentation analysis, generating default")
                name = f"Segmentation {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            logger.info(f"Saving analysis with name: '{name}'")
            
            # Validate results structure
            if not isinstance(results, dict):
                logger.error(f"Results is not a dictionary: {type(results)}")
                raise ValueError("Results must be a dictionary")

            # Generate a segmentation_run_id if not provided
            if not segmentation_run_id:
                import uuid
                segmentation_run_id = str(uuid.uuid4())
                logger.info(f"Generated new segmentation_run_id: {segmentation_run_id}")
            else:
                logger.info(f"Using provided segmentation_run_id: {segmentation_run_id}")

            # Extract parameters from results if available
            parameters = results.get("parameters", {})
            
            # Log the structure of results for debugging
            logger.info(f"Results keys: {list(results.keys())}")
            logger.info(f"Parameters: {parameters}")

            # Add segmentation_run_id to parameters
            if parameters:
                parameters["segmentation_run_id"] = segmentation_run_id
            else:
                parameters = {"segmentation_run_id": segmentation_run_id}

            # Create a new analysis record
            try:
                analysis = SegmentationAnalysis(
                    model_id=model_id,
                    version_id=version_id,
                    user_id=user_id,
                    name=name.strip(),
                    parameters=parameters,
                    results=results
                )
                logger.info("Created SegmentationAnalysis instance successfully")
            except Exception as e:
                logger.error(f"Error creating SegmentationAnalysis instance: {str(e)}")
                raise

            # Add to database and commit
            try:
                db.add(analysis)
                logger.info("Added analysis to session")
                await db.commit()
                logger.info("Committed analysis to database")
                await db.refresh(analysis)
                logger.info(f"Refreshed analysis from database, ID: {analysis.id}")
            except Exception as e:
                logger.error(f"Database error during save: {str(e)}")
                raise

            # Convert to dictionary
            result_dict = {
                "id": analysis.id,
                "model_id": analysis.model_id,
                "version_id": analysis.version_id,
                "user_id": analysis.user_id,
                "name": analysis.name,
                "parameters": analysis.parameters,
                "segmentation_run_id": segmentation_run_id,
                "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
                "success": True
            }

            logger.info(
                f"Successfully saved segmentation analysis with ID: {analysis.id} and segmentation_run_id: {segmentation_run_id}")
            return result_dict
        except Exception as e:
            logger.error(f"Error saving segmentation results: {str(e)}", exc_info=True)
            await db.rollback()  # Rollback the transaction in case of error

            # Always return a dictionary even in case of error
            return {
                "error": True,
                "message": f"Error saving segmentation results: {str(e)}",
                "success": False
            }

    async def get_saved_segmentation_analyses(
            self,
            db: AsyncSession,  # Pass db explicitly
            model_id: int,
            version_id: int,
            user_id: Optional[int] = None,
            skip: int = 0,
            limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get saved segmentation analyses for a model version.

        Args:
            db: Database session
            model_id: ID of the model
            version_id: ID of the model version
            user_id: Optional user ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of saved segmentation analyses
        """
        try:
            # Build query
            query = select(SegmentationAnalysis).where(
                SegmentationAnalysis.model_id == model_id,
                SegmentationAnalysis.version_id == version_id
            )

            # Add user filter if provided
            if user_id is not None:
                query = query.where(SegmentationAnalysis.user_id == user_id)

            # Order by creation date, newest first
            query = query.order_by(SegmentationAnalysis.created_at.desc())

            # Add pagination
            query = query.offset(skip).limit(limit)

            # Execute query
            result = await db.execute(query)
            analyses = result.scalars().all()

            # Convert to list of dictionaries
            return [
                {
                    "id": analysis.id,
                    "model_id": analysis.model_id,
                    "version_id": analysis.version_id,
                    "user_id": analysis.user_id,
                    "name": analysis.name,
                    "parameters": {
                        "min_prospects_percent": analysis.parameters.get("min_prospects_percent", 0.05),
                        "max_depth": analysis.parameters.get("max_depth", 3),
                        "reuse_features": analysis.parameters.get("reuse_features", False),
                        "segmentation_run_id": analysis.parameters.get("segmentation_run_id")
                    },
                    "results": analysis.results,  # Include full results for viewing
                    "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
                    # Include a summary of the results if available
                    "group_count": len(analysis.results.get("segments", {}).get("segments", {})) if analysis.results and "segments" in analysis.results else 0
                }
                for analysis in analyses
            ]
        except Exception as e:
            logger.error(f"Error getting saved segmentation analyses: {str(e)}", exc_info=True)
            return []

    async def get_saved_segmentation_analysis(
            self,
            db: AsyncSession,  # Pass db explicitly
            analysis_id: int,
            model_id: int,
            version_id: int,
            user_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific saved segmentation analysis by ID.

        Args:
            db: Database session
            analysis_id: ID of the analysis
            model_id: ID of the model
            version_id: ID of the model version
            user_id: ID of the user

        Returns:
            Saved segmentation analysis or None if not found
        """
        logger.info(f"Getting saved segmentation analysis {analysis_id} for model {model_id}, version {version_id}")

        try:
            # Query for the specific analysis
            query = select(SegmentationAnalysis).where(
                SegmentationAnalysis.id == analysis_id,
                SegmentationAnalysis.model_id == model_id,
                SegmentationAnalysis.version_id == version_id,
                SegmentationAnalysis.user_id == user_id
            )

            result = await db.execute(query)
            analysis = result.scalars().first()

            if analysis:
                # Convert to dictionary
                return {
                    "id": analysis.id,
                    "model_id": analysis.model_id,
                    "version_id": analysis.version_id,
                    "user_id": analysis.user_id,
                    "name": analysis.name,
                    "parameters": analysis.parameters,
                    "results": analysis.results,
                    "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
                    "segmentation_run_id": analysis.parameters.get("segmentation_run_id") if analysis.parameters else None
                }
            return None
        except Exception as e:
            logger.error(f"Error getting saved segmentation analysis: {str(e)}", exc_info=True)
            raise ValueError(f"Error getting saved segmentation analysis: {str(e)}")

    async def delete_segmentation_analysis(
            self,
            db: AsyncSession,
            analysis_id: int
    ) -> bool:
        """
        Delete a segmentation analysis.

        Args:
            db: Database session
            analysis_id: ID of the analysis to delete

        Returns:
            True if the analysis was deleted, False otherwise
        """
        try:
            # Get the analysis
            result = await db.execute(
                select(SegmentationAnalysis).where(
                    SegmentationAnalysis.id == analysis_id
                )
            )
            analysis = result.scalars().first()

            if not analysis:
                logger.warning(f"Analysis not found: {analysis_id}")
                return False

            # Delete the analysis
            await db.delete(analysis)
            await db.commit()

            logger.info(f"Deleted segmentation analysis: {analysis.name} (ID: {analysis.id})")
            return True
        except Exception as e:
            await db.rollback()
            logger.error(f"Error deleting segmentation analysis: {str(e)}", exc_info=True)
            return False


async def get_segmentation_service(db_session: AsyncSession) -> SegmentationService:
    """
    Dependency to get the segmentation service.

    Args:
        db_session: Database session dependency

    Returns:
        Segmentation service instance
    """
    return SegmentationService(db_session)


async def generate_tree_visualization(segments):
    """
    Generate a tree visualization for segmentation results.

    Args:
        segments: Segmentation results

    Returns:
        Base64-encoded PNG image of the tree visualization
    """
    try:
        # Create a new Digraph
        dot = Digraph()

        # Set graph attributes
        dot.attr('graph', rankdir='TB', splines='ortho', nodesep='0.5', ranksep='0.5')
        dot.attr('node', shape='box', style='filled,rounded', fontname='Arial', fontsize='12', margin='0.3,0.2')
        dot.attr('edge', fontname='Arial', fontsize='10')

        # Add root node
        dot.node('root', 'All Customers', fillcolor='#f0f0f0')

        # Add segment nodes and edges
        segment_names = list(segments["segment_characteristics"].keys())

        # Get the first split feature
        first_feature = None
        if len(segment_names) > 0 and segments["segment_characteristics"][segment_names[0]]:
            first_feature = segments["segment_characteristics"][segment_names[0]][0]["feature"]

        if first_feature:
            # Add first level split
            dot.node('split1', f'Split on {first_feature.replace("high_", "").replace("low_", "")}',
                     fillcolor='#e0e0e0')
            dot.edge('root', 'split1')

            # Add segments
            for i, segment_name in enumerate(segment_names):
                # Get segment characteristics
                characteristics = segments["segment_characteristics"][segment_name]
                count = segments["segment_counts"].get(segment_name, "N/A")

                # Create label with characteristics
                label = f'{segment_name}\nCount: {count}\n'
                for char in characteristics[:3]:  # Show top 3 characteristics
                    feature = char["feature"].replace("high_", "High ").replace("low_", "Low ")
                    label += f'\n{feature}: {char["value"]:.2f}'

                # Add segment node
                dot.node(f'segment{i}', label, fillcolor='#d0e0f0')

                # Connect to appropriate parent
                if i == 0:
                    dot.edge('split1', f'segment{i}', label='True')
                elif i == 1 and len(segment_names) == 2:
                    dot.edge('split1', f'segment{i}', label='False')
                elif i == 1 and len(segment_names) > 2:
                    # Add second level split for 3+ segments
                    second_feature = None
                    if len(characteristics) > 1:
                        second_feature = characteristics[1]["feature"]

                    if second_feature:
                        split_label = f'Split on {second_feature.replace("high_", "").replace("low_", "")}'
                        dot.node('split2', split_label, fillcolor='#e0e0e0')
                        dot.edge('split1', 'split2', label='False')
                        dot.edge('split2', f'segment{i}', label='True')
                elif i >= 2:
                    dot.edge('split2', f'segment{i}', label='False')

        # Render the graph to a PNG image
        png_data = dot.pipe(format='png')

        # Encode as base64
        base64_encoded = base64.b64encode(png_data).decode('utf-8')

        return base64_encoded
    except Exception as e:
        logger.error(f"Error generating tree visualization: {str(e)}", exc_info=True)

        # Create a simple error image using matplotlib
        plt.figure(figsize=(8, 6))
        plt.text(0.5, 0.5, f"Error generating visualization:\n{str(e)}",
                 horizontalalignment='center', verticalalignment='center',
                 fontsize=12, color='red')
        plt.axis('off')

        # Save to bytes buffer
        buf = io.BytesIO()
        plt.savefig(buf, format='png')
        plt.close()
        buf.seek(0)

        # Encode as base64
        base64_encoded = base64.b64encode(buf.read()).decode('utf-8')
        return base64_encoded
