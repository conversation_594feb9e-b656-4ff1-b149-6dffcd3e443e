"""
Connection metadata operations.

This module provides functions for retrieving metadata about database schemas, tables, and views.
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status

from app.core.logger import get_logger, log_function_call
from app.services.connection_service.core import get_connection_by_id
from app.services.connection_service.warehouse import get_data_warehouse_connections

# Create logger for this module
logger = get_logger(__name__)


@log_function_call()
async def list_connection_tables(
    db: AsyncSession,
    connection_id: int,
    schema: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """
    List tables in a database connection.

    Args:
        db: Database session
        connection_id: Connection ID
        schema: Schema name (optional)

    Returns:
        List of tables with metadata

    Raises:
        HTTPException: If connection not found
    """
    # Get connection
    connection = await get_connection_by_id(db, connection_id)
    if not connection:
        logger.error(f"Connection not found: {connection_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Connection not found",
        )

    try:
        # List tables
        from app.utils.database_connection import create_sqlalchemy_engine_sync, decrypt_credentials

        # Decrypt credentials
        credentials = decrypt_credentials(connection.credentials)

        # Create engine
        engine = create_sqlalchemy_engine_sync(connection.type, credentials)

        # Get tables
        from sqlalchemy import inspect
        inspector = inspect(engine)

        # Get schemas if not specified
        schemas = [schema] if schema else inspector.get_schema_names()

        # Get tables for each schema
        tables = []
        for schema_name in schemas:
            try:
                schema_tables = inspector.get_table_names(schema=schema_name)
                for table_name in schema_tables:
                    # Get table metadata
                    columns = inspector.get_columns(table_name, schema=schema_name)
                    primary_keys = inspector.get_pk_constraint(table_name, schema=schema_name)

                    # Add table to result
                    tables.append({
                        "schema": schema_name,
                        "name": table_name,
                        "type": "table",
                        "columns": [
                            {
                                "name": column["name"],
                                "type": str(column["type"]),
                                "nullable": column.get("nullable", True),
                                "default": str(column.get("default", "")),
                                "primary_key": column["name"] in primary_keys.get("constrained_columns", []),
                            }
                            for column in columns
                        ],
                    })
            except Exception as e:
                logger.error(f"Error getting tables for schema {schema_name}: {str(e)}")
                continue

        return tables
    except Exception as e:
        logger.error(f"Error listing tables for connection {connection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing tables: {str(e)}",
        )


@log_function_call()
async def list_connection_views(
    db: AsyncSession,
    connection_id: int,
    schema: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """
    List views in a database connection.

    Args:
        db: Database session
        connection_id: Connection ID
        schema: Schema name (optional)

    Returns:
        List of views with metadata

    Raises:
        HTTPException: If connection not found
    """
    # Get connection
    connection = await get_connection_by_id(db, connection_id)
    if not connection:
        logger.error(f"Connection not found: {connection_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Connection not found",
        )

    try:
        # List views
        from app.utils.database_connection import create_sqlalchemy_engine_sync, decrypt_credentials

        # Decrypt credentials
        credentials = decrypt_credentials(connection.credentials)

        # Create engine
        engine = create_sqlalchemy_engine_sync(connection.type, credentials)

        # Get views
        from sqlalchemy import inspect
        inspector = inspect(engine)

        # Get schemas if not specified
        schemas = [schema] if schema else inspector.get_schema_names()

        # Get views for each schema
        views = []
        for schema_name in schemas:
            try:
                schema_views = inspector.get_view_names(schema=schema_name)
                for view_name in schema_views:
                    # Get view metadata
                    columns = inspector.get_columns(view_name, schema=schema_name)

                    # Add view to result
                    views.append({
                        "schema": schema_name,
                        "name": view_name,
                        "type": "view",
                        "columns": [
                            {
                                "name": column["name"],
                                "type": str(column["type"]),
                                "nullable": column.get("nullable", True),
                                "default": str(column.get("default", "")),
                            }
                            for column in columns
                        ],
                    })
            except Exception as e:
                logger.error(f"Error getting views for schema {schema_name}: {str(e)}")
                continue

        return views
    except Exception as e:
        logger.error(f"Error listing views for connection {connection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing views: {str(e)}",
        )


@log_function_call()
async def get_available_schemas(db: AsyncSession) -> Dict[str, List[str]]:
    """
    Get available schemas from the data warehouse.

    Args:
        db: Database session

    Returns:
        Dictionary with warehouse connection name as key and list of schemas as value
    """
    # Get data warehouse connections
    connections = await get_data_warehouse_connections(db)

    # Get schemas for each connection
    schemas = {}
    for connection in connections:
        try:
            # Import here to avoid circular imports
            from app.utils.database_connection import create_sqlalchemy_engine_sync, decrypt_credentials

            # Decrypt credentials
            credentials = decrypt_credentials(connection.credentials)

            # Create engine
            engine = create_sqlalchemy_engine_sync(connection.type, credentials)

            # Get schemas
            from sqlalchemy import inspect
            inspector = inspect(engine)
            connection_schemas = inspector.get_schema_names()

            # Add to result
            schemas[connection.name] = connection_schemas
        except Exception as e:
            logger.error(f"Error getting schemas for connection {connection.name}: {str(e)}")
            schemas[connection.name] = []

    return schemas