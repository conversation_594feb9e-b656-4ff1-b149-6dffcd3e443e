"""
Main application module.

This module sets up the FastAPI application with all middleware, exception handlers,
and routers. It also handles application startup and shutdown events.
"""

from fastapi import FastAPI, Request, status, File, UploadFile
# from fastapi.middleware.cors import CORSMiddleware as FastAPICORSMiddleware
# from starlette.middleware.cors import CORSMiddleware # as FastAPICORSMiddleware
# import starlette.middleware.cors as starlette_cors
from starlette.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError, ResponseValidationError
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError

from app.core import config
from app.core.logger import get_logger
from app.core.middleware import RequestLoggingMiddleware, ResponseTimeHeaderMiddleware
from app.core.exception_handlers import (
    validation_exception_handler,
    sqlalchemy_exception_handler,
    general_exception_handler,
)
from app.api.router import api_router
from app.db.init_db import init_db
from app.db.session import get_db

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Union

# Create logger for this module
logger = get_logger(__name__)

# Create FastAPI application
app = FastAPI(
    title=config.PROJECT_NAME,
    description="API for customer churn analysis",
    version="0.1.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
)

# Define origins
origins = config.CORS_ORIGINS
logger.debug("CORS origins: ", origins)
if isinstance(origins, str):
    origins = [origin.strip() for origin in origins.split(",") if origin.strip()]
if "http://localhost:3000" not in origins:
    origins.append("http://localhost:3000")
logger.debug("CORS origins after: ", origins)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(ResponseTimeHeaderMiddleware)

# Register exception handlers
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# Include API router
app.include_router(api_router, prefix=config.API_V1_PREFIX)

# Helper function to convert NumPy types to Python native types
def convert_numpy_types(obj):
    """
    Recursively convert NumPy types to Python native types.
    
    Args:
        obj: Object to convert (can be a scalar, list, or dict)
        
    Returns:
        Object with NumPy types converted to Python native types
    """
    if isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj

@app.post("/api/datasets/preview", response_model=Dict[str, Any])
async def preview_dataset_direct(
    file: UploadFile = File(...),
):
    """
    Direct endpoint for previewing a CSV file before creating a dataset.
    This endpoint is needed to match the frontend's expected URL pattern.
    
    Args:
        file: CSV file to preview
        
    Returns:
        Preview data including column information and sample data
    """
    logger.debug("Calling direct preview_dataset endpoint")
    
    # Check file type
    if not file.filename.endswith(".csv"):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": "Only CSV files are supported"},
        )
    
    try:
        # Read CSV with pandas
        contents = await file.read()
        file.file.seek(0)  # Reset file pointer for potential future reads
        
        # Use StringIO to create a file-like object from the contents
        import io
        csv_file = io.StringIO(contents.decode('utf-8'))
        
        # Read the CSV file
        df = pd.read_csv(csv_file)
        
        # Get sample data (first 10 rows)
        sample_data = df.head(10).to_dict('records')
        
        # Extract column information
        columns = []
        for col_name in df.columns:
            # Determine the inferred data type
            dtype = df[col_name].dtype
            if pd.api.types.is_integer_dtype(dtype):
                inferred_type = "integer"
            elif pd.api.types.is_float_dtype(dtype):
                inferred_type = "float"
            elif pd.api.types.is_bool_dtype(dtype):
                inferred_type = "boolean"
            elif pd.api.types.is_datetime64_dtype(dtype):
                inferred_type = "datetime"
            else:
                # Default to string for text and other types
                inferred_type = "string"
            
            # Create column info
            col_info = {
                "name": col_name,
                "inferred_type": inferred_type,
                "nullable": bool(df[col_name].isna().any()),  # Convert numpy.bool to Python bool
                "unique_values": int(df[col_name].nunique()),  # Convert numpy.int64 to Python int
                "sample_values": [convert_numpy_types(val) for val in df[col_name].head(5).tolist()],
            }
            columns.append(col_info)
        
        # Convert all NumPy types to Python native types
        sample_data = convert_numpy_types(sample_data)
        
        # Return preview data
        preview_data = {
            "columns": columns,
            "sample_data": sample_data,
            "row_count": int(len(df)),  # Ensure this is a Python int
            "column_count": int(len(df.columns)),  # Ensure this is a Python int
        }
        
        return preview_data
    
    except pd.errors.EmptyDataError:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": "The CSV file is empty"},
        )
    except pd.errors.ParserError:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": "Error parsing CSV file. Please check the file format."},
        )
    except UnicodeDecodeError:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": "Error decoding the file. Please ensure it's a valid CSV file with UTF-8 encoding."},
        )
    except Exception as e:
        logger.error(f"Error previewing CSV file: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": f"Error processing CSV file: {str(e)}"},
        )


@app.on_event("startup")
async def startup_event():
    """
    Runs when the application starts.
    Initializes database and logs startup information.
    """
    logger.info(f"Starting {config.PROJECT_NAME} API")
    logger.info(f"Environment: {config.ENVIRONMENT}")
    
    # Initialize database
    await init_db()
    
    # Initialize agent graph
    try:
        # Import here to avoid circular imports
        from app.services.agent_graph_service import AgentGraphService
        from app.services.llm_service import LLMService
        from app.services.embedding_service import EmbeddingService
        
        # Get database session
        async for db in get_db():
            # Create services
            llm_service = LLMService(db)
            embedding_service = EmbeddingService()
            agent_graph_service = AgentGraphService(llm_service, embedding_service)
            
            # Initialize agent graph
            await agent_graph_service.initialize_from_db(db)
            break
        
        logger.info("Agent graph initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize agent graph: {str(e)}")
    
    logger.info("Application startup complete")


@app.on_event("shutdown")
async def shutdown_event():
    """
    Runs when the application shuts down.
    Performs cleanup operations and logs shutdown.
    """
    logger.info("Application shutting down")


@app.get("/", include_in_schema=False)
async def root():
    """
    Root endpoint that redirects to API documentation.
    """
    return {"message": f"Welcome to {config.PROJECT_NAME} API", "docs_url": "/api/docs"}


@app.get("/health", tags=["health"])
async def health_check():
    """
    Health check endpoint to verify API is running.
    """
    logger.debug("Health check requested")
    return {"status": "ok"}
