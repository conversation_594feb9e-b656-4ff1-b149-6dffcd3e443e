"""
Database dataset API endpoints.

This module provides API endpoints for database-sourced dataset operations like
creating datasets from database connections, exporting datasets to the data
warehouse, and refreshing datasets from their source.
"""

from typing import List, Optional, Dict, Any, Tuple
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import TypeAdapter

from app.core.logger import get_logger
from app.db.session import get_db
from app.db.models.dataset import Dataset
from app.db.models.task import TaskStatus, DatasetRefresh
from app.schemas.dataset import (
    Dataset as DatasetSchema,
    DatabaseDatasetCreate,
)
from app.schemas.task import (
    DatasetRefresh as DatasetRefreshSchema,
    DatasetRefreshCreate,
    DatasetRefreshUpdate,
    TaskStatus as TaskStatusSchema,
    TaskResult,
)
from app.schemas.user import User as UserSchema
from app.services import database_dataset_service, connection_service
from app.core.security import get_current_user, get_current_active_user, get_current_admin_user
from app.tasks.dataset_tasks import (
    load_dataset_from_database_task,
    export_dataset_to_warehouse_task,
    refresh_dataset_task,
)
from app.celery_app import celery_app

# Create logger for this module
logger = get_logger(__name__)

# Create router
router = APIRouter()


@router.post("/from-database", response_model=TaskResult)
async def create_dataset_from_database(
    dataset_in: DatabaseDatasetCreate,
    background_tasks: BackgroundTasks,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Create a new dataset from a database connection.
    
    Args:
        dataset_in: Dataset creation data
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Task result with task ID
        
    Raises:
        HTTPException: If dataset creation fails
    """
    logger.debug("Calling create_dataset_from_database")
    
    # Check if user has access to the connection
    has_access = await connection_service.check_connection_access(
        db, connection_id=dataset_in.connection_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use this connection",
        )
    
    # Create task status record
    task_id = str(uuid.uuid4())
    task = TaskStatus(
        task_id=task_id,
        task_type="load",
        connection_id=dataset_in.connection_id,
        status="pending",
        progress=0,
    )
    db.add(task)
    await db.commit()
    
    # Start Celery task
    try:
        # Convert dataset_in to dict
        dataset_data = dataset_in.dict()
        
        # Convert user to dict
        user_data = current_user.dict()
        
        # Start task
        load_dataset_from_database_task.delay(
            dataset_data=dataset_data,
            user_data=user_data,
        )
        
        return TaskResult(
            task_id=task_id,
            status="pending",
            progress=0,
        )
    except Exception as e:
        logger.error(f"Error starting dataset load task: {str(e)}")
        
        # Update task status with error
        task.status = "failed"
        task.error = str(e)
        db.add(task)
        await db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting dataset load task: {str(e)}",
        )


@router.post("/{dataset_id}/to-warehouse", response_model=TaskResult)
async def export_dataset_to_warehouse_endpoint(
    dataset_id: int,
    background_tasks: BackgroundTasks,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Export a dataset to the data warehouse.
    
    Args:
        dataset_id: Dataset ID
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Task result with task ID
        
    Raises:
        HTTPException: If dataset not found or export fails
    """
    logger.debug(f"Calling export_dataset_to_warehouse_endpoint {dataset_id}")
    
    # Get dataset
    from sqlalchemy.future import select
    result = await db.execute(select(Dataset).where(Dataset.id == dataset_id))
    dataset = result.scalars().first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found",
        )
    
    # Check if user has access to the dataset
    from app.services import dataset_service
    has_access = await dataset_service.check_dataset_access(
        db, dataset_id=dataset_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to export this dataset",
        )
    
    # Create task status record
    task_id = str(uuid.uuid4())
    task = TaskStatus(
        task_id=task_id,
        task_type="export",
        dataset_id=dataset_id,
        status="pending",
        progress=0,
    )
    db.add(task)
    await db.commit()
    
    # Start Celery task
    try:
        # Start task
        export_dataset_to_warehouse_task.delay(
            dataset_id=dataset_id,
        )
        
        return TaskResult(
            task_id=task_id,
            status="pending",
            progress=0,
        )
    except Exception as e:
        logger.error(f"Error starting dataset export task: {str(e)}")
        
        # Update task status with error
        task.status = "failed"
        task.error = str(e)
        db.add(task)
        await db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting dataset export task: {str(e)}",
        )


@router.post("/{dataset_id}/refresh/now", response_model=TaskResult)
async def refresh_dataset_now(
    dataset_id: int,
    background_tasks: BackgroundTasks,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Refresh a dataset from its source.
    
    Args:
        dataset_id: Dataset ID
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Task result with task ID
        
    Raises:
        HTTPException: If dataset not found or refresh fails
    """
    logger.debug(f"Calling refresh_dataset_now {dataset_id}")
    
    # Get dataset
    from sqlalchemy.future import select
    result = await db.execute(select(Dataset).where(Dataset.id == dataset_id))
    dataset = result.scalars().first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found",
        )
    
    # Check if user has access to the dataset
    from app.services import dataset_service
    has_access = await dataset_service.check_dataset_access(
        db, dataset_id=dataset_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to refresh this dataset",
        )
    
    # Create task status record
    task_id = str(uuid.uuid4())
    task = TaskStatus(
        task_id=task_id,
        task_type="refresh",
        dataset_id=dataset_id,
        connection_id=dataset.connection_id,
        status="pending",
        progress=0,
    )
    db.add(task)
    await db.commit()
    
    # Start Celery task
    try:
        # Start task
        refresh_dataset_task.delay(
            dataset_id=dataset_id,
        )
        
        return TaskResult(
            task_id=task_id,
            status="pending",
            progress=0,
        )
    except Exception as e:
        logger.error(f"Error starting dataset refresh task: {str(e)}")
        
        # Update task status with error
        task.status = "failed"
        task.error = str(e)
        db.add(task)
        await db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting dataset refresh task: {str(e)}",
        )


@router.get("/{dataset_id}/refresh", response_model=Optional[DatasetRefreshSchema])
async def get_dataset_refresh_endpoint(
    dataset_id: int,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get the refresh schedule for a dataset.
    
    Args:
        dataset_id: Dataset ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Refresh schedule if found, None otherwise
        
    Raises:
        HTTPException: If dataset not found or access denied
    """
    logger.debug(f"Calling get_dataset_refresh_endpoint {dataset_id}")
    
    # Get dataset
    from sqlalchemy.future import select
    result = await db.execute(select(Dataset).where(Dataset.id == dataset_id))
    dataset = result.scalars().first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found",
        )
    
    # Check if user has access to the dataset
    from app.services import dataset_service
    has_access = await dataset_service.check_dataset_access(
        db, dataset_id=dataset_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to view this dataset's refresh schedule",
        )
    
    # Get refresh schedule
    refresh = await database_dataset_service.get_dataset_refresh(db, dataset_id=dataset_id)
    
    if not refresh:
        return None
    
    # Convert to Pydantic model
    try:
        # Create dict with dataset information
        refresh_dict = {
            "id": refresh.id,
            "dataset_id": refresh.dataset_id,
            "schedule_type": refresh.schedule_type,
            "cron_expression": refresh.cron_expression,
            "last_refresh": refresh.last_refresh,
            "last_status": refresh.last_status,
            "error_message": refresh.error_message,
            "created_at": refresh.created_at,
            "updated_at": refresh.updated_at,
            "dataset": {
                "id": dataset.id,
                "name": dataset.name
            }
        }
        
        refresh_schema = DatasetRefreshSchema.model_validate(refresh_dict)
        return refresh_schema
    except Exception as e:
        logger.error(f"Error converting refresh schedule: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing refresh schedule data",
        )


@router.post("/{dataset_id}/refresh", response_model=DatasetRefreshSchema)
async def create_dataset_refresh_endpoint(
    dataset_id: int,
    refresh_in: DatasetRefreshCreate,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Create a refresh schedule for a dataset.
    
    Args:
        dataset_id: Dataset ID
        refresh_in: Refresh schedule creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Created refresh schedule
        
    Raises:
        HTTPException: If dataset not found, access denied, or refresh schedule creation fails
    """
    logger.debug(f"Calling create_dataset_refresh_endpoint {dataset_id}")
    
    # Get dataset
    from sqlalchemy.future import select
    result = await db.execute(select(Dataset).where(Dataset.id == dataset_id))
    dataset = result.scalars().first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found",
        )
    
    # Check if user has access to the dataset
    from app.services import dataset_service
    has_access = await dataset_service.check_dataset_access(
        db, dataset_id=dataset_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to create a refresh schedule for this dataset",
        )
    
    try:
        # Create refresh schedule
        refresh = await database_dataset_service.create_dataset_refresh(
            db=db,
            refresh_in=refresh_in,
        )
        
        # Convert to Pydantic model
        refresh_dict = {
            "id": refresh.id,
            "dataset_id": refresh.dataset_id,
            "schedule_type": refresh.schedule_type,
            "cron_expression": refresh.cron_expression,
            "last_refresh": refresh.last_refresh,
            "last_status": refresh.last_status,
            "error_message": refresh.error_message,
            "created_at": refresh.created_at,
            "updated_at": refresh.updated_at,
            "dataset": {
                "id": dataset.id,
                "name": dataset.name
            }
        }
        
        refresh_schema = DatasetRefreshSchema.model_validate(refresh_dict)
        return refresh_schema
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error creating refresh schedule: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating refresh schedule",
        )


@router.put("/{dataset_id}/refresh", response_model=DatasetRefreshSchema)
async def update_dataset_refresh_endpoint(
    dataset_id: int,
    refresh_in: DatasetRefreshUpdate,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Update the refresh schedule for a dataset.
    
    Args:
        dataset_id: Dataset ID
        refresh_in: Refresh schedule update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Updated refresh schedule
        
    Raises:
        HTTPException: If dataset not found, access denied, or refresh schedule update fails
    """
    logger.debug(f"Calling update_dataset_refresh_endpoint {dataset_id}")
    
    # Get dataset
    from sqlalchemy.future import select
    result = await db.execute(select(Dataset).where(Dataset.id == dataset_id))
    dataset = result.scalars().first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found",
        )
    
    # Check if user has access to the dataset
    from app.services import dataset_service
    has_access = await dataset_service.check_dataset_access(
        db, dataset_id=dataset_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to update the refresh schedule for this dataset",
        )
    
    try:
        # Update refresh schedule
        refresh = await database_dataset_service.update_dataset_refresh(
            db=db,
            dataset_id=dataset_id,
            refresh_in=refresh_in,
        )
        
        # Convert to Pydantic model
        refresh_dict = {
            "id": refresh.id,
            "dataset_id": refresh.dataset_id,
            "schedule_type": refresh.schedule_type,
            "cron_expression": refresh.cron_expression,
            "last_refresh": refresh.last_refresh,
            "last_status": refresh.last_status,
            "error_message": refresh.error_message,
            "created_at": refresh.created_at,
            "updated_at": refresh.updated_at,
            "dataset": {
                "id": dataset.id,
                "name": dataset.name
            }
        }
        
        refresh_schema = DatasetRefreshSchema.model_validate(refresh_dict)
        return refresh_schema
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error updating refresh schedule: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating refresh schedule",
        )


@router.delete("/{dataset_id}/refresh", response_model=Dict[str, Any])
async def delete_dataset_refresh_endpoint(
    dataset_id: int,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Delete the refresh schedule for a dataset.
    
    Args:
        dataset_id: Dataset ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If dataset not found, access denied, or refresh schedule deletion fails
    """
    logger.debug(f"Calling delete_dataset_refresh_endpoint {dataset_id}")
    
    # Get dataset
    from sqlalchemy.future import select
    result = await db.execute(select(Dataset).where(Dataset.id == dataset_id))
    dataset = result.scalars().first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found",
        )
    
    # Check if user has access to the dataset
    from app.services import dataset_service
    has_access = await dataset_service.check_dataset_access(
        db, dataset_id=dataset_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete the refresh schedule for this dataset",
        )
    
    # Delete refresh schedule
    success = await database_dataset_service.delete_dataset_refresh(
        db=db,
        dataset_id=dataset_id,
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Refresh schedule not found",
        )
    
    return {"message": "Refresh schedule deleted successfully"}


@router.get("/tasks/{task_id}", response_model=TaskStatusSchema)
async def get_task_status_endpoint(
    task_id: str,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get the status of a task.
    
    Args:
        task_id: Task ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Task status
        
    Raises:
        HTTPException: If task not found
    """
    logger.debug(f"Calling get_task_status_endpoint {task_id}")
    
    # Get task status
    task = await database_dataset_service.get_task_status(db, task_id=task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found",
        )
    
    # Check if user has access to the dataset or connection
    if task.dataset_id:
        from app.services import dataset_service
        has_access = await dataset_service.check_dataset_access(
            db, dataset_id=task.dataset_id, user_id=current_user.id
        )
        
        if not has_access and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions to view this task",
            )
    elif task.connection_id:
        has_access = await connection_service.check_connection_access(
            db, connection_id=task.connection_id, user_id=current_user.id
        )
        
        if not has_access and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions to view this task",
            )
    
    # Convert to Pydantic model
    try:
        # Get dataset if available
        dataset = None
        if task.dataset_id:
            result = await db.execute(select(Dataset).where(Dataset.id == task.dataset_id))
            dataset = result.scalars().first()
        
        # Get connection if available
        connection = None
        if task.connection_id:
            connection = await connection_service.get_connection_by_id(db, connection_id=task.connection_id)
        
        # Create dict with task information
        task_dict = {
            "id": task.id,
            "task_id": task.task_id,
            "task_type": task.task_type,
            "dataset_id": task.dataset_id,
            "connection_id": task.connection_id,
            "status": task.status,
            "progress": task.progress,
            "result": task.result,
            "error": task.error,
            "created_at": task.created_at,
            "updated_at": task.updated_at,
            "dataset": {
                "id": dataset.id,
                "name": dataset.name
            } if dataset else None,
            "connection": {
                "id": connection.id,
                "name": connection.name
            } if connection else None
        }
        
        task_schema = TaskStatusSchema.model_validate(task_dict)
        return task_schema
    except Exception as e:
        logger.error(f"Error converting task status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing task status data",
        )


@router.get("/{dataset_id}/tasks", response_model=List[TaskStatusSchema])
async def get_dataset_tasks_endpoint(
    dataset_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get tasks for a dataset.
    
    Args:
        dataset_id: Dataset ID
        skip: Number of tasks to skip
        limit: Maximum number of tasks to return
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of tasks
        
    Raises:
        HTTPException: If dataset not found or access denied
    """
    logger.debug(f"Calling get_dataset_tasks_endpoint {dataset_id}")
    
    # Get dataset
    from sqlalchemy.future import select
    result = await db.execute(select(Dataset).where(Dataset.id == dataset_id))
    dataset = result.scalars().first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found",
        )
    
    # Check if user has access to the dataset
    from app.services import dataset_service
    has_access = await dataset_service.check_dataset_access(
        db, dataset_id=dataset_id, user_id=current_user.id
    )
    
    if not has_access and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to view tasks for this dataset",
        )
    
    # Get tasks
    tasks = await database_dataset_service.get_dataset_tasks(
        db, dataset_id=dataset_id, skip=skip, limit=limit
    )
    
    # Convert to Pydantic models
    result = []
    for task in tasks:
        try:
            # Create dict with task information
            task_dict = {
                "id": task.id,
                "task_id": task.task_id,
                "task_type": task.task_type,
                "dataset_id": task.dataset_id,
                "connection_id": task.connection_id,
                "status": task.status,
                "progress": task.progress,
                "result": task.result,
                "error": task.error,
                "created_at": task.created_at,
                "updated_at": task.updated_at,
                "dataset": {
                    "id": dataset.id,
                    "name": dataset.name
                },
                "connection": None  # We don't need connection information here
            }
            
            task_schema = TaskStatusSchema.model_validate(task_dict)
            result.append(task_schema)
        except Exception as e:
            logger.error(f"Error converting task status: {str(e)}")
            continue
    
    return result
