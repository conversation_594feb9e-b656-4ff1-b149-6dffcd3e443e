"""
User API endpoint tests.

This module provides tests for the user API endpoints, including
creating, updating, and retrieving users.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models.user import User
from app.db.models.role import RoleEnum
from app.services import user_service


def test_read_users_admin(client: TestClient, test_superuser: User, superuser_token_headers: dict) -> None:
    """
    Test getting all users as admin.
    
    Args:
        client: Test client
        test_superuser: Test superuser
        superuser_token_headers: Superuser authentication headers
    """
    response = client.get("/api/v1/users/", headers=superuser_token_headers)
    
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    # Should have at least the test users created in fixtures
    assert len(response.json()) >= 3


def test_read_users_forbidden(client: TestClient, test_user: User, user_token_headers: dict) -> None:
    """
    Test getting all users as non-admin (should be forbidden).
    
    Args:
        client: Test client
        test_user: Test regular user
        user_token_headers: User authentication headers
    """
    response = client.get("/api/v1/users/", headers=user_token_headers)
    
    assert response.status_code == 403
    assert "detail" in response.json()


def test_read_users_unauthorized(client: TestClient) -> None:
    """
    Test getting all users without authentication.
    
    Args:
        client: Test client
    """
    response = client.get("/api/v1/users/")
    
    assert response.status_code == 401
    assert "detail" in response.json()


def test_create_user_admin(client: TestClient, superuser_token_headers: dict) -> None:
    """
    Test creating a new user as admin.
    
    Args:
        client: Test client
        superuser_token_headers: Superuser authentication headers
    """
    user_data = {
        "email": "<EMAIL>",
        "password": "newuserpassword",
        "full_name": "New User",
        "role": RoleEnum.USER.value,
    }
    
    response = client.post("/api/v1/users/", json=user_data, headers=superuser_token_headers)
    
    assert response.status_code == 200
    assert response.json()["email"] == user_data["email"]
    assert response.json()["full_name"] == user_data["full_name"]
    assert response.json()["role"] == user_data["role"]
    assert "id" in response.json()


def test_create_user_existing_email(
    client: TestClient, 
    test_user: User, 
    superuser_token_headers: dict
) -> None:
    """
    Test creating a user with an existing email (should fail).
    
    Args:
        client: Test client
        test_user: Test user with existing email
        superuser_token_headers: Superuser authentication headers
    """
    user_data = {
        "email": "<EMAIL>",  # Already exists
        "password": "newuserpassword",
        "full_name": "Duplicate User",
        "role": RoleEnum.USER.value,
    }
    
    response = client.post("/api/v1/users/", json=user_data, headers=superuser_token_headers)
    
    assert response.status_code == 400
    assert "detail" in response.json()


def test_create_user_forbidden(client: TestClient, user_token_headers: dict) -> None:
    """
    Test creating a user as non-admin (should be forbidden).
    
    Args:
        client: Test client
        user_token_headers: User authentication headers
    """
    user_data = {
        "email": "<EMAIL>",
        "password": "newuserpassword",
        "full_name": "New User",
        "role": RoleEnum.USER.value,
    }
    
    response = client.post("/api/v1/users/", json=user_data, headers=user_token_headers)
    
    assert response.status_code == 403
    assert "detail" in response.json()


def test_read_user_self(client: TestClient, test_user: User, user_token_headers: dict) -> None:
    """
    Test getting own user information.
    
    Args:
        client: Test client
        test_user: Test user
        user_token_headers: User authentication headers
    """
    response = client.get(f"/api/v1/users/{test_user.id}", headers=user_token_headers)
    
    assert response.status_code == 200
    assert response.json()["id"] == test_user.id
    assert response.json()["email"] == test_user.email
    assert response.json()["full_name"] == test_user.full_name


def test_read_user_admin(
    client: TestClient, 
    test_user: User, 
    test_superuser: User, 
    superuser_token_headers: dict
) -> None:
    """
    Test getting another user's information as admin.
    
    Args:
        client: Test client
        test_user: Test user to get information for
        test_superuser: Test superuser
        superuser_token_headers: Superuser authentication headers
    """
    response = client.get(f"/api/v1/users/{test_user.id}", headers=superuser_token_headers)
    
    assert response.status_code == 200
    assert response.json()["id"] == test_user.id
    assert response.json()["email"] == test_user.email
    assert response.json()["full_name"] == test_user.full_name


def test_read_user_forbidden(
    client: TestClient, 
    test_user: User, 
    test_viewer: User, 
    viewer_token_headers: dict
) -> None:
    """
    Test getting another user's information as non-admin (should be forbidden).
    
    Args:
        client: Test client
        test_user: Test user to get information for
        test_viewer: Test viewer user
        viewer_token_headers: Viewer authentication headers
    """
    response = client.get(f"/api/v1/users/{test_user.id}", headers=viewer_token_headers)
    
    assert response.status_code == 403
    assert "detail" in response.json()


def test_read_user_not_found(client: TestClient, superuser_token_headers: dict) -> None:
    """
    Test getting a non-existent user (should return 404).
    
    Args:
        client: Test client
        superuser_token_headers: Superuser authentication headers
    """
    response = client.get("/api/v1/users/999999", headers=superuser_token_headers)
    
    assert response.status_code == 404
    assert "detail" in response.json()


@pytest.mark.asyncio
async def test_update_user_self(
    async_client: AsyncClient,
    db_session: AsyncSession,
    test_user: User,
    user_token_headers: dict,
) -> None:
    """
    Test updating own user information.
    
    Args:
        async_client: Async test client
        db_session: Database session
        test_user: Test user
        user_token_headers: User authentication headers
    """
    update_data = {
        "full_name": "Updated User Name",
    }
    
    response = await async_client.put(
        f"/api/v1/users/{test_user.id}",
        json=update_data,
        headers=user_token_headers,
    )
    
    assert response.status_code == 200
    assert response.json()["full_name"] == update_data["full_name"]
    
    # Verify database was updated
    updated_user = await user_service.get_user_by_id(db_session, user_id=test_user.id)
    assert updated_user.full_name == update_data["full_name"]


@pytest.mark.asyncio
async def test_update_user_admin(
    async_client: AsyncClient,
    db_session: AsyncSession,
    test_user: User,
    test_superuser: User,
    superuser_token_headers: dict,
) -> None:
    """
    Test updating another user's information as admin.
    
    Args:
        async_client: Async test client
        db_session: Database session
        test_user: Test user to update
        test_superuser: Test superuser
        superuser_token_headers: Superuser authentication headers
    """
    update_data = {
        "full_name": "Admin Updated User",
        "role": RoleEnum.VIEWER.value,  # Change role (only admin can do this)
    }
    
    response = await async_client.put(
        f"/api/v1/users/{test_user.id}",
        json=update_data,
        headers=superuser_token_headers,
    )
    
    assert response.status_code == 200
    assert response.json()["full_name"] == update_data["full_name"]
    assert response.json()["role"] == update_data["role"]
    
    # Verify database was updated
    updated_user = await user_service.get_user_by_id(db_session, user_id=test_user.id)
    assert updated_user.full_name == update_data["full_name"]
    assert updated_user.role.name == update_data["role"]


def test_update_user_forbidden_other_user(
    client: TestClient,
    test_user: User,
    test_viewer: User,
    viewer_token_headers: dict,
) -> None:
    """
    Test updating another user's information as non-admin (should be forbidden).
    
    Args:
        client: Test client
        test_user: Test user to update
        test_viewer: Test viewer user
        viewer_token_headers: Viewer authentication headers
    """
    update_data = {
        "full_name": "Unauthorized Update",
    }
    
    response = client.put(
        f"/api/v1/users/{test_user.id}",
        json=update_data,
        headers=viewer_token_headers,
    )
    
    assert response.status_code == 403
    assert "detail" in response.json()


def test_update_user_forbidden_role_change(
    client: TestClient,
    test_user: User,
    user_token_headers: dict,
) -> None:
    """
    Test updating own role as non-admin (should be forbidden).
    
    Args:
        client: Test client
        test_user: Test user
        user_token_headers: User authentication headers
    """
    update_data = {
        "role": RoleEnum.ADMIN.value,  # Try to promote self to admin
    }
    
    response = client.put(
        f"/api/v1/users/{test_user.id}",
        json=update_data,
        headers=user_token_headers,
    )
    
    assert response.status_code == 403
    assert "detail" in response.json()


@pytest.mark.asyncio
async def test_delete_user_admin(
    async_client: AsyncClient,
    db_session: AsyncSession,
    test_viewer: User,
    test_superuser: User,
    superuser_token_headers: dict,
) -> None:
    """
    Test deleting a user as admin.
    
    Args:
        async_client: Async test client
        db_session: Database session
        test_viewer: Test viewer user to delete
        test_superuser: Test superuser
        superuser_token_headers: Superuser authentication headers
    """
    response = await async_client.delete(
        f"/api/v1/users/{test_viewer.id}",
        headers=superuser_token_headers,
    )
    
    assert response.status_code == 200
    assert "message" in response.json()
    
    # Verify user was deleted
    deleted_user = await user_service.get_user_by_id(db_session, user_id=test_viewer.id)
    assert deleted_user is None


def test_delete_user_forbidden(
    client: TestClient,
    test_viewer: User,
    user_token_headers: dict,
) -> None:
    """
    Test deleting a user as non-admin (should be forbidden).
    
    Args:
        client: Test client
        test_viewer: Test viewer user to delete
        user_token_headers: User authentication headers
    """
    response = client.delete(
        f"/api/v1/users/{test_viewer.id}",
        headers=user_token_headers,
    )
    
    assert response.status_code == 403
    assert "detail" in response.json()


def test_delete_self_forbidden(
    client: TestClient,
    test_superuser: User,
    superuser_token_headers: dict,
) -> None:
    """
    Test deleting own user account (should be forbidden).
    
    Args:
        client: Test client
        test_superuser: Test superuser
        superuser_token_headers: Superuser authentication headers
    """
    response = client.delete(
        f"/api/v1/users/{test_superuser.id}",
        headers=superuser_token_headers,
    )
    
    assert response.status_code == 400
    assert "detail" in response.json()
