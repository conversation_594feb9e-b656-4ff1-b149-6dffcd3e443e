# Core utilities
packaging>=24.1
setuptools>=65.5.1
wheel>=0.41.2
typing_extensions==4.8.0   # fixes 'deprecated' import errors with anyio/jupyter-server

# Networking / crypto (modern, Py3.8-compatible)
requests==2.32.3
urllib3==2.2.3
idna==3.7
charset-normalizer==3.3.2
cryptography==41.0.7
pycparser>=2.21
cffi>=1.16.0
certifi>=2024.8.30

# Scientific Python
numpy==1.24.4
scipy==1.10.1
pandas==1.5.3
scikit-learn==1.3.2
matplotlib==3.7.3
pillow==10.4.0

# Jupyter stack (works on Py3.8)
jupyterlab==4.2.5
ipykernel==6.29.5
ipython==8.12.2          # last IPython LTS for Py3.8
jupyter-client==8.6.2
jupyter-core==5.7.2
traitlets==5.14.3
tornado==6.4.1
pyzmq==25.1.2
prompt-toolkit==3.0.48
Pygments==2.18.0
wcwidth==0.2.13
decorator==5.1.1

# Excel / files
openpyxl==3.1.5
et-xmlfile==1.1.0
XlsxWriter==3.2.0
xlrd==2.0.1   # note: reads .xls only; for .xlsx use openpyxl

# Optional but handy
tqdm==4.66.4
psutil==5.9.8
protobuf>=4.25.3
beautifulsoup4==4.12.3
pyyaml==6.0.2
jsons