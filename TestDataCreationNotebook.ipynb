#%% md
# Prepaid Customer Data Generation Notebook

This notebook generates synthetic prepaid customer data with realistic usage patterns, top-up behaviors, and device characteristics.
#%%
from backend.app.utils.data_generators_prepaid.raw_data_generators import generate_customers

import pandas as pd
import numpy as np
#%% md
## Generate Prepaid Customer Data

Generate synthetic prepaid customers with realistic usage patterns.
#%%
# Generate 100 prepaid customers with realistic usage patterns
customers = generate_customers(100)
print(f"Generated {len(customers)} prepaid customers")
#%% md
## Process and Structure the Data

Extract different data types into separate DataFrames for analysis.
#%%
# Extract customer data
customers_data_raw = [c['customer'] for c in customers]

# Extract plans data
plans_data_raw = []
for c in customers:
    for plan in c['plans']:
        plans_data_raw.append(plan)

# Extract billing/transaction data
billing_data_raw = []
for c in customers:
    for billing in c['billing']:
        billing_data_raw.append(billing)

# Extract network data
network_data_raw = []
for c in customers:
    for network in c['network']:
        network_data_raw.append(network)

print(f"Extracted {len(customers_data_raw)} customers, {len(plans_data_raw)} plans, {len(billing_data_raw)} transactions, {len(network_data_raw)} network records")
#%% md
## Create DataFrames

Convert the raw data into pandas DataFrames for easier analysis.
#%%
# Create DataFrames
customers_data_df = pd.DataFrame(customers_data_raw)
plans_data_df = pd.DataFrame(plans_data_raw)
billing_data_df = pd.DataFrame(billing_data_raw)  # Now contains prepaid transactions
network_data_df = pd.DataFrame(network_data_raw)

print("DataFrames created successfully!")
print(f"Customers DataFrame shape: {customers_data_df.shape}")
print(f"Plans DataFrame shape: {plans_data_df.shape}")
print(f"Billing/Transactions DataFrame shape: {billing_data_df.shape}")
print(f"Network DataFrame shape: {network_data_df.shape}")
#%% md
## Analyze Prepaid-Specific Features

Let's examine the new prepaid-specific columns we've added.
#%%
# Analyze prepaid-specific columns
print("=== PREPAID-SPECIFIC FEATURES ANALYSIS ===")
print("\nNew prepaid columns in the dataset:")
prepaid_columns = [
    'topup_frequency', 'topup_amount_avg', 'total_topup_3m', 
    'data_usage_last_30d', 'call_minutes_last_30d', 
    'last_topup_channel', 'days_since_last_activity', 'device_type'
]

for col in prepaid_columns:
    if col in customers_data_df.columns:
        print(f"✓ {col}")
    else:
        print(f"✗ {col} - Missing!")

print("\n=== SAMPLE PREPAID DATA ===")
display(customers_data_df[prepaid_columns].head())

print("\n=== PREPAID USAGE PATTERNS ===")
print("\nTop-up Frequency Distribution:")
print(customers_data_df['topup_frequency'].value_counts())

print("\nDevice Type Distribution:")
print(customers_data_df['device_type'].value_counts())

print("\nTop Payment Channels:")
print(customers_data_df['last_topup_channel'].value_counts().head(10))

print("\n=== PREPAID TRANSACTION DATA ===")
if 'transaction_id' in billing_data_df.columns:
    print("✓ Billing data converted to prepaid transactions")
    print(f"Total transactions: {len(billing_data_df)}")
    print("\nTransaction columns:")
    print(billing_data_df.columns.tolist())
    print("\nSample transactions:")
    display(billing_data_df[['transaction_id', 'package_id', 'transaction_date', 'topup_amount', 'payment_channel']].head())
else:
    print("⚠ Still using postpaid billing structure")
#%% md
## Generate ML Features

Transform the raw data into machine learning features.
#%%
from backend.app.utils.data_generators_prepaid.raw_data_generators.example import transform_to_churn_features

ml_feature_dfs = []

for customer in customers:
    ml_data = transform_to_churn_features(customer)
    ml_feature_dfs.append(ml_data)

ml_features_df = pd.concat(ml_feature_dfs)
print(f"ML features DataFrame shape: {ml_features_df.shape}")
#%% md
## Export Data

Save the generated prepaid data to CSV files.
#%%
import os 

# Create output directory for prepaid data
demo_data_dir = os.path.join(r'/mnt/c/Users/<USER>/Downloads/', 'prepaid_demo_data') 
os.makedirs(demo_data_dir, exist_ok=True) 
print(f"Output directory: {demo_data_dir}")
print(f"ML features file: {os.path.join(demo_data_dir, 'ml_features_prepaid.csv')}")

# Export all DataFrames with prepaid naming
ml_features_df.to_csv(os.path.join(demo_data_dir, 'ml_features_prepaid.csv'), index=False)
customers_data_df.to_csv(os.path.join(demo_data_dir, 'customers_data_prepaid.csv'), index=False) 
plans_data_df.to_csv(os.path.join(demo_data_dir, 'plans_data_prepaid.csv'), index=False, na_rep='') 
billing_data_df.to_csv(os.path.join(demo_data_dir, 'transactions_data_prepaid.csv'), index=False) 
network_data_df.to_csv(os.path.join(demo_data_dir, 'network_data_prepaid.csv'), index=False)

print("\n=== FILES EXPORTED ===")
print("✓ ml_features_prepaid.csv")
print("✓ customers_data_prepaid.csv")
print("✓ plans_data_prepaid.csv")
print("✓ transactions_data_prepaid.csv (formerly billing_data.csv)")
print("✓ network_data_prepaid.csv")
print(f"\nAll prepaid data exported to: {demo_data_dir}")