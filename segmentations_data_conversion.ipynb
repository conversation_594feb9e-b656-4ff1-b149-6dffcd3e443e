#%%
#!/usr/bin/env python
# coding: utf-8

import sys
import pandas as pd
import numpy as np

### DEFINE HELPER FUNCTIONS ###

# Decompose column method
def decompose(colname, raw_df, processed_df, scaleRangeMin = 1, scaleRangeMax = 5, nullMode = 'exc', invert_par = 0):
    # Index into data frame column
    column = raw_df.loc[:, colname]

    # Get sorted unique values in column
    uniques = sorted(column.dropna().unique())

    # Exclude all-null columns
    if len(uniques) > 0:
        appended_col_names = [colname + '__' + str(x) for x in uniques]

        for i in range(len(appended_col_names)):
            # Instantiate new column as replica of input column to be decomposed
            new_col = column.copy()

            # Update non-null values not equal to unique value to scaleRangeMin (default = 1)
            new_col[(column != uniques[i]) & ~(column.isnull())] = scaleRangeMin

            # Update non-null values equal to unique value to scaleRangeMax (default = 5)
            new_col[(column == uniques[i]) & ~(column.isnull())] = scaleRangeMax

            # Invert if specified
            if invert_par == 1:
                new_col = invert(new_col, scaleRangeMin, scaleRangeMax)

            # Handle nulls of new column according to specified nullMode behavior
            null_handled = null_handler(new_col, nullMode)

            # Append decomposed column
            processed_df[appended_col_names[i]] = null_handled

# Input: Raw data column to be scaled and scaling parameters
# Output: Scaled data column
def scale_column(col, minMode = 'def', min_set = 1, maxMode = 'def', max_set = 5, scaleRangeMin = 1, scaleRangeMax = 5):
    # Define minimum dependent on scale setting
    if minMode == 'def':
        min = col.min()
    elif minMode == 'set':
        min = min_set
    else: raise ValueError('minMode must be "def" (default) or "set" if specific minimum is provided')

    # Define maximum dependent on scale setting
    if maxMode == 'def':
        max = col.max()
    elif maxMode == 'set':
        max = max_set
    else: raise ValueError('maxMode must be "def" (default) or "set" if specific minimum is provided')

    # If minimum and maximum value of column are the same, fill all non-NaN values with scaleRangeMax value (default is 5)
    if min == max:
        new_col = col.replace(min, scaleRangeMax)
    else:
        # Apply scale function to each element of the raw data column
        new_col = col.apply(scale, min = min, max = max, scaleRangeMin = scaleRangeMin, scaleRangeMax = scaleRangeMax)

    return new_col

# Scaling function to be applied to each value; default scale is 1 to 5
def scale(x, min, max, scaleRangeMin = 1, scaleRangeMax = 5):
    # Check inputs for validity
    if max <= min:
        raise ValueError('min must be strictly less than max')
    if scaleRangeMax <= scaleRangeMin:
        raise ValueError('scaleRangeMin must be strictly less than scaleRangeMax')

    # Return NaN if input value is NaN or out of specified range for column; otherwise scale value
    if np.isnan(x) or x < min or x > max:
        return np.nan
    else:
        return ((abs(x - min) / abs(max - min)) * abs(scaleRangeMax - scaleRangeMin)) + scaleRangeMin

# Null handling function that implements null behavior according to 'NullMode' parameter
def null_handler(col, nullMode):
    if nullMode == 'exc':
        handled_col = col
    elif nullMode == 'zero':
        handled_col = col.replace(np.nan, 0)
    elif nullMode == 'mean':
        mean = col.mean()
        handled_col = col.replace(np.nan, mean)
    else:
        raise ValueError("Null mode input parameter must be 'exc', 'zero', or 'mean'")

    return handled_col

# Column inverter that flips directionality of values in SCALED columns; default scale is 1 to 5
# (e.g. value of 1 on scale of 1 to 5 becomes a 5, and value of 5 becomes 1)
def invert(col, scaleRangeMin = 1, scaleRangeMax = 5):
    delta_factor = scaleRangeMax + scaleRangeMin
    new_col = col.apply(lambda x: delta_factor - x if not np.isnan(x) else x)
    return new_col

# Validate parameter values function
def validate_param_vals(parameters, colDType):
    colName = parameters['colName'].values[0]
    include = parameters['include'].values[0]
    varType = parameters['varType'].values[0].lower()
    nullMode = parameters['nullMode'].values[0].lower()
    decompose = parameters['decompose'].values[0]
    scale = parameters['scale'].values[0]
    minMode = parameters['minMode'].values[0].lower()
    min = parameters['min'].values[0]
    maxMode = parameters['maxMode'].values[0].lower()
    max = parameters['max'].values[0]
    invert = parameters['invert'].values[0]

    if include not in [1, 0]:
        raise ValueError("Scale input parameter must be 0 or 1 for column: " + colName)

    # Validate input parameters when variable is included
    if include == 1:
        if varType not in ['cat', 'cont']:
            raise ValueError("Variable type input parameter must be 'cat' or 'cont' for included column: " + colName)

        if nullMode not in ['exc', 'zero', 'mean']:
            raise ValueError("Null mode input parameter must be 'exc', 'zero', or 'mean' for included column: " + colName)

        if decompose not in [1, 0]:
            raise ValueError("Decompose input parameter must be 0 or 1 for included column: " + colName)

        # Validate input parameters when variable is included and not decomposed
        if decompose == 0:
            if scale not in [1, 0]:
                raise ValueError("Scale input parameter must be 0 or 1 for included, non-decomposed column: " + colName)

            # Validate scale input parameters when variable is included, not decomposed, and scaled
            if scale == 1:
                if minMode not in ['def', 'set']:
                    raise ValueError("Min mode input parameter must be 'def' or 'set' for included, non-decomposed, scaled column: " + colName)

                if minMode == 'set':
                    if not (isinstance(min, (int, float, complex)) and not isinstance(min, bool)):
                        raise ValueError("Set minimum input parameter must be numeric type for included, non-decomposed, scaled column: " + colName)

                if maxMode not in ['def', 'set']:
                    raise ValueError("Max mode input parameter must be 'def' or 'set' for included, non-decomposed, scaled column: " + colName)

                if maxMode == 'set':
                    if not (isinstance(max, (int, float, complex)) and not isinstance(max, bool)):
                        raise ValueError("Set maximum input parameter must be numeric type for included, non-decomposed, scaled column: " + colName)

                if colDType == object:
                    raise ValueError("Cannot scale a column that contains strings. Scale input "
                                     "parameter must be 0 for column: " + colName)

        else:
            if scale != 0:
                raise ValueError("Scale input parameter must be 0 for included, decomposed "
                                 "column: " + colName)

        if invert not in [1, 0]:
            raise ValueError("Invert input parameter must be 0 or 1 for included column: " + colName)


### DEFINE MAIN FUNCTION ###

# Input: Raw data file path, output data file path name, optional scale range (default 1 to 5)
# Output: Processed csv
def main(raw_data_file_path, output_data_file_path, scaleRangeMin = 1, scaleRangeMax = 5):

    if raw_data_file_path == output_data_file_path:
        raise ValueError("Input data file path and output file path must be different!")

    # Open file
    infile = open(raw_data_file_path, encoding="utf-8", errors="ignore")

    # Create raw data frame
    raw_data = pd.read_csv(infile, header=11)

    infile.close()

    # Drop first blank column of raw data frame
    raw_data.drop(raw_data.columns[0], axis=1, inplace = True)

    # Open file
    infile = open(raw_data_file_path, encoding="utf-8", errors="ignore")

    # Create column parameter data frame
    column_params = pd.read_csv(infile, header=None, nrows=12, index_col=0).T

    infile.close()

    # Drop blank column
    column_params.drop(column_params.columns[len(column_params.columns)-2], axis=1, inplace = True)

    # Fix column name capitalization
    column_params = column_params.rename(columns={'Include': 'include'})

    # Set column data types
    column_params = column_params.astype(dtype = {'include': 'float',
              'varType': 'str',
              'nullMode': 'str',
              'decompose': 'float',
              'scale': 'float',
              'minMode': 'str',
              'min': 'float',
              'maxMode': 'str',
               'max': 'float',
               'invert':'float',
               'colName': 'str'})

    # Instatiate empty new data frame
    output_data = pd.DataFrame()

    # Loop through raw data and construct output data frame
    for column_name in raw_data.columns:
        # Get data adjustment parameters for data column
        parameters = column_params.loc[column_params['colName'] == column_name]

        # Check column name uniqueness
        if len(parameters) != 1:
            raise ValueError('Duplicate column name found. Column names must be unique! Duplicate column name: ' + column_name)

        # Validate parameter values
        validate_param_vals(parameters, raw_data.dtypes[column_name])

        # Only include specified columns in output data
        if parameters['include'].values[0] == 1:
            # Decompose column if specified (default scale range is between 1 and 5)
            if parameters['decompose'].values[0] == 1:
                decompose(column_name, raw_data, output_data, float(scaleRangeMin), float(scaleRangeMax),
                        parameters['nullMode'].values[0], parameters['invert'].values[0])

            else:
                # Scale column if specified according to scale parameters (default scale range is between 1 and 5)
                if parameters['scale'].values[0] == 1:
                    scaled_col = scale_column(raw_data[column_name], minMode = parameters['minMode'].values[0],
                                                            min_set = parameters['min'].values[0], maxMode = parameters['maxMode'].values[0],
                                                            max_set = parameters['max'].values[0], scaleRangeMin = float(scaleRangeMin), scaleRangeMax = float(scaleRangeMax))
                    # Exclude all-null columns handle nulls according to null-mode
                    if not scaled_col.isnull().all():
                        # Invert column if specified
                        if parameters['invert'].values[0] == 1:
                            inverted_col = invert(scaled_col, scaleRangeMin, scaleRangeMax)
                            final_col = null_handler(inverted_col, parameters['nullMode'].values[0])
                        else:
                            final_col = null_handler(scaled_col, parameters['nullMode'].values[0])

                        output_data[column_name] = final_col

                # If column is included and specified to not be scaled, include raw data column
                else:
                    unscaled_col = raw_data[column_name]
                    if not unscaled_col.isnull().all():
                        # Invert column if specified
                        if parameters['invert'].values[0] == 1:
                            inverted_col = invert(unscaled_col, scaleRangeMin, scaleRangeMax)
                            final_col = null_handler(inverted_col, parameters['nullMode'].values[0])
                        else:
                            final_col = null_handler(unscaled_col, parameters['nullMode'].values[0])

                        output_data[column_name] = final_col

    # Output processed data to csv file
    print(f"About to save {len(output_data)} rows and {len(output_data.columns)} columns to: {output_data_file_path}")
    output_data.to_csv(output_data_file_path, na_rep = np.nan, index=False)
    print(f"File saved successfully to: {output_data_file_path}")

#if __name__ == "__main__":
 #   if len(sys.argv) != 3 and len(sys.argv) != 5:
  #      raise ValueError('Command line input syntax is invalid. Correct syntax: "py [script_name.py] ["full_input_file_path"] ["full_output_file_path"] [scaleRangeMin (optional: default 1)] [scaleRangeMax # (optional: default 5)]"')
    #main(*sys.argv[1:])
#%%
read_path = "/mnt/c/Users/<USER>/Downloads/altman_telco_use_of_ai_98n.csv"
save_path = "/mnt/c/Users/<USER>/Downloads/altman_telco_use_of_ai_98n_processed.csv"
#%%
main(read_path, save_path, scaleRangeMin=1, scaleRangeMax=5)
#%%
