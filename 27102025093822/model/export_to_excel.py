#!/usr/bin/env python3
"""
Excel Export Script for Collaborative Filtering Model Results
Created for timestamp: 27102025093822
"""

import pandas as pd
import numpy as np
import os
import sys
import pickle
from pathlib import Path

def load_model_data():
    """Load model data from the current directory and parent directories"""
    current_dir = Path(__file__).parent
    parent_dir = current_dir.parent
    
    data = {}
    
    # Try to load existing output.xlsx from parent directory
    output_file = parent_dir / "output.xlsx"
    if output_file.exists():
        print(f"Loading existing output.xlsx from {output_file}")
        try:
            # Read all sheets from the existing Excel file
            excel_data = pd.read_excel(output_file, sheet_name=None)
            data.update(excel_data)
            print(f"Loaded {len(excel_data)} sheets from existing output.xlsx")
        except Exception as e:
            print(f"Error loading output.xlsx: {e}")
    
    # Try to load history.pkl
    history_file = parent_dir / "history.pkl"
    if history_file.exists():
        try:
            with open(history_file, 'rb') as f:
                history = pickle.load(f)
            
            # Convert history to DataFrame
            history_df = pd.DataFrame(history)
            data['Training_History'] = history_df
            print(f"Loaded training history with {len(history_df)} epochs")
        except Exception as e:
            print(f"Error loading history.pkl: {e}")
    
    # Try to load hyperparameters
    hps_file = parent_dir / "collaborative_filtering_hps.json"
    if hps_file.exists():
        try:
            import json
            with open(hps_file, 'r') as f:
                hps = json.load(f)
            
            # Convert hyperparameters to DataFrame
            hps_df = pd.DataFrame([hps])
            data['Hyperparameters'] = hps_df
            print(f"Loaded hyperparameters: {list(hps.keys())}")
        except Exception as e:
            print(f"Error loading hyperparameters: {e}")
    
    return data

def create_sample_data():
    """Create sample data if no model data is available"""
    print("Creating sample data for demonstration...")
    
    # Sample user clusters
    np.random.seed(42)
    n_users = 100
    n_clusters = 5
    
    user_clusters = pd.DataFrame({
        'user_id': range(n_users),
        'cluster': np.random.randint(0, n_clusters, n_users),
        'cluster_probability': np.random.random(n_users)
    })
    
    # Sample latent factors
    n_factors = 10
    user_factors = pd.DataFrame(
        np.random.randn(n_users, n_factors),
        columns=[f'factor_{i}' for i in range(n_factors)]
    )
    user_factors['user_id'] = range(n_users)
    
    # Sample recommendations
    recommendations = pd.DataFrame({
        'user_id': np.repeat(range(20), 5),  # Top 20 users, 5 recommendations each
        'item_id': np.random.randint(0, 50, 100),
        'predicted_rating': np.random.uniform(3.0, 5.0, 100),
        'rank': np.tile(range(1, 6), 20)
    })
    
    return {
        'User_Clusters': user_clusters,
        'User_Factors': user_factors,
        'Recommendations': recommendations
    }

def export_to_excel(data, output_path):
    """Export data to Excel file"""
    try:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Export to Excel with multiple sheets
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for sheet_name, df in data.items():
                # Clean sheet name (Excel has restrictions)
                clean_sheet_name = sheet_name.replace('/', '_').replace('\\', '_')[:31]
                
                df.to_excel(writer, sheet_name=clean_sheet_name, index=False)
                print(f"Exported sheet: {clean_sheet_name} ({len(df)} rows)")
        
        print(f"\n✅ Successfully exported to: {output_path}")
        print(f"📊 Total sheets: {len(data)}")
        
        # Show file size
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"📁 File size: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error exporting to Excel: {e}")
        return False

def main():
    """Main export function"""
    print("🚀 Starting Excel export process...")
    print(f"📂 Working directory: {os.getcwd()}")
    
    # Define output path
    output_path = "/mnt/c/Users/<USER>/Downloads/27102025093822.xlsx"
    print(f"🎯 Target file: {output_path}")
    
    # Load data
    data = load_model_data()
    
    # If no data found, create sample data
    if not data:
        print("⚠️  No model data found, creating sample data...")
        data = create_sample_data()
    
    # Export to Excel
    success = export_to_excel(data, output_path)
    
    if success:
        print("\n🎉 Export completed successfully!")
        print(f"📋 You can now find your Excel file at: {output_path}")
    else:
        print("\n💥 Export failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
