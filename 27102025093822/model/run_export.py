#!/usr/bin/env python3
"""
Quick runner script to export Excel file
Usage: python run_export.py
"""

import subprocess
import sys
import os

def main():
    """Run the export script"""
    script_path = os.path.join(os.path.dirname(__file__), "export_to_excel.py")
    
    print("🚀 Running Excel export...")
    print(f"📜 Script: {script_path}")
    
    try:
        # Run the export script
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True)
        
        # Print output
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("Errors:", result.stderr)
        
        if result.returncode == 0:
            print("✅ Export completed successfully!")
        else:
            print(f"❌ Export failed with return code: {result.returncode}")
            
    except Exception as e:
        print(f"💥 Error running export: {e}")

if __name__ == "__main__":
    main()
