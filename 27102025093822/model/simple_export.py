#!/usr/bin/env python3
"""
Simple script to export output.xlsx directly to Downloads folder
No modifications, just a direct copy
"""

import shutil
import os
from pathlib import Path

def main():
    """Copy output.xlsx directly to Downloads folder"""
    
    # Source file (output.xlsx in 27102025093822 directory)
    current_dir = Path(__file__).parent
    source_file = current_dir.parent / "output.xlsx"
    
    # Destination file in Downloads
    destination_file = "/mnt/c/Users/<USER>/Downloads/27102025093822.xlsx"
    
    print(f"📂 Source: {source_file}")
    print(f"🎯 Destination: {destination_file}")
    print(f"🔍 Current working directory: {os.getcwd()}")
    print(f"📁 Script location: {Path(__file__).parent}")
    print(f"📁 Parent directory: {Path(__file__).parent.parent}")

    # Check if source file exists
    if not source_file.exists():
        print(f"❌ Source file not found: {source_file}")
        print(f"🔍 Absolute path: {source_file.absolute()}")
        return 1
    
    try:
        # Create destination directory if it doesn't exist
        os.makedirs(os.path.dirname(destination_file), exist_ok=True)
        
        # Copy the file directly
        shutil.copy2(source_file, destination_file)
        
        # Check file size
        file_size = os.path.getsize(destination_file) / (1024 * 1024)  # MB
        
        print(f"✅ File copied successfully!")
        print(f"📁 File size: {file_size:.2f} MB")
        print(f"📋 You can find your file at: {destination_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error copying file: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
