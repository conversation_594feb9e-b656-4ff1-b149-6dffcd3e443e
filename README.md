
# smart_segments

## Collaborative Filtering
In order to run Collaborative Filtering on a new ratings dataset, follow these instructions:
1. Open the [Collaborative_Filtering.ipynb](https://octocat.altvil.com/AIT/smart_segments/blob/develop/Collaborative_Filtering.ipynb) file.
2. Modify the content of the second cell according to the context.
	- **data_location:** path to the ratings CSV file. The shape of this dataset should be (_number_of_users_, _number_of_items_).
	- **user_col_name:** column name that identifies users.
	- **min_rating:** minimum rating (worst possible rating).
	- **max_rating:** maximum rating (best possible rating).
	- **test_pct:** percentage of the dataset to be used for validating the model.
	- **num_factors:** desired number of latent factors.
	- **model_hps:** dictionary of model's hyperparameters indicating learning_rate (_lr_), number of epochs (_epochs_) and number of samples per training iteration (_batch_size_).
3. Go to _Cell_ and select the _Run All_ option. During the execution, you will see progress messages related to the pipeline's state.
4. After the whole notebook is executed (it should take several minutes), a new directory (named according to the current UTC timestamp) will be created in the same location as the Collaborative Filtering notebook and it will contain a JSON file with the hyperparameters used for the execution, the generated model, the training history, a plot of the model loss, another plot of the model RMSE and a file called **output.xlsx**. This file will have one sheet called "Users" and another one called "Items", each of them containing the user/item name, the corresponding user/item index as well as the extracted biases and latent factors.

## Latent Factors Clustering
In order to run K-Means on the extracted latent factors obtained through the [Collaborative_Filtering.ipynb](https://octocat.altvil.com/AIT/smart_segments/blob/develop/Collaborative_Filtering.ipynb) file, follow these instructions:
1. Open the [Latent Factors Clustering.ipynb](https://octocat.altvil.com/AIT/smart_segments/blob/develop/Latent_Factors_Clustering.ipynb) file.
2. Modify the content of the second cell according to the context.
	- **data_location:** path to the _output.xlsx_ file. This file is generated after running Collaborative Filtering on the input data and it contains the latent factors for users and items.
	- **user_col_name:** column name that identifies users.
	- **entity:** whether to cluster the latent factors of the users or the items (possible values are _user_ or _item_).
	- **max_k_elbow_plot:** maximum number of clusters (_K_) to try in order to make the _Elbow plot_ (_K_ vs. Loss) and let the user analyze a reasonable value for _K_.
	- **num_iterations:** maximum number of iterations when executing K-Means.
	- **num_diff_inits:** number of different clusters' centroids initializations to try.
	- **num_clusters:** number of clusters to group latent factors into.
3. Go to _Cell_ and select the _Run All_ option. During the execution, you will see progress messages related to the pipeline's state.
4. After the whole notebook is executed (it should take several seconds), you will see a JSON file with the hyperparameters used for the execution, the _Elbow plot_ and the plot of the clustering loss on the _output.xlsx_ directory. In addition, two new sheets will be appended (or re-created) to the **output.xlsx** file, namely "Clusters (Users/Items)" and "Cluster assignment (Users/Items)". The former will contain the number of entities that belong to each cluster as well as the clusters' centroids, whereas the latter will show the cluster assignment for each data point.

## Latent Factors Visualization
In order to visualize the extracted latent factors' clustering in 2D (applying PCA and t-SNE) obtained through the [Latent Factors Clustering.ipynb](https://octocat.altvil.com/AIT/smart_segments/blob/develop/Latent_Factors_Clustering.ipynb) file, follow these instructions:
1. Open the [Latent_Factors_Visualization.ipynb](https://octocat.altvil.com/AIT/smart_segments/blob/develop/Latent_Factors_Visualization.ipynb) file.
2. Modify the content of the second cell according to the context.
	- **data_location:** path to the _output.xlsx_ file. This file is generated after running Collaborative Filtering and Latent Factors Clustering on the input data and it contains the latent factors for users and items as well as the clustering information.
	- **user_col_name:** column name that identifies users.
	- **entity:** whether to visualize the latent factors' clustering of the users or the items (possible values are _user_ or _item_).
3. Go to _Cell_ and select the _Run All_ option.
4. After the whole notebook is executed (it should take several seconds), you will see the PCA and the t-SNE entity segmentation plots on the _output.xlsx_ directory. In addition, two new sheets will be appended (or re-created) to the **output.xlsx** file, namely "PCA (Users/Items)" and "tSNE (Users/Items)". The former will contain the dimensionality reduction result after applying PCA on the latent factors, whereas the latter will show the same information but using t-SNE.