#%%
import sys
print(sys.executable)
print(sys.path)
#%%
from collaborative_filtering import CollaborativeFiltering
#%%
data_location = "/mnt/c/Users/<USER>/Downloads/segmentation_2910/altman_telco_use_of_ai_86n_processed.csv" ##"/Users/<USER>/Documents/Data Science/Sports & News Survey/processed_output_v08262020.csv"
user_col_name = "sys_Respondent" #"sys_RespNum"
min_rating = 1.0 #0.5
max_rating = 5.0
test_pct = 0.2 #0.1
num_factors = 10 #5 #Epochs = 10, batch size = 128
model_hps = {"lr": 0.001, "epochs": 6, "batch_size": 64}
min_row_density_threshold = 0.15
#%%
collaborative_filtering = CollaborativeFiltering(data_location=data_location,
                                                 user_col_name=user_col_name,
                                                 min_rating=min_rating, max_rating=max_rating,
                                                 test_pct=test_pct, num_factors=num_factors,
                                                 model_hps=model_hps,
                                                 min_row_density_threshold=min_row_density_threshold)
collaborative_filtering.run()
#%%
