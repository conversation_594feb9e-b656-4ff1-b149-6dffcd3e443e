#%%
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

plt.style.use(["ggplot", "seaborn-ticks"])
#%%
data_location = "/mnt/c/Users/<USER>/Downloads/27102025093822.xlsx"
user_col_name = "sys_Respondent"
entity = "user"
#%% md
## Read results
#%%
if entity == "user":
    sheet_name = "Users"
    entity_info_cols = [user_col_name, "user_idx"]
    cluster_info_sheet_name = "Clusters (Users)"
    cluster_assignment_sheet_name = "Cluster Assignment (Users)"
    num_entities_col = "number_of_users"
else:
    sheet_name = "Items"
    entity_info_cols = ["item", "item_idx"]
    cluster_info_sheet_name = "Clusters (Items)"
    cluster_assignment_sheet_name = "Cluster Assignment (Items)"
    num_entities_col = "number_of_items"
df = pd.read_excel(data_location, sheet_name=sheet_name).drop(entity_info_cols, axis=1)
clusters_df = pd.read_excel(data_location, sheet_name=cluster_info_sheet_name)
assignments_df = pd.read_excel(data_location, sheet_name=cluster_assignment_sheet_name)
#%%
color_map = {i: np.random.rand(3) for i in range(len(clusters_df))}
#%%
def build_df_for_plotting(technique: str, assignments_df: pd.DataFrame, color_map: dict)  \
    -> pd.DataFrame:
    """
    Build the Dataframe that is required for plotting the latent factors in 2D.

    :param technique: Whether to use PCA or t-SNE for dimensionality reduction
    :param assignments_df: Dataframe containing cluster assignment for each data point
    :param color_map: Map from cluster to color
    :return: Dataframe for plotting
    """
    dim_red = PCA(n_components=2) if technique == "pca" else TSNE(n_components=2)
    df_for_plotting = pd.DataFrame(dim_red.fit_transform(df)).rename(columns={0: 'X', 1: 'Y'})
    df_for_plotting = pd.concat([df_for_plotting, assignments_df["cluster"]], axis=1)
    df_for_plotting["color"] = df_for_plotting["cluster"].apply(lambda x: color_map[x])
    return df_for_plotting
#%%
def plot(entity: str, df_for_plotting: pd.DataFrame, clusters_df: pd.DataFrame, 
         data_location: str, technique: str):
    """
    Plot and save the latent factors in 2D using a different color for each cluster and indicating 
    the number of points that belong to each of them.

    :param entity: Whether to plot the latent factors for users or items
    :param df_for_plotting: Dataframe containing the information for plotting
    :param clusters_df: Dataframe containing the number of points per cluster and its centroids
    :param data_location: Path to the file containing latent factors and cluster information
    :param technique: Whether PCA or t-SNE was used for dimensionality reduction
    :return: None
    """
    plt.figure(figsize=(15, 15))
    plt.title(f"{entity} segmentation", fontdict={"fontsize": 20, "fontweight": "bold"})
    for c, grouped_df in df_for_plotting.groupby("cluster"):
        num_entities = clusters_df[clusters_df["cluster"] == c][num_entities_col].values[0]
        plt.scatter(grouped_df['X'], grouped_df['Y'], c=grouped_df["color"], 
                    label="Cluster {}: {}".format(c, num_entities))
    plt.legend(prop={"size": 15})
    fname = os.path.join(os.path.dirname(data_location), 
                         f"{technique}_{entity.lower()}_segmentation.png")
    plt.savefig(fname=fname, dpi=500, format="png", bbox_inches="tight")
    plt.show()
#%%
def save_reduced_df(technique: str, entity: str, data_location: str, assignments_df: pd.DataFrame,
                    entity_info_cols: list, df_for_plotting: pd.DataFrame):
    """
    Save the results of applying dimensionality reduction to the latent factors representing each 
    data point.

    :param technique: Whether PCA or t-SNE was used for dimensionality reduction
    :param entity: Whether to plot the latent factors for users or items
    :param data_location: Path to the file containing latent factors and cluster information
    :param assignments_df: Dataframe containing cluster assignment for each data point
    :param entity_info_cols: Column names for the entity and its indices
    :param df_for_plotting: Dataframe containing the information for plotting
    :return: None
    """
    if technique == "pca":
        dim_red_values_sheet_name = f"PCA ({entity})"
    else:
        dim_red_values_sheet_name = f"tSNE ({entity})"
    dim_red_df = pd.concat([assignments_df[entity_info_cols], df_for_plotting[['X', 'Y']]], axis=1)
    with pd.ExcelWriter(data_location, engine="openpyxl", mode='a') as writer:
        sheet_names = writer.book.sheetnames
        # Remove dimensionality reduction sheet using the given technique if it already exists.
        if dim_red_values_sheet_name in sheet_names:
            writer.book.remove(writer.book[dim_red_values_sheet_name])
        dim_red_df.to_excel(writer, index=False, sheet_name=dim_red_values_sheet_name)
    print(f"Results saved in {os.path.dirname(data_location)}/.")
#%% md
## PCA
#%%
technique = "pca"
df_for_plotting = build_df_for_plotting(technique=technique, assignments_df=assignments_df, 
                                        color_map=color_map)
plot(entity=sheet_name, df_for_plotting=df_for_plotting, clusters_df=clusters_df, 
     data_location=data_location, technique=technique)
save_reduced_df(technique=technique, entity=sheet_name, data_location=data_location, 
                assignments_df=assignments_df, entity_info_cols=entity_info_cols, 
                df_for_plotting=df_for_plotting)
#%% md
## t-SNE
#%%
technique = "tsne"
df_for_plotting = build_df_for_plotting(technique=technique, assignments_df=assignments_df, 
                                        color_map=color_map)
plot(entity=sheet_name, df_for_plotting=df_for_plotting, clusters_df=clusters_df, 
     data_location=data_location, technique=technique)
save_reduced_df(technique=technique, entity=sheet_name, data_location=data_location, 
                assignments_df=assignments_df, entity_info_cols=entity_info_cols, 
                df_for_plotting=df_for_plotting)
#%%

#%%
