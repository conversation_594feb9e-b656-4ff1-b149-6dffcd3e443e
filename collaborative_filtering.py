import datetime as dt
import json
import os
from typing import Union

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import tensorflow as tf
import math

plt.style.use(["ggplot", "seaborn-ticks"])
from six.moves import cPickle as pickle
from tensorflow.keras.layers import Dot, Embedding, Activation
from tensorflow.keras.losses import BinaryCrossentropy
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.regularizers import l2
from tensorflow.keras import backend


class CollaborativeFiltering:

    def __init__(self, data_location: str, user_col_name: str, min_rating: float, max_rating: float,
                 test_pct: float, num_factors: int, model_hps: dict, min_row_density_threshold: float):
        self.data_location = data_location
        self.user_col_name = user_col_name
        self.min_rating = min_rating
        self.max_rating = max_rating
        self.test_pct = test_pct
        self.num_factors = num_factors
        self.model_hps = model_hps
        self.min_row_density_threshold = min_row_density_threshold
        self.user_to_user_idx = None
        self.user_idx_to_user = None
        self.item_to_item_idx = None
        self.item_idx_to_item = None
        self.adj_min_rating = None
        self.adj_max_rating = None
        self.history = None
        self.raw_data = None

    def read_data(self) -> pd.DataFrame:
        """
        Read the data and format it as required.

        :param: None
        :return: Dataframe containing users, items and ratings
        """
        print("Reading data...")
        df = pd.read_csv(self.data_location)
        df = df.sort_values(by=[self.user_col_name])
        self.raw_data = df
        user_ids = df[self.user_col_name].unique().tolist()
        self.user_to_user_idx = {user: idx for idx, user in enumerate(user_ids)}
        self.user_idx_to_user = {idx: user for user, idx in self.user_to_user_idx.items()}
        item_ids = list(df.columns)
        item_ids.remove(self.user_col_name)
        self.item_to_item_idx = {item: idx for idx, item in enumerate(item_ids)}
        self.item_idx_to_item = {idx: item for item, idx in self.item_to_item_idx.items()}
        df = pd.melt(df, id_vars=[self.user_col_name], var_name="item_idx", value_name="rating")
        df = df.rename(columns={self.user_col_name: "user_idx"})
        df["user_idx"] = df["user_idx"].map(self.user_to_user_idx)
        df["item_idx"] = df["item_idx"].map(self.item_to_item_idx)
        # df["rating"] = df["rating"].fillna(value=0)
        df = df.dropna(subset=["rating"])
        df["rating"] = df["rating"].values.astype(dtype=np.float32)
        print("Data read.")
        return df

    def split_data(self, df: pd.DataFrame) -> dict:
        """
        Split the data into train and test sets.

        :param df: Dataframe containing users, items and ratings
        :return: Data splits
        """
        print("Splitting data...")
        df = df.sample(frac=1, random_state=42)
        X = df[["user_idx", "item_idx"]].values
        # Normalize the targets between 0 and 1 for making training easier.
        self.adj_min_rating = max(self.min_rating - 0.5, 0.0)
        self.adj_max_rating = self.max_rating + 0.5
        y = df["rating"] \
            .apply(lambda x: max(x - self.adj_min_rating, 0.0) /
                             (self.adj_max_rating - self.adj_min_rating)).values
        num_train_samples = int((1.0 - self.test_pct) * df.shape[0])
        data_splits = {"X_train": X[:num_train_samples], "X_test": X[num_train_samples:],
                       "y_train": y[:num_train_samples], "y_test": y[num_train_samples:]}
        print("Data split.")
        return data_splits

    def scale_to_original(self, y: np.array) -> np.array:
        """
        Put values into their original scale.

        :param y: Values to scale
        :return: Scaled values
        """
        return (y * (self.adj_max_rating - self.adj_min_rating)) + self.adj_min_rating

    def rmse(self, y_true: np.array, y_pred: np.array) -> float:
        """
        Measure the root mean square error between true values and predictions.

        :param y_true: True values
        :param y_pred: Predicted values
        :return: Measured root mean square error
        """
        y_true, y_pred = self.scale_to_original(y=y_true), self.scale_to_original(y=y_pred)
        return backend.sqrt(backend.mean(backend.square(y_pred - y_true), axis=-1))

    def train_model(self, data_splits: dict) -> tf.keras.Model:
        """
        Train the Collaborative Filtering model using the given data splits and save the training
        history.

        :param data_splits: Data splits
        :return: Trained model
        """
        print("Training model...")
        model = CFModel(num_users=len(self.user_idx_to_user), num_items=len(self.item_idx_to_item),
                        embedding_size=self.num_factors)
        model.compile(loss=BinaryCrossentropy(), optimizer=Adam(lr=self.model_hps["lr"]),
                      metrics=[self.rmse])
        history = model.fit(x=data_splits["X_train"], y=data_splits["y_train"],
                            batch_size=self.model_hps["batch_size"],
                            epochs=self.model_hps["epochs"], verbose=1,
                            validation_data=(data_splits["X_test"], data_splits["y_test"]))
        self.history = history.history
        print("Model trained.")
        return model

    def save_hyperparameters(self, directory: str):
        """
        Save hyperparameters used for the execution.

        :param directory: Directory to save the hyperparameters.
        :return: None
        """
        hps_dict = {
            "data_location": self.data_location,
            "user_col_name": self.user_col_name,
            "min_rating": self.min_rating,
            "max_rating": self.max_rating,
            "test_pct": self.test_pct,
            "num_factors": self.num_factors,
            "model_hps": self.model_hps,
            "min_row_density_threshold": self.min_row_density_threshold
        }
        hps_file_path = os.path.join(directory, "collaborative_filtering_hps.json")
        with open(hps_file_path, mode='w') as json_file:
            json.dump(obj=hps_dict, fp=json_file, indent=2)
    
    def calculate_preds(self, user_df: pd.DataFrame, item_df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate predicted DataFrame for full data set
        Formula: (1/(1+EXP(-(MMULT(user_LVs,feature_LVs)+userBias+featureBias))))*4+1
        :param user_df: user embeddings with user IDs
        :param item_df: feature embeddings with feature names
        :return: full prediction DataFrame
        """
        print("Predicting full data set...")

        prediction_df = pd.DataFrame({self.user_col_name: user_df[self.user_col_name]})
        user_column_df = pd.DataFrame({'user_idx': user_df['user_idx']})
        latent_factors_list = ["latent_factor_"+str(x) for x in range(self.num_factors)]
        dot_product_array = np.dot(user_df[latent_factors_list], item_df[latent_factors_list].T)
        dot_product_df = pd.DataFrame(dot_product_array)
        dot_product_df.columns = item_df['item']
        dot_product_df.reset_index()
        dot_product_df = pd.concat([user_column_df, dot_product_df], axis=1)

        # Set indices for speeding up data access
        dot_product_df.set_index(['user_idx'], inplace=True, drop=False)
        user_df.set_index(['user_idx'], inplace=True, drop=False)
        item_df.set_index(['item'], inplace=True, drop=False)

        for column in item_df.index:
            prediction_col = []
            for user_idx in user_df['user_idx']:
                user_bias = user_df.at[user_idx, 'bias']
                feature_bias = item_df.at[column, 'bias']
                pred_value = (1/(1+math.exp(-(dot_product_df.at[user_idx, column]+user_bias+feature_bias))))*(self.adj_max_rating - self.adj_min_rating)+self.adj_min_rating
                prediction_col.append(pred_value)

            prediction_df[column] = prediction_col
            #print(f'Column prediction: {column} stored at {dt.datetime.utcnow().strftime("%d%m%Y%H%M%S")}')

        prediction_df.reset_index()
        print("Predictions complete.")

        return(prediction_df)

    def correlate_dfs(self, raw_df: pd.DataFrame, embeddings_df: pd.DataFrame, min_row_density_threshold: float) -> Union[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Create correlation matrix between user embeddings and user raw data and determine feature importance

        :param raw_df: raw data frame
        :param embeddings_df: user embeddings data frame
        :param min_row_density_threshold: minimum rows of data required per feature to calculate feauture correlation (as % of total raw data rows)
        :return: embedding/user correlation data frame
        :return: features ranked by importance data frame
        :return: embedding/embedding correlation data frame
        """
        print("Calculating feature correlations with latent variables...")

        if min_row_density_threshold > 1 or min_row_density_threshold < 0:
            raise Exception("min_row_density_threshold must be value between 0 and 1 inclusive")

        min_rows_for_corr = int(min_row_density_threshold * len(raw_df))

        df = raw_df.copy()
        df = df.drop([self.user_col_name], axis=1)
        correlation_df = pd.DataFrame()

        var_skip_count = 0
        for feature in df.columns:
            feature_corr_list = []
            if df[feature].count() >= min_rows_for_corr:
                if df[feature].nunique() > 1:
                    for latent_var in embeddings_df.columns:
                        corr = df[feature].corr(embeddings_df[latent_var])
                        feature_corr_list.append(corr)
                else:
                    feature_corr_list = [0] * len(embeddings_df.columns)
                    print(f"Correlation for column '{feature}' not calculated: zero variance")
                    var_skip_count += 1
            else:
                feature_corr_list = [0] * len(embeddings_df.columns)
                print(f"Correlation for column '{feature}' not calculated: below minimum {min_rows_for_corr} data points ({df[feature].count()})")
                var_skip_count += 1

            feature_corr_series = pd.Series(feature_corr_list,  index = embeddings_df.columns)
            correlation_df = pd.concat([correlation_df, feature_corr_series], axis = 1)

        correlation_df = correlation_df.T
        correlation_df = correlation_df.reset_index(drop=True)
        latent_vars_list = correlation_df.columns
        
        item_idx_df = pd.DataFrame().from_dict({"item": list(self.item_to_item_idx.keys()),
                                        "item_idx": list(self.item_to_item_idx.values())})
        correlation_df = pd.concat([item_idx_df, correlation_df], axis=1)
        
        feature_rank_df = pd.DataFrame({'Rank': range(1,len(correlation_df)+1)})
        feature_rank_df.set_index('Rank', inplace=True, drop=False)

        for latent_var in latent_vars_list:
            rank_col_name = latent_var + '_rank'
            rank_col_dir_name = latent_var + '_rankdir'
            correlation_df[rank_col_name] = correlation_df[latent_var].abs().rank(method = 'first', ascending = False)

            rank_col_dir = correlation_df[latent_var] >= 0
            rank_col_dir.replace([True, False], [1,-1], inplace = True)
            correlation_df[rank_col_dir_name] = rank_col_dir

            latent_var_item_col_name = latent_var + '_item'
            tmp_feature_rank_df = correlation_df.copy()[['item', rank_col_name, latent_var]]
            tmp_feature_rank_df.rename(columns={'item': latent_var_item_col_name}, inplace=True)
            tmp_feature_rank_df.sort_values(by=rank_col_name)
            tmp_feature_rank_df.set_index(rank_col_name, inplace=True, drop=True)

            feature_rank_df = pd.concat([feature_rank_df,tmp_feature_rank_df], axis=1)

        print(f'Correlations calculated for {len(correlation_df)-var_skip_count}/{len(correlation_df)} features ({round(float((len(correlation_df)-var_skip_count)/len(correlation_df)*100),2)}%). {var_skip_count} features did not meet correlation criteria.')
        embedding_corr_df = embeddings_df.corr()
        return([correlation_df, feature_rank_df, embedding_corr_df])

    def create_comp_plot(self, train_values: np.array, test_values: np.array, title: str,
                         y_label: str, x_label: str, x_ticks: list, x_tick_labels: list,
                         legend: list, legend_loc: str, fname: str):
        """
        Create a comparative plot according to the given parameters and save it.

        :param train_values: Training values to plot
        :param test_values: Testing values to plot
        :param title: Title of the plot
        :param y_label: Label for the Y-axis
        :param x_label: Label for the X-axis
        :param x_ticks: Number of ticks on the X-axis
        :param x_tick_labels: Labels for the X-axis' ticks
        :param legend: Legend for the plot
        :param legend_loc: Location for the legend
        :param fname: Destination path for the plot
        :return: None
        """
        print("Creating plot...")
        fig, ax = plt.subplots(figsize=(8, 5))
        ax.plot(train_values, marker='^')
        ax.plot(test_values, marker='s')
        plt.title(label=title, fontdict={"fontweight": "bold"})
        plt.ylabel(y_label)
        plt.xlabel(x_label)
        ax.set_xticks(x_ticks)
        ax.set_xticklabels(x_tick_labels)
        plt.legend(legend, loc=legend_loc)
        plt.savefig(fname=fname, dpi=500, format="png", bbox_inches="tight")
        print("Plot created.")
        plt.show()

        
    def save_results(self, model: tf.keras.Model):
        """
        Prepare the results and save them into disk.

        :param model: Trained model
        :return: None
        """
        timestamp = dt.datetime.utcnow().strftime("%d%m%Y%H%M%S")
        os.makedirs(timestamp)
        self.save_hyperparameters(directory=timestamp)
        user_idx_df = pd.DataFrame() \
            .from_dict({self.user_col_name: list(self.user_to_user_idx.keys()),
                        "user_idx": list(self.user_to_user_idx.values())})
        user_bias_df = pd.DataFrame(model.user_bias.get_weights()[0]).rename(columns={0: "bias"})
        user_emb_df = pd.DataFrame(model.user_embedding.get_weights()[0])
        user_emb_df.columns = [f"latent_factor_{lf}" for lf in user_emb_df.columns]
        user_df = pd.concat([user_idx_df, user_bias_df, user_emb_df], axis=1)
        item_idx_df = pd.DataFrame().from_dict({"item": list(self.item_to_item_idx.keys()),
                                                "item_idx": list(self.item_to_item_idx.values())})
        item_bias_df = pd.DataFrame(model.item_bias.get_weights()[0]).rename(columns={0: "bias"})
        item_emb_df = pd.DataFrame(model.item_embedding.get_weights()[0])
        item_emb_df.columns = [f"latent_factor_{lf}" for lf in item_emb_df.columns]
        item_df = pd.concat([item_idx_df, item_bias_df, item_emb_df], axis=1)
        
        prediction_df = self.calculate_preds(user_df, item_df)
        corr_df, feature_rank_df, embedding_corr_df = self.correlate_dfs(self.raw_data, pd.concat([user_bias_df, user_emb_df], axis=1), self.min_row_density_threshold)

        print("Saving results...")

        with pd.ExcelWriter(os.path.join(timestamp, "output.xlsx"), engine="xlsxwriter") as writer:
            self.raw_data.to_excel(writer, index=False, sheet_name="Flat File")
            prediction_df.to_excel(writer, index=False, sheet_name="Predictions")
            user_df.to_excel(writer, index=False, sheet_name="Users")
            item_df.to_excel(writer, index=False, sheet_name="Items")
            embedding_corr_df.to_excel(writer, index=True, sheet_name="Embedding Self Cor")
            corr_df.to_excel(writer, index=False, sheet_name="Embedding Cor")
            feature_rank_df.to_excel(writer, index=False, sheet_name="Embedding Feat Rank")

        model.save(os.path.join(timestamp, "model"))
        with open(os.path.join(timestamp, "history.pkl"), mode="wb") as pickle_file:
            pickle.dump(self.history, pickle_file)
        self.create_comp_plot(train_values=self.history["loss"],
                              test_values=self.history["val_loss"], title="Model loss",
                              y_label="Loss", x_label="Epoch",
                              x_ticks=list(range(self.model_hps["epochs"])),
                              x_tick_labels=list(range(1, self.model_hps["epochs"] + 1)),
                              legend=["train", "test"], legend_loc="upper right",
                              fname=os.path.join(timestamp, "model_loss.png"))
        self.create_comp_plot(train_values=self.history["rmse"],
                              test_values=self.history["val_rmse"], title="Model RMSE",
                              y_label="RMSE", x_label="Epoch",
                              x_ticks=list(range(self.model_hps["epochs"])),
                              x_tick_labels=list(range(1, self.model_hps["epochs"] + 1)),
                              legend=["train", "test"], legend_loc="upper right",
                              fname=os.path.join(timestamp, "model_rmse.png"))
        print(f"Results saved in {timestamp}/.")

    def run(self):
        """
        Run the Collaborative Filtering pipeline.

        :param: None
        :return: None
        """
        df = self.read_data()
        data_splits = self.split_data(df=df)
        model = self.train_model(data_splits=data_splits)
        self.save_results(model=model)


class CFModel(Model):
    def __init__(self, num_users, num_items, embedding_size, **kwargs):
        super(CFModel, self).__init__(**kwargs)
        self.num_users = num_users
        self.num_movies = num_items
        self.embedding_size = embedding_size
        self.user_embedding = Embedding(input_dim=num_users, output_dim=embedding_size,
                                        embeddings_initializer="he_normal",
                                        embeddings_regularizer=l2(1e-6))
        self.user_bias = Embedding(input_dim=num_users, output_dim=1)
        self.item_embedding = Embedding(input_dim=num_items, output_dim=embedding_size,
                                        embeddings_initializer="he_normal",
                                        embeddings_regularizer=l2(1e-6))
        self.item_bias = Embedding(input_dim=num_items, output_dim=1)

    def call(self, inputs: np.ndarray) -> tf.Tensor:
        """
        Execute model's forward propagation.

        :param inputs: User and item indexes
        :return: Predicted rating
        """
        user_idx = inputs[:, 0]
        item_idx = inputs[:, 1]
        user_vector = self.user_embedding(user_idx)
        user_bias = self.user_bias(user_idx)
        item_vector = self.item_embedding(item_idx)
        item_bias = self.item_bias(item_idx)
        user_item_dot_prod = Dot(axes=1)([user_vector, item_vector])
        output = user_item_dot_prod + user_bias + item_bias
        output = Activation(activation="sigmoid")(output)
        return output


if __name__ == "__main__":
    data_location = "data/2019_sports_survey_output_processed_cmd_output.csv"
    user_col_name = "sys_RespNum"
    min_rating = 0.0
    max_rating = 5.0
    test_pct = 0.1
    num_factors = 10
    model_hps = {"lr": 0.001, "epochs": 5, "batch_size": 128}
    min_row_density_threshold = 0.15
    collaborative_filtering = CollaborativeFiltering(data_location=data_location,
                                                     user_col_name=user_col_name,
                                                     min_rating=min_rating, max_rating=max_rating,
                                                     test_pct=test_pct, num_factors=num_factors,
                                                     model_hps=model_hps,
                                                     min_row_density_threshold=min_row_density_threshold)
    collaborative_filtering.run()
