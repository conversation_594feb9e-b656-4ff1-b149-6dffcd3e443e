# Customizable Dashboard Documentation Index

This document serves as an index for all documentation related to the customizable dashboard feature in the Customer Churn POC application.

## Overview

The customizable dashboard feature allows users to create personalized dashboards with various visualizations. Users can add, remove, resize, and configure visualizations to suit their needs. Dashboards can also be shared with other users with different permission levels.

## Documentation

### Architecture and Design

- [ADR 029: Customizable Dashboard Architecture](./adr/029-customizable-dashboard-architecture.md)
  - Architectural decision record describing the design choices and trade-offs for the customizable dashboard feature.

- [Technical Specification](./technical-specs/customizable-dashboard-technical-spec.md)
  - Detailed technical specification for the customizable dashboard feature, including data models, API endpoints, and frontend components.

### Implementation

- [Dashboard Service Refactoring](./refactoring/dashboard-service-refactoring.md)
  - Documentation of the refactoring of the dashboard service to support customizable dashboards.

- [Implementation Plan](./implementation-plans/customizable-dashboard-implementation-plan.md)
  - Phased implementation plan for the customizable dashboard feature.

### User Documentation

- [User Guide](./user-guides/customizable-dashboard-user-guide.md)
  - Comprehensive guide for end users on how to create, configure, and share dashboards.

### Assets

- [Dashboard Interface Mockups](./assets/dashboard-interface.txt)
  - Text-based mockups of the dashboard interface.

## Code References

### Backend

- **Models**
  - [Dashboard Models](../backend/app/models/dashboard.py)
  - [Dashboard Schemas](../backend/app/schemas/dashboard.py)

- **Services**
  - [Dashboard Service](../backend/app/services/dashboard_service.py)
  - [Dashboard Module](../backend/app/services/dashboard_service/dashboard.py)
  - [Visualization Module](../backend/app/services/dashboard_service/visualization.py)
  - [Visualization Type Module](../backend/app/services/dashboard_service/visualization_type.py)
  - [Permission Module](../backend/app/services/dashboard_service/permission.py)
  - [Query Module](../backend/app/services/dashboard_service/query.py)

- **API Endpoints**
  - [Dashboard Endpoints](../backend/app/api/endpoints/dashboard.py)

- **Database Migrations**
  - [Dashboard Tables Migration](../backend/app/db/migrations/add_dashboard_tables.py)

### Frontend

- **Pages**
  - [Dashboard List Page](../frontend/app/(dashboard)/dashboard/page.tsx)
  - [Dashboard Detail Page](../frontend/app/(dashboard)/dashboard/[id]/page.tsx)

- **API Client**
  - [Dashboard API Client](../frontend/app/lib/api/dashboard.ts)

## Testing

- [Dashboard Service Tests](../backend/test_dashboard_service.py)
- [Dashboard Service Refactoring Verification](../backend/verify_dashboard_service_refactoring.py)

## Future Work

- Advanced visualization types (heat maps, scatter plots, geospatial visualizations)
- Dashboard templates
- Data alerts
- Export and sharing features
- Natural language query interface

## Contact

For questions or feedback about the customizable dashboard feature, please contact the development <NAME_EMAIL>.
