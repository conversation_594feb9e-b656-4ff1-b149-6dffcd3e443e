#!/usr/bin/env python3
"""
Test script for the prepaid data generators.
"""

import sys
import os
sys.path.append('backend/app/utils/data_generators_prepaid/raw_data_generators')

from customer_generator import CustomerGenerator
from billing_generator import BillingGenerator
import j<PERSON>

def test_prepaid_generators():
    """Test the prepaid customer and billing generators."""
    
    print("Testing Prepaid Data Generators")
    print("=" * 40)
    
    # Initialize generators
    customer_gen = CustomerGenerator(random_seed=42)
    billing_gen = BillingGenerator(random_seed=42)
    
    # Generate a test customer
    print("\n1. Generating test customer...")
    customer = customer_gen.generate_customer()
    
    # Display customer info
    print(f"Customer ID: {customer['customer_id']}")
    print(f"Age: {customer['age']}, Income: ${customer['income']:,}")
    print(f"Device Type: {customer['device_type']}")
    print(f"Top-up Frequency: {customer['topup_frequency']}")
    print(f"Average Top-up Amount: ${customer['topup_amount_avg']}")
    print(f"Total Top-up (3m): ${customer['total_topup_3m']}")
    print(f"Data Usage (30d): {customer['data_usage_last_30d']} GB")
    print(f"Call Minutes (30d): {customer['call_minutes_last_30d']} min")
    print(f"Last Top-up Channel: {customer['last_topup_channel']}")
    print(f"Days Since Last Activity: {customer['days_since_last_activity']}")
    
    # Create a mock plan for testing billing
    print("\n2. Creating mock plan...")
    mock_plan = {
        'plan_id': 'PLAN-TEST123',
        'start_date': '2024-01-01',
        'base_price': 50.0,
        'add_ons': []
    }
    
    # Generate billing transactions
    print("\n3. Generating prepaid transactions...")
    transactions = billing_gen.generate_customer_billing(
        customer['customer_id'], 
        [mock_plan]
    )
    
    print(f"Generated {len(transactions)} transactions")
    
    # Display first few transactions
    print("\n4. Sample transactions:")
    for i, txn in enumerate(transactions[:3]):
        print(f"\nTransaction {i+1}:")
        print(f"  Transaction ID: {txn['transaction_id']}")
        print(f"  Package ID: {txn['package_id']}")
        print(f"  Date: {txn['transaction_date']}")
        print(f"  Top-up Amount: ${txn['topup_amount']}")
        print(f"  Total Amount: ${txn['total_amount']}")
        print(f"  Payment Channel: {txn['payment_channel']}")
        if 'sim_fee' in txn:
            print(f"  SIM Fee: ${txn['sim_fee']}")
            print(f"  Activation Type: {txn['activation_type']}")
    
    print("\n" + "=" * 40)
    print("Test completed successfully!")
    
    return customer, transactions

if __name__ == "__main__":
    test_prepaid_generators()
