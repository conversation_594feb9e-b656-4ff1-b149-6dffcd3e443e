#%%
from latent_factors_clustering import LatentFactorsClustering
#%%
data_location = "/mnt/c/Users/<USER>/Downloads/27102025093822.xlsx"
user_col_name = "sys_Respondent"
entity = "user"
max_k_elbow_plot = 20
num_iterations = 100
num_diff_inits = 20
num_clusters = 6
high_percentile = 70
low_percentile = 25
latent_factors_clustering = LatentFactorsClustering(data_location=data_location,
                                                    user_col_name=user_col_name, entity=entity,
                                                    max_k_elbow_plot=max_k_elbow_plot,
                                                    num_iterations=num_iterations,
                                                    num_diff_inits=num_diff_inits,
                                                    num_clusters=num_clusters,
                                                    high_percentile = high_percentile,
                                                    low_percentile=low_percentile)
latent_factors_clustering.run()
#%%
