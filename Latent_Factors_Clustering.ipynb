#%%
from latent_factors_clustering import LatentFactorsClustering
#%%
data_location = "/mnt/c/Users/<USER>/Downloads/segmentation_2910/28102025223049.xlsx"
user_col_name = "sys_Respondent"
entity = "user"
max_k_elbow_plot = 20
num_iterations = 100
num_diff_inits = 40
num_clusters = 4
high_percentile = 80
low_percentile = 20
latent_factors_clustering = LatentFactorsClustering(data_location=data_location,
                                                    user_col_name=user_col_name, entity=entity,
                                                    max_k_elbow_plot=max_k_elbow_plot,
                                                    num_iterations=num_iterations,
                                                    num_diff_inits=num_diff_inits,
                                                    num_clusters=num_clusters,
                                                    high_percentile = high_percentile,
                                                    low_percentile=low_percentile)
latent_factors_clustering.run()
#%%
