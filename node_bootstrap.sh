#!/bin/bash
echo "---------------------------------------------------"
echo Starting Time - $(date '+%Y %b %d %H:%M:%S')
source /usr/lib/qubole/bootstrap-functions/misc/util.sh
source /usr/lib/hustler/bin/qubole-bash-lib.sh
is_master=`nodeinfo is_master`
nvidia_driver_install() {
  cd /media/ephemeral0
  curl -O https://storage.googleapis.com/nvidia-drivers-us-public/GRID/GRID8.0/NVIDIA-Linux-x86_64-418.70-grid.run
  chmod +x ./NVIDIA-Linux-x86_64-418.70-grid.run
  if [[ "$is_master" == "1" ]]; then
    TERM=dumb ./NVIDIA-Linux-x86_64-418.70-grid.run -s --install-libglvnd > /tmp/nvidia.log 2>&1 &
  else
    TERM=dumb ./NVIDIA-Linux-x86_64-418.70-grid.run -s --install-libglvnd > /tmp/nvidia.log 2>&1
  fi   
}
function pre_service_start() {
  echo "pre service install"
  if [[ "$is_master" == "1" ]]; then
    nvidia_driver_install
  fi
}
function pre_task_start() {
  echo "pre task install"
  nvidia_driver_install
}
function post_start() {
  echo "post service install"
}

# Bootstrap for installing custom Python packages on top of Qubole environments
install_py_packages() {
  echo "*** Starting Python package installs ***"
  # pip install torch==1.5.0+cu101 torchvision==0.6.0+cu101 -f https://download.pytorch.org/whl/torch_stable.html
  # pip install --upgrade --user awscli
  echo "*** Python package installs finished ***"
}
# Bootstrap for installing custom R packages on top of Qubole environments
install_r_packages() {
  echo "*** Starting R package installs ***"
  echo "*** R Package installs finished ***"
}
# Enable environment to install custom packages
node_ip=$(nodeinfo node_ip)
use_spark=$(nodeinfo use_spark)
env_id=$(nodeinfo quboled_env_id)
new_package_management_enabled=$(nodeinfo_feature quboled.new_package_management)
package_management_enabled=$(nodeinfo tapp_ui_enable_package_management)
if [[ ${new_package_management_enabled} = "true" 
    && ${use_spark} = "1" && z != "${env_id}" ]]; then
  python /usr/lib/quboled/hustler/status_check.py --hostname ${node_ip}
  if [ $? -eq 0 ]; then
    # Python package installs
    python_version=$(nodeinfo quboled_env_python_version)
    baseenvprefix=$(ls -d /usr/lib/environs/a-*| grep -F py-$python_version)
    envprefix=$(ls -d /usr/lib/environs/e-a-*| grep -F py-$python_version)
    source $baseenvprefix/bin/activate $envprefix
  echo "========================================================="
    install_py_packages
  echo "========================================================="
    # R package installs   
  r_version=$(nodeinfo quboled_env_r_version)
  baseenvprefix=$(ls -d /usr/lib/environs/a-*| grep -F r-$r_version)
  envprefix=$(ls -d /usr/lib/environs/e-a*| grep -F r-$r_version)
  source $baseenvprefix/bin/activate $envprefix
  echo "========================================================="
  install_r_packages
  echo "========================================================="
  echo "========================================================="
  nvidia_driver_install
  echo "========================================================="
  else
    echo "*** Error - Issue with node status, skipping package installation ***"
    # Very unlikely scenario, can take evasive action ... 
  fi
elif [[ ${package_management_enabled} = "true" &&
  ${use_spark} = "1" && z != "${env_id}" ]]; then
  python /usr/lib/quboled/hustler/status_check.py --hostname ${node_ip}
  if [ $? -eq 0 ]; then
    # Python package installs
  envprefix=$(ls -d /usr/lib/envs/*| grep env-$env_id-ver- | grep py)
  source $envprefix/bin/activate $envprefix
  echo "========================================================="
  install_py_packages
  echo "========================================================="
  # R package installs   
  envprefix=$(ls -d /usr/lib/envs/*| grep env-$env_id-ver- | grep "\-r-3")
  source $envprefix/bin/activate $envprefix
  echo "========================================================="
  install_r_packages
  echo "========================================================="
  echo "========================================================="
  nvidia_driver_install
  echo "========================================================="
  else
    echo "*** Error - Issue with node status, skipping package installation ***"
    # Very unlikely scenario, can take evasive action ... 
  fi
else
  echo "*** Warning - Environment is not attached to the cluster *** Contact support team"
fi

source /usr/lib/hustler/bin/qubole-bash-lib.sh
is_master=`nodeinfo is_master`
if [[ "$is_master" != "1" ]]; then
  echo "####################################################"
  echo "This is a worker"
  nvidia_driver_install
  echo "####################################################"
fi
echo Ending Time - $(date '+%Y %b %d %H:%M:%S')
echo "---------------------------------------------------"