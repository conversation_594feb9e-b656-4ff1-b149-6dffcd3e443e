#%% md
### **Setup**
#%%
import pandas as pd
import numpy as np
#%%
%reload_ext autoreload
%autoreload 2
%matplotlib inline
#%%
import sklearn 
from fastai import *
from fastai.collab import *
from fastai.tabular import *
import torch
#%%
torch.cuda.set_device(0)
torch.cuda.is_available()
#%%
torch.backends.cudnn.enabled
#%% md
### **Data Import**
#%%
from google.colab import files
uploaded = files.upload()
#%%
import io
ratings = pd.read_csv(io.BytesIO(uploaded['vz_data_cf_18.csv']))
#%%
# Set up dataset
colnames = np.arange(1,62).astype(str).tolist()
colnames = ['feature_' + s for s in colnames]
colnames = ['user_id'] + colnames
ratings.columns = colnames
#%%
all_users = array(ratings['user_id'])
#%%
ratings = pd.melt(ratings, id_vars=['user_id'],var_name='content_id', value_name='ratings')
ratings.fillna(0,inplace=True)
#%%
data = CollabDataBunch.from_df(ratings,seed=42,valid_pct=0.0)
#%%
y_range = [0.,5.]
wd=1e-3
n_factors = 5
#%%
learn = collab_learner(data, n_factors=n_factors, y_range=y_range, wd=wd)
#%%
learn.fit_one_cycle(3, 5e-3)
#%%
learn.lr_find()
learn.recorder.plot(skip_end=15)
#%%
learn.model
#%%
learn.save('dotprod')
#%% md
### Prediction
#%%
learn.load('dotprod')
#%%
weights= learn.model.u_weight.weight.data.cpu().numpy()[1:]
biases = learn.model.u_bias.weight.data.cpu().numpy()[1:]
#%%
np.savetxt('weights.csv', weights, delimiter = ',')
files.download('weights.csv')
np.savetxt('biases.csv', biases, delimiter = ',')
files.download('biases.csv')