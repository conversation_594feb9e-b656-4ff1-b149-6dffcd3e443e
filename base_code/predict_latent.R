setwd("~/Desktop/AVCo_BD/CF/")
library(readxl)
library(extracat)
library(tidyverse)
library(lubridate)
library(caret)
library(factoextra)
library(cluster)
library(ggbiplot)
library(ranger)

# Read in CF output

wt <- read.csv('Results/weights.csv', header = FALSE)
bias <- read.csv('Results/biases.csv', header = FALSE)

cf_output <- data.frame(cbind(bias, wt))
names(cf_output) <- paste0('dim_', seq(0,5,1))

# Visualize each CF dimension

ggplot(cf_output, aes(x=dim_0)) + geom_density() + theme_classic() + ylim(0,3) + xlim(-4,3)
ggplot(cf_output, aes(x=dim_1)) + geom_density() + theme_classic() + ylim(0,3) + xlim(-4,3)
ggplot(cf_output, aes(x=dim_2)) + geom_density() + theme_classic() + ylim(0,3) + xlim(-4,3)
ggplot(cf_output, aes(x=dim_3)) + geom_density() + theme_classic() + ylim(0,3) + xlim(-4,3)
ggplot(cf_output, aes(x=dim_4)) + geom_density() + theme_classic() + ylim(0,3) + xlim(-4,3)
ggplot(cf_output, aes(x=dim_5)) + geom_density() + theme_classic() + ylim(0,3) + xlim(-4,3)

features_18 <- read.csv('../Dynamic_Clustering/Data/vz_data_cf_18.csv')[,-1]
features_19 <- read.csv('../Dynamic_Clustering/Data/vz_data_cf_19.csv')[,-1]

test_size <- 0.15
test_idx  <- sample(nrow(cf_output), test_size*nrow(cf_output))
train_idx <- setdiff(c(1:nrow(cf_output)), test_idx)
  
# Develop model on 2018 data. Use cross-validation and plot predictions to sanity check results

overall <- cbind(cf_output, features_18)
train <- overall[train_idx,]
test <- overall[test_idx,]

overall_0 <- cbind(cf_output$dim_0, features_18)
names(overall_0)[1] <- 'y'

train_0 <- overall_0[train_idx,]
test_0  <- overall_0[test_idx,]

tgrid <- expand.grid(.mtry = 2:4,
                     .splitrule = 'variance',
                     .min.node.size = c(10, 20, 50))

caret_0 <- train(y  ~ ., data = train_0,
                  method = "ranger",
                  trControl = trainControl(method="cv", number = 5, verboseIter = T),
                  tuneGrid = tgrid,
                  importance = "impurity")

ranger_0 <- ranger(dim_0 ~., train[,c(1,7:ncol(train))], verbose = TRUE, importance='impurity',
                  mtry=4, splitrule='variance', min.node.size = 10)
ranger_1 <- ranger(dim_1 ~., train[,c(2,7:ncol(train))], verbose = TRUE, importance='impurity',
                   mtry=4, splitrule='variance', min.node.size = 10)
ranger_2 <- ranger(dim_2 ~., train[,c(3,7:ncol(train))], verbose = TRUE, importance='impurity',
                   mtry=4, splitrule='variance', min.node.size = 10)
# ranger_3 <- ranger(dim_3 ~., train[,c(4,7:ncol(train))], verbose = TRUE, importance='impurity', 
#                    mtry=4, splitrule='variance', min.node.size = 10)
# ranger_4 <- ranger(dim_4 ~., train[,c(5,7:ncol(train))], verbose = TRUE, importance='impurity', 
#                    mtry=4, splitrule='variance', min.node.size = 10)
# ranger_5 <- ranger(dim_5 ~., train[,c(6,7:ncol(train))], verbose = TRUE, importance='impurity', 
#                    mtry=4, splitrule='variance', min.node.size = 10)

pred_0 <- predict(ranger_0, test[,c(1,7:ncol(train))])$predictions
plot(density(pred_0), xlim=c(-3.5,-2.5), ylim=c(0,5))
plot(density(test$dim_0), xlim=c(-3.5,-2.5), ylim=c(0,5))
 
pred_1 <- predict(ranger_1, test[,c(2,7:ncol(train))])$predictions
plot(density(pred_1), xlim=c(-2.5,0.5))
plot(density(test$dim_1), xlim=c(-2.5,0.5))

pred_2 <- predict(ranger_2, test[,c(3,7:ncol(train))])$predictions
plot(density(pred_2), xlim=c(0,2.5))
plot(density(test$dim_2), xlim=c(0,2.5))

cor <- cor(overall)

# Predict 2019 latent variables

ranger_0 <- ranger(dim_0 ~., overall[,c(1,7:ncol(overall))], verbose = TRUE, importance='impurity', 
                   mtry=4, splitrule='variance', min.node.size = 10)
ranger_1 <- ranger(dim_1 ~., overall[,c(2,7:ncol(overall))], verbose = TRUE, importance='impurity', 
                   mtry=4, splitrule='variance', min.node.size = 10)
ranger_2 <- ranger(dim_2 ~., overall[,c(3,7:ncol(overall))], verbose = TRUE, importance='impurity', 
                   mtry=4, splitrule='variance', min.node.size = 10)
ranger_3 <- ranger(dim_3 ~., overall[,c(4,7:ncol(overall))], verbose = TRUE, importance='impurity', 
                   mtry=4, splitrule='variance', min.node.size = 10)
ranger_4 <- ranger(dim_4 ~., overall[,c(5,7:ncol(overall))], verbose = TRUE, importance='impurity', 
                   mtry=4, splitrule='variance', min.node.size = 10)
ranger_5 <- ranger(dim_5 ~., overall[,c(6,7:ncol(overall))], verbose = TRUE, importance='impurity', 
                   mtry=4, splitrule='variance', min.node.size = 10)

# Predict all 

pred_19_0 <- predict(ranger_0, features_19)$predictions
pred_19_1 <- predict(ranger_1, features_19)$predictions
pred_19_2 <- predict(ranger_2, features_19)$predictions
pred_19_3 <- predict(ranger_3, features_19)$predictions
pred_19_4 <- predict(ranger_4, features_19)$predictions
pred_19_5 <- predict(ranger_5, features_19)$predictions

pred_19 <- data.frame(cbind(pred_19_0, pred_19_1,pred_19_2,
                 pred_19_3, pred_19_4, pred_19_5))
names(pred_19) <- names(cf_output)

latent_all <- rbind(cf_output, pred_19)

write.csv(pred_19,'Results/latent_predicted_19.csv')
write.csv(latent_all,'Results/latent_predicted_all.csv')

