#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from scipy.optimize import minimize
from scipy.optimize import Bounds
#%%
# Read in VZ Data
vz_latent  = pd.read_csv('../Results/latent_predicted_all.csv', sep=',',header=0)
vz_latent = vz_latent.values[:,2:]
#%%
# Read in original clusters (determined using clustering.ipynb)
mu_old = np.loadtxt('../Results/mu_cf_best_18.csv', delimiter=',')
c_old  = np.loadtxt('../Results/c_cf_best_18.csv', delimiter=',')
#%%
# Set K
K=np.shape(mu_old)[0]
#%%
# Dynamic k-means
def error(mu, k, c, data):
    mu = np.reshape(mu, (k,int(np.size(mu)/k)))
    error = 0
    for j in range(k):
        # Calculate the error as the summation of the distances between each point and the 
        # centroid that it was assigned to.
        error += np.sum(np.sum((data - mu[j])**2,1)*(c==j))
    return error

def error_g(mu, k, c, data):
    mu = np.reshape(mu, (k,int(np.size(mu)/k)))
    grad = np.zeros(np.shape(mu))
    for j in range(k):
        # Calculate the gradient for each centroid as the summation (by latent factor) of the 
        # derivative of the distances between each point and the centroid that it was assigned to.
        grad[j] = -2*np.sum((data - mu[j]) * np.reshape((c==j), (np.size(c),1)),0)
    grad = np.reshape(grad, (np.size(mu)))
    return grad

def constrained_kmeans(data, centroids_init, margin, k):
    nDim = np.size(data, axis =1)
    nRow = np.size(data, axis =0)

    mu = centroids_init
    
    iterations = 21
    L = np.zeros(iterations)
    
    lb = np.reshape(centroids_init - margin, np.size(centroids_init)) 
    ub = np.reshape(centroids_init + margin, np.size(centroids_init))
    bounds = Bounds(lb, ub)
    
    for it in range(iterations):
        dist = np.zeros((nRow, k))
        for j in range(k):
            dist[:,j] = np.sum((data - mu[j])**2, axis=1)
            
        c = np.argmin(dist, axis = 1)
        # Calculate the loss at each epoch as the summation of the distances between each point 
        # and the centroid that it was assigned to.
        L[it] = np.sum(np.amin(dist, axis=1))
        
        # jac: Jacobian matrix.
        # options={'disp': True}: print convergence messages.
        # The optimization is trying to minimize the summation of the distances between each 
        # point and the centroid that it was assigned to (total intra-cluster distance).
        mu = np.reshape(minimize(error, np.reshape(mu, np.size(mu)),
                                 jac=error_g,
                                 options={'disp': True}, 
                                      tol= 1e-8, 
                                      bounds=bounds,
                                      args = (k, c, data)).x, np.shape(mu))
    return L, mu
#%%
# Set boundaries
# stdev is extracted per latent factor.
margin = np.std(vz_latent,axis=0)/2.
margin = np.repeat([margin], K, axis=0)
#%%
# Determine new clusters (Note: these are deterministic, because we always start from the original clusters)
L_new, mu_new = constrained_kmeans(vz_latent, mu_old, margin, K)
#%%
# Obtain new cluster assignments
nRow_latent = np.size(vz_latent, axis =0)
dist_latent = np.zeros((nRow_latent, K))
for j in range(K):
    dist_latent[:,j] = np.sum((vz_latent - mu_new[j])**2, axis=1)
    
c_new = np.argmin(dist_latent, axis = 1)
np.unique(c_new, return_counts=True)
#%%
# Save results
np.savetxt('../Results/mu_cf_dynamic.csv', mu_new, delimiter=',')
np.savetxt('../Results/c_cf_dynamic.csv', c_new, delimiter=',')