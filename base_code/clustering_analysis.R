setwd("~/Desktop/AVCo_BD/CF/")
library(readxl)
library(extracat)
library(tidyverse)
library(lubridate)
library(caret)
library(factoextra)
library(cluster)
library(ggbiplot)

## Raw Data Input

data <- read_xlsx("../Dynamic_Clustering/Data/project_maguire_akshay_tables.xlsx", na = c('?'))

data <- data %>% select(hashed_cust_id, ga_cohort, ga_byod_Dev_ctgry,
                        ga_trans_channel, ga_device_group, ga_ispu_sdd_ctgry,
                        ga_rev_gaPlus3_mth, ga_activity_dt, ga_tmp_taker,
                        ADULT_AGE_RANGE_INP	,age_bkt, ethnicity,DENSITY_CLASS, 
                        ga_curr_contract_term, ga_net_ga_cnt)

data_raw <- data

# NA in ga_ispu_sdd_ctgry (in store pick up or same day delivery) really should be zero

visna(data)
data$ga_ispu_sdd_ctgry[is.na(data$ga_ispu_sdd_ctgry)] <- 'Not SDD/ISPU'
data <- data %>% select(-ADULT_AGE_RANGE_INP)
visna(data)

data <- na.omit(data) %>% data.frame()

# Data preparation

data$ga_cohort[data$ga_cohort %in% c('GA_AAL', 'UPG_AAL')] <- 'AAL'

data$ga_device_group[data$ga_device_group %in% c('Apple', 'Android', 'Other Smart')] <- 'Smart Phone'
data$ga_device_group[data$ga_device_group %in% c('Basic', 'Blackberry', 'Palm')] <- 'Other Phone'
data$ga_device_group[!data$ga_device_group %in% c('Smart Phone', 'Other Phone', 'Tablet', 'Wearable Devices')] <- 'Other'

# Combine care and other
data$ga_trans_channel[data$ga_trans_channel=='Stores'] <- 'VZRetail'
data$ga_trans_channel[data$ga_trans_channel=='Loc Retail'] <- 'Local_Retailer'
data$ga_trans_channel[data$ga_trans_channel=='Internet'] <- 'Online'
data$ga_trans_channel[data$ga_trans_channel=='TMK'] <- 'TMK'
data$ga_trans_channel[data$ga_trans_channel=='Nat Retail'] <- 'National_Retailer'
data$ga_trans_channel[data$ga_trans_channel=='Loc Agent'] <- 'Local_Agent'
data$ga_trans_channel[data$ga_trans_channel=='NonComm'] <- 'Care'
data$ga_trans_channel[data$ga_trans_channel %in% c('Other', 'Outside', 'Care')] <- 'Other'

# BYOD or not BYOD
data$Smart_Phone_BYOD <- 0
data$Smart_Phone_BYOD[data$ga_device_group == 'Smart Phone' & 
                        data$ga_byod_Dev_ctgry!="NON-BYOD" & 
                        !data$ga_curr_contract_term %in% c('Edge', '2 Yr Contract', '1 Yr Contract')] <- data$ga_net_ga_cnt[data$ga_device_group == 'Smart Phone' & 
                                                                                                                              data$ga_byod_Dev_ctgry!="NON-BYOD" & 
                                                                                                                              !data$ga_curr_contract_term %in% c('Edge', '2 Yr Contract', '1 Yr Contract')]
data$Smart_Phone_BYOD[data$Smart_Phone_BYOD == -1] <- 0

data <- data %>% select(-ga_net_ga_cnt, -ga_curr_contract_term, -ga_byod_Dev_ctgry)
data <- data %>% mutate(tenure = difftime(Sys.Date(), ga_activity_dt, units = 'days'))

data <- data %>% mutate(ga_cohort = factor(ga_cohort), 
                        ga_trans_channel = factor(ga_trans_channel), 
                        ga_device_group = factor(ga_device_group), 
                        age_bkt = factor(age_bkt), 
                        ethnicity = factor(ethnicity), 
                        ga_ispu_sdd_ctgry = factor(ga_ispu_sdd_ctgry),
                        DENSITY_CLASS = factor(DENSITY_CLASS))

data <- data %>% select(hashed_cust_id, ga_activity_dt, tenure,
                        ga_cohort:ga_rev_gaPlus3_mth, ga_tmp_taker:Smart_Phone_BYOD)

data <- data %>% mutate(tenure = as.numeric(tenure))
data <- data  %>% mutate(month = factor(month(ga_activity_dt)))

## Analysis

wt <- read.csv('Results/weights.csv', header = FALSE)
bias <- read.csv('Results/biases.csv', header = FALSE)

assignment <- read.csv('Results/c_cf_best_18.csv', header=FALSE)
assignment_km <- read.csv('../Dynamic_Clustering/Results/c_best_18.csv', header=FALSE)

vz_raw <- read.csv('../Dynamic_Clustering/Data/vz_data_kmeans_unscaled.csv')

vz_18_raw  <- vz_18_raw %>% cbind(assignment)
names(vz_18_raw)[length(names(vz_18_raw))] <- 'c'

mu_18_cf <- vz_18_raw %>% group_by(c) %>% summarise_all(mean)

write.csv(mu_18_cf, 'Results/mu_cf_war_best_18.csv')

# SD of each variable

dmy <- dummyVars(" ~ .", data = data_18[,-c(1:3)])
wide_data_18 <- data.frame(predict(dmy, newdata = data_18[,-c(1:3)]))

means_cf <- wide_data_18 %>% select(-c_km) %>% 
                group_by(c_cf) %>% summarise_all(mean)

means_km <- wide_data_18 %>% select(-c_cf) %>% 
              group_by(c_km) %>% summarise_all(mean)

View(means_cf %>% summarise_all(sd) %>% t() %>% data.frame() %>%
       rownames_to_column() %>% arrange(-.))

View(means_km %>% summarise_all(sd) %>% t() %>% data.frame() %>%
       rownames_to_column() %>% arrange(-.))
