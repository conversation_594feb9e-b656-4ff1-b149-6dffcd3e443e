#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from scipy.optimize import minimize
from scipy.optimize import Bounds
#%%
# Read in VZ Data
vz_18_raw = pd.read_csv('../../Dynamic_Clustering/Data/vz_data_cf_18.csv', sep=',',header=0)
vz_18_cf_weights  = pd.read_csv('../Results/weights.csv', sep=',',header=None)
vz_18_cf_biases  = pd.read_csv('../Results/biases.csv', sep=',',header=None)
#%%
# Prepare CF output for clustering
vz_18_latent = pd.concat([vz_18_cf_biases, vz_18_cf_weights], axis=1)
vz_18_latent = vz_18_latent.values[:,1:]
#%%
# Function for k-means clustering

def kmeans(data, k):
    nDim = np.size(data, axis =1)
    nRow = np.size(data, axis =0)
    # mu dimensions: num_clusters X emb_dimension.
    mu = data[np.random.randint(0, nRow, size=k)]
    iterations = 11
    L = np.zeros(iterations)
    for it in range(iterations):
        dist = np.zeros((nRow, k))
        for j in range(k):
            # Calculate distance for each point to each centroid.
            # dist dimensions: num_points X num_centroids.
            dist[:,j] = np.sum((data - mu[j])**2, axis=1)
        # Determines which centroid each point belongs to.
        # c dimensions: num_points X 1.
        c = np.argmin(dist, axis = 1)
        # Calculate the loss at each epoch as the summation of the distances between each point 
        # and the centroid that it was assigned to.
        L[it] = np.sum(np.amin(dist, axis=1))
        for j in range(k):
            # Update each centroid's coordinates as the average of the points' coordinates that 
            # belong to it.
            mu[j] = (1./np.sum((c==j))) * np.sum(data * (c==j).reshape((nRow,1)),0)
    # Return loss at each epoch and final centroids.
    return L, mu
#%%
# Determine a reasonable value for K

L = np.zeros((11, 20))
for k in range(1,21):
    L[:,k-1],mu = kmeans(vz_18_latent, k)
    print('Done k=', k)
    labelk = 'k=' + str(k)
    plt.plot(np.arange(0,11), L[:,k-1], label = labelk)
    
plt.legend()
plt.xticks(np.arange(0,11,2))
plt.xlabel('Iteration')
plt.ylabel('Objective')

plt.figure()
#%%
# Final loss using all different values of K.
plt.plot(L[10])
#%%
# Run k-means from multiple start points, and take best clusters

K=10
L_18, mu_18 = kmeans(vz_18_latent, K)
runs= 20
L = np.zeros((11, 20))
# mus dimensions: num_runs X (num_clusters x emb_dimension).
mus = np.zeros([runs, K, np.size(vz_18_latent,1)])
for k in range(1,21):
    L[:,k-1],mus[k-1] = kmeans(vz_18_latent, 10)
    print('Done k=', k)
    labelk = 'k=' + str(k)
    plt.plot(np.arange(0,11), L[:,k-1], label = labelk)
    
plt.legend()
plt.xticks(np.arange(0,11,2))
plt.xlabel('Iteration')
plt.ylabel('Objective')

plt.figure()
#%%
# Obtain cluster assignments

mu_18 = mus[np.argmax(L[10,:])]  # Typo, should be np.argmin().
nRow_18 = np.size(vz_18_latent, axis =0)
dist_18 = np.zeros((nRow_18, K))
for j in range(K):
    dist_18[:,j] = np.sum((vz_18_latent - mu_18[j])**2, axis=1)
    
c_18 = np.argmin(dist_18, axis = 1)

np.unique(c_18, return_counts=True)
#%%
# Save results
np.savetxt('../Results/mu_cf_best_18.csv', mu_18, delimiter=',')
np.savetxt('../Results/c__cf_best_18.csv', c_18, delimiter=',')