'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Alert, AlertDescription } from '@/app/components/ui/alert';
import { DocumentArrowUpIcon, PlayIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import SegmentationParameters from '../models/SegmentationParameters';
import SegmentationResults from '../models/SegmentationResults';

interface ShapFileSegmentationProps {
  onAnalysisComplete: (results: any) => void;
}

export default function ShapFileSegmentation({ onAnalysisComplete }: ShapFileSegmentationProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showParameters, setShowParameters] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        setError('Please upload a CSV file');
        return;
      }
      setFile(selectedFile);
      setError(null);
      setShowParameters(true);
    }
  };

  const handleRunAnalysis = async (parameters: any) => {
    if (!file) return;

    try {
      setIsAnalyzing(true);
      setError(null);
      
      // Mock analysis for demo - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockResults = {
        parameters: parameters,
        segments: {
          type: 'tree',
          segments: [
            {
              id: '1',
              name: 'Opportunity Group 1',
              description: 'Young professionals with high data usage showing price sensitivity',
              size: 3200,
              percentage: 28.5,
              criteria: {
                rules: ['Age < 35', 'Data Usage > 50GB/month', 'Contract Length < 12 months'],
                characteristics: [
                  { feature: 'Monthly Charges', value: '>$85' },
                  { feature: 'Tech Support Calls', value: '>3/month' }
                ]
              }
            },
            {
              id: '2', 
              name: 'Opportunity Group 2',
              description: 'Family accounts with multiple lines and long tenure',
              size: 2100,
              percentage: 18.7,
              criteria: {
                rules: ['Number of Lines >= 3', 'Tenure > 24 months', 'Payment Method = Auto-pay'],
                characteristics: [
                  { feature: 'Monthly Charges', value: '$120-180' },
                  { feature: 'Contract Type', value: 'Two Year' }
                ]
              }
            },
            {
              id: '3',
              name: 'Opportunity Group 3',
              description: 'Older customers on basic plans with minimal data usage',
              size: 1800,
              percentage: 16.0,
              criteria: {
                rules: ['Age > 65', 'Data Usage < 5GB/month', 'Plan Type = Basic'],
                characteristics: [
                  { feature: 'Monthly Charges', value: '<$40' },
                  { feature: 'Services', value: 'Phone Only' }
                ]
              }
            }
          ]
        },
        summary: {
          total_segments: 3,
          total_population: 11223,
          coverage: 0.635
        }
      };
      
      setAnalysisResults(mockResults);
      setShowParameters(false);
      onAnalysisComplete(mockResults);
    } catch (err) {
      console.error('Segmentation analysis failed:', err);
      setError('Failed to analyze SHAP values. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Demo Mode Banner */}
      <Alert className="bg-yellow-50 border-yellow-200">
        <AlertDescription className="text-yellow-800">
          <strong>Demo Mode:</strong> This feature demonstrates SHAP file upload and segmentation analysis. Results are simulated for demonstration purposes.
        </AlertDescription>
      </Alert>
      
      {/* File Upload Section */}
      {!showParameters && !analysisResults && (
        <Card>
          <CardHeader>
            <CardTitle>Upload SHAP Values</CardTitle>
            <CardDescription>
              Upload a CSV file containing SHAP values to automatically identify customer segments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center relative">
                <DocumentArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">
                  {file ? file.name : 'Click to upload or drag and drop'}
                </p>
                <p className="text-xs text-gray-500">CSV files only</p>
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
              
              {error && (
                <Alert className="bg-red-50 border-red-200">
                  <AlertDescription className="text-red-700">{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Parameters Section */}
      {showParameters && !analysisResults && (
        <Card>
          <CardHeader>
            <CardTitle>Configure Segmentation Analysis</CardTitle>
            <CardDescription>
              Set parameters for analyzing the uploaded SHAP values
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SegmentationParameters
              onSubmit={handleRunAnalysis}
              isLoading={isAnalyzing}
              onCancel={() => {
                setShowParameters(false);
                setFile(null);
              }}
            />
          </CardContent>
        </Card>
      )}

      {/* Results Section */}
      {analysisResults && (
        <div className="space-y-4">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircleIcon className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Successfully analyzed SHAP values and identified {analysisResults.summary.total_segments} customer segments
            </AlertDescription>
          </Alert>
          
          <SegmentationResults
            results={analysisResults}
            isReadOnly={true} // This hides the save button
          />
          
          <div className="flex justify-end gap-3">
            <button
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              onClick={() => {
                setAnalysisResults(null);
                setFile(null);
                setShowParameters(false);
                onAnalysisComplete(null);
              }}
            >
              Start Over
            </button>
          </div>
        </div>
      )}
    </div>
  );
}