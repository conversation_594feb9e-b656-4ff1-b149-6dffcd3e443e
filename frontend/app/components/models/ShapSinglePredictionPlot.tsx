'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Layout } from 'plotly.js';
import { getShapExplanation, getAvailableInstances } from '../../lib/api/shap';
import { formatFeatureValue, getFeatureDisplaySettings } from '../../lib/visualization-constants';
import Tooltip from '../ui/tooltip';

// Dynamically import Plot to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

interface ShapSinglePredictionPlotProps {
  modelId: number;
  versionId: number;
}

interface SingleShapDatum {
  feature: string;
  value: number;
  feature_value?: any;
}

export default function ShapSinglePredictionPlot({ modelId, versionId }: ShapSinglePredictionPlotProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customerId, setCustomerId] = useState<string>('');
  const [baseValue, setBaseValue] = useState<number>(0);
  const [finalPrediction, setFinalPrediction] = useState<number>(0);
  const [contributions, setContributions] = useState<SingleShapDatum[]>([]);
  const [availableCustomers, setAvailableCustomers] = useState<string[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<string[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    async function fetchAvailableCustomers() {
      try {
        setLoading(true);
        const data = await getAvailableInstances(modelId, versionId);
        if (data.instances && data.instances.length > 0) {
          setAvailableCustomers(data.instances);
          setFilteredCustomers(data.instances.slice(0, 10));
          setCustomerId(data.instances[0]);
        } else {
          setError('No customers available for this model version');
        }
      } catch (err) {
        console.error('Error fetching available customers:', err);
        setError('Failed to load available customers');
      } finally {
        setLoading(false);
      }
    }
    if (modelId && versionId) fetchAvailableCustomers();
  }, [modelId, versionId]);

  useEffect(() => {
    async function fetchExplanation() {
      if (!customerId) return;
      try {
        setLoading(true);
        const data = await getShapExplanation(modelId, versionId, customerId);
        setBaseValue(data.base_value);
        setFinalPrediction(data.final_prediction);
        // Filter out binary variables with value 0
        const filteredContributions = data.contributions.filter(contrib => {
          // Check if it's a binary feature with value 0
          const isBinaryWithZero =
            typeof contrib.feature_value === 'number' &&
            (contrib.feature_value === 0 || contrib.feature_value === false);

          // Keep the contribution if it's not a binary feature with value 0
          return !isBinaryWithZero;
        });
        setContributions(filteredContributions);
        setError(null);
      } catch (err) {
        console.error('Error fetching explanation:', err);
        setError('Failed to load explanation for this customer');
        setContributions([]);
      } finally {
        setLoading(false);
      }
    }
    if (customerId) fetchExplanation();
  }, [modelId, versionId, customerId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCustomerId(value);
    if (value) {
      const filtered = availableCustomers.filter(c => c.toLowerCase().includes(value.toLowerCase())).slice(0, 10);
      setFilteredCustomers(filtered);
      setIsDropdownOpen(true);
    } else {
      setFilteredCustomers(availableCustomers.slice(0, 10));
      setIsDropdownOpen(false);
    }
  };

  const handleSelectCustomer = (customer: string) => {
    setCustomerId(customer);
    setIsDropdownOpen(false);
  };

  const getWaterfallPlotData = (): Plotly.Data[] => {
  if (!contributions || contributions.length === 0) return [];
  let cumulative = baseValue;
  const features = contributions.map(f => ({
    label: `${f.feature} (${f.feature_value ?? 'N/A'})`,
    value: f.value,
    cumulative: cumulative += f.value
  }));
  return [{
    type: 'waterfall' as const,
    orientation: 'v' as const,
    x: ['Base', ...features.map(f => f.label), 'Final'],
    y: [baseValue, ...features.map(f => f.value), finalPrediction - cumulative],
    measure: ['absolute' as const, ...features.map(_ => 'relative' as const), 'total' as const],
    connector: { line: { color: "gray" } },
    increasing: { marker: { color: "#EF4444CC" } },
    decreasing: { marker: { color: '#3B82F6F2' } },
    totals: { marker: { color: "#D1D5DB" } }
  } as Plotly.Data];
};
  const layout: Partial<Layout> = {
    // wrap your title in an object:
    showlegend:   false,
    margin:       { l: 80, r: 30, t: 40, b: 130 },
    xaxis:        { tickangle: -35 },
    yaxis: {
      // wrap axis title in an object, too:
      title:     { text: 'SHAP Value' },
      tickfont:  {
        size:   11,
        family: 'Inter, ui-sans-serif, system-ui',
        color:  '#111827'
      }
    },
    // confirm the names the TS defs expect here!
    paper_bgcolor: 'transparent',
    plot_bgcolor:  'transparent',
    font:          {
      color:  '#111827',
      family: 'Inter, ui-sans-serif, system-ui',
      size:   14
    },
    autosize:      true
  };

  return (
    <>
      <div className="flex items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Single Prediction Explainer (SHAP)</h3>
        <Tooltip content={
          <p className="text-xs">
            Single‐prediction waterfall plot: shows each customer's stepwise impact on the model's base score—pulling the prediction up or down—to arrive at the final value, so you can trace exactly why this individual scored as it did.
          </p>
        }>
          <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-gray-500 bg-gray-100 rounded-full cursor-help">
            i
          </span>
        </Tooltip>
      </div>
      <div className="flex-grow flex flex-col" style={{ height: '420px' }}>
        {loading && !contributions.length ? (
          <div className="flex items-center justify-center flex-grow">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : (
          <Plot
            data={getWaterfallPlotData()}
            layout={ layout }
            config={{ responsive: true, displayModeBar: false }}
            style={{ width: '100%', height: '100%' }}
          />
        )}
      </div>
      <div className="mt-2">
        <select
          id="customer-id"
          className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-base py-1.5 px-3 bg-gray-50"
          value={customerId}
          onChange={(e) => setCustomerId(e.target.value)}
          disabled={loading || availableCustomers.length === 0}
        >
          {availableCustomers.map((customer) => (
            <option key={customer} value={customer}>
              {customer}
            </option>
          ))}
        </select>
      </div>
    </>
  );
}