'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { Visualization, getVisualizationData } from '@/app/lib/api/dashboard'
import { useTheme } from '@/app/contexts/ThemeContext'

interface PieChartProps {
  visualization: Visualization
  dashboardId: number
}

interface PieChartData {
  data: Array<{
    name: string
    value: number
    color?: string
  }>
  dataKey: string
  nameKey: string
  nameFormat?: string // Added name format property
  innerRadius?: number
  outerRadius?: number
}

/**
 * PieChart component
 * 
 * This component displays data as a pie chart.
 */
export default function PieChart({ visualization, dashboardId }: PieChartProps) {
  const [chartData, setChartData] = useState<PieChartData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { theme: currentTheme } = useTheme()
  
  // Get theme from visualization config or use system default
  const theme = visualization.style_config?.theme || 'auto'
  const effectiveTheme = theme === 'auto' ? currentTheme : theme
  const isDarkMode = effectiveTheme === 'dark'

  // Colors based on theme
  const colors = {
    text: isDarkMode ? '#e5e7eb' : '#1f2937',
    tooltip: isDarkMode ? '#1f2937' : '#ffffff'
  }
  
  // Default colors
  const defaultColors = visualization.style_config?.colors || [
    '#4f46e5', // indigo
    '#10b981', // emerald
    '#f59e0b', // amber
    '#ef4444', // red
    '#8b5cf6', // violet
    '#06b6d4', // cyan
    '#ec4899', // pink
    '#84cc16', // lime
    '#14b8a6', // teal
    '#f97316'  // orange
  ]

  // Format a value based on specified format
  const formatValue = (value: any, format?: string) => {
    if (value === undefined || value === null) return '';
    
    switch (format) {
      case 'number':
        return Number(value).toLocaleString();
      case 'currency':
        return new Intl.NumberFormat(undefined, { style: 'currency', currency: 'USD' }).format(Number(value));
      case 'date':
        try {
          const date = new Date(value);
          return date.toLocaleDateString();
        } catch (e) {
          return value;
        }
      default:
        return String(value);
    }
  };
  
  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        // Fetch data from the API using the visualization ID
        const response = await getVisualizationData(dashboardId, visualization.id);
        
        // Check if we have data
        if (response && response.data && response.data.length > 0) {
          // Extract configuration from style_config
          const config = visualization.style_config || {};
          const nameKey = config.nameKey || 'name';
          const nameFormat = config.nameFormat || 'text'; // Get the name format from config
          const dataKey = config.dataKey || 'value';
          const innerRadius = config.innerRadius || 0;
          const outerRadius = config.outerRadius || 80;
          
          // Process the data to match the expected format
          const processedData = response.data.map(item => ({
            name: String(item[nameKey]),
            value: parseFloat(item[dataKey]) || 0
          }));
          
          // Create chart data
          const chartData: PieChartData = {
            data: processedData,
            nameKey,
            nameFormat,
            dataKey,
            innerRadius,
            outerRadius
          };
          
          setChartData(chartData);
        } else {
          // If no data is returned, set empty data
          setChartData(null);
          setError('No data available for this visualization');
        }
      } catch (err) {
        console.error('Error fetching pie chart data:', err);
        setError('Failed to load data: ' + (err instanceof Error ? err.message : String(err)));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [dashboardId, visualization.id, visualization.style_config]);
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="flex items-center justify-center h-full text-red-500 dark:text-red-400">
        <span>{error}</span>
      </div>
    )
  }
  
  if (!chartData || !chartData.data || chartData.data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-secondary-500 dark:text-secondary-400">
        <span>No data available</span>
      </div>
    )
  }
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white dark:bg-gray-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow">
          <p className="font-medium">{formatValue(data.name, chartData.nameFormat)}</p>
          <p className="text-sm">
            <span className="font-medium">{data.value}</span>
            <span className="ml-1 text-gray-500 dark:text-gray-400">({((data.value / chartData.data.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%)</span>
          </p>
        </div>
      )
    }
    return null
  }

  // Custom label formatter
  const renderCustomizedLabel = ({ name, percent }: any) => {
    return `${formatValue(name, chartData.nameFormat)}: ${(percent * 100).toFixed(0)}%`;
  };
  
  return (
    <div className="h-full w-full">
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={chartData.data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={chartData.outerRadius || 80}
            innerRadius={chartData.innerRadius || 0}
            fill="#8884d8"
            dataKey="value"
            nameKey="name"
          >
            {chartData.data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.color || defaultColors[index % defaultColors.length]} 
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  )
}