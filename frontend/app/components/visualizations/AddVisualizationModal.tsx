'use client'

import { useState, useEffect } from 'react'
import { Dialog } from '@headlessui/react'
import { 
  XMarkIcon,
  ChartBarIcon,
  ChartPieIcon,
  TableCellsIcon,
  HashtagIcon,
  ArrowLeftIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

import { addVisualization, Dashboard, executeQuery } from '@/app/lib/api/dashboard'

interface AddVisualizationModalProps {
  dashboardId: number
  onClose: () => void
  onVisualizationAdded: (dashboard: Dashboard) => void
}

// Visualization type options
const visualizationTypes = [
  {
    id: 1,
    name: 'Number Card',
    description: 'Display a single metric with optional comparison.',
    icon: HashtagIcon,
    component_name: 'NumberCard'
  },
  {
    id: 2,
    name: 'Line Chart',
    description: 'Show trends over time or continuous data.',
    icon: ChartBarIcon,
    component_name: 'Line<PERSON><PERSON>'
  },
  {
    id: 3,
    name: 'Bar Chart',
    description: 'Compare values across categories.',
    icon: ChartBarIcon,
    component_name: '<PERSON><PERSON><PERSON>'
  },
  {
    id: 4,
    name: '<PERSON> <PERSON>',
    description: 'Show proportions of a whole.',
    icon: ChartPieIcon,
    component_name: '<PERSON><PERSON><PERSON>'
  },
  {
    id: 5,
    name: 'Table',
    description: 'Display tabular data with sorting and filtering.',
    icon: TableCellsIcon,
    component_name: 'TableView'
  }
]

// Refresh interval options (in seconds)
const refreshIntervals = [
  { value: 0, label: 'No auto-refresh' },
  { value: 300, label: '5 minutes' },
  { value: 900, label: '15 minutes' },
  { value: 1800, label: '30 minutes' },
  { value: 3600, label: '1 hour' },
  { value: 21600, label: '6 hours' },
  { value: 86400, label: '24 hours' }
]

/**
 * AddVisualizationModal component
 * 
 * This component displays a multi-step modal for adding a new visualization to a dashboard.
 */
export default function AddVisualizationModal({
  dashboardId,
  onClose,
  onVisualizationAdded
}: AddVisualizationModalProps) {
  // Step state
  const [currentStep, setCurrentStep] = useState(1)
  const [totalSteps, setTotalSteps] = useState(3)
  
  // Form state - Step 1
  const [title, setTitle] = useState('')
  const [selectedTypeId, setSelectedTypeId] = useState<number | null>(null)
  
  // Form state - Step 2
  const [sqlQuery, setSqlQuery] = useState('SELECT * FROM customers LIMIT 10')
  const [refreshInterval, setRefreshInterval] = useState(0)
  
  // Form state - Step 3 (Number Card specific)
  const [valueColumn, setValueColumn] = useState('')
  const [previousValueColumn, setPreviousValueColumn] = useState('')
  const [displayFormat, setDisplayFormat] = useState('number')
  const [abbreviateNumbers, setAbbreviateNumbers] = useState(false)
  const [decimalPlaces, setDecimalPlaces] = useState(1)
  const [label, setLabel] = useState('')
  const [changeLabel, setChangeLabel] = useState('vs. previous period')
  
  // Form state - Step 3 (Line Chart specific)
  const [xAxisColumn, setXAxisColumn] = useState('')
  const [yAxisColumns, setYAxisColumns] = useState<string[]>([])
  const [xAxisLabel, setXAxisLabel] = useState('')
  const [yAxisLabel, setYAxisLabel] = useState('')
  
  // Form state - Step 3 (Bar Chart specific)
  const [barXAxisColumn, setBarXAxisColumn] = useState('')
  const [barYAxisColumns, setBarYAxisColumns] = useState<string[]>([])
  const [barXAxisLabel, setBarXAxisLabel] = useState('')
  const [barYAxisLabel, setBarYAxisLabel] = useState('')
  const [isStacked, setIsStacked] = useState(false)
  
  // LineChart and BarChart
  const [xAxisLabelFormat, setXAxisLabelFormat] = useState('text')
  const [yAxisLabelFormat, setYAxisLabelFormat] = useState('text')

  // Form state - Step 3 (Pie Chart specific)
  const [nameColumn, setNameColumn] = useState('')
  const [nameColumnFormatPie, setNameColumnFormatPie] = useState('text')
  const [valueColumnPie, setValueColumnPie] = useState('')
  const [innerRadius, setInnerRadius] = useState(0)
  const [outerRadius, setOuterRadius] = useState(80)
  const [showDonut, setShowDonut] = useState(false)
  
  // Form state - Step 3 (Table specific)
  const [selectedColumns, setSelectedColumns] = useState<string[]>([])
  const [columnFormats, setColumnFormats] = useState<Record<string, string>>({})
  const [enablePagination, setEnablePagination] = useState(true)
  const [pageSize, setPageSize] = useState(10)
  
  // Mock columns from SQL query
  const [availableColumns, setAvailableColumns] = useState<string[]>([])
  
  // UI state
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Style config state
  const [styleConfig, setStyleConfig] = useState<any>({
    colors: ['#4f46e5', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
    showLegend: true,
    theme: 'auto',
    autoScaleYAxis: true,
    yAxisMin: null,
    yAxisMax: null
  })
  
  // Fetch columns from SQL query
  useEffect(() => {
    if (currentStep === 3 && sqlQuery) {
      const fetchColumns = async () => {
        try {
          // Execute the query to get a preview of the data
          const result = await executeQuery(sqlQuery);
          
          if (result && result.length > 0) {
            // Extract column names from the first row
            const columns = Object.keys(result[0]);
            setAvailableColumns(columns);
            
            // For Number Card
            if (selectedTypeId === 1) {
              // Set default value column if not already set
              if (!valueColumn && columns.length > 0) {
                // Try to find a numeric column that might be suitable for a number card
                const numericColumns = columns.filter(col => 
                  typeof result[0][col] === 'number' || 
                  !isNaN(parseFloat(result[0][col]))
                );
                
                if (numericColumns.length > 0) {
                  setValueColumn(numericColumns[0]);
                } else {
                  setValueColumn(columns[0]);
                }
              }
            }
            
            // For Line Chart
            if (selectedTypeId === 2) {
              // Set default x-axis column if not already set (prefer date/time columns)
              if (!xAxisColumn && columns.length > 0) {
                // Try to find a date/time column that might be suitable for x-axis
                const dateColumns = columns.filter(col => {
                  const value = result[0][col];
                  return typeof value === 'string' && (
                    /^\d{4}-\d{2}-\d{2}/.test(value) || // ISO date format
                    /^\d{2}\/\d{2}\/\d{4}/.test(value) || // MM/DD/YYYY
                    /^\w{3}\s\d{4}/.test(value) // Month Year
                  );
                });
                
                if (dateColumns.length > 0) {
                  setXAxisColumn(dateColumns[0]);
                } else {
                  setXAxisColumn(columns[0]);
                }
              }
              
              // Set default y-axis columns if not already set
              if (yAxisColumns.length === 0 && columns.length > 0) {
                // Try to find numeric columns that might be suitable for y-axis
                const numericColumns = columns.filter(col => 
                  col !== xAxisColumn && (
                    typeof result[0][col] === 'number' || 
                    !isNaN(parseFloat(result[0][col]))
                  )
                );
                
                if (numericColumns.length > 0) {
                  // Take up to 2 numeric columns
                  setYAxisColumns(numericColumns.slice(0, 2));
                }
              }
            }
            
            // For Bar Chart
            if (selectedTypeId === 3) {
              // Set default x-axis column if not already set
              if (!barXAxisColumn && columns.length > 0) {
                // Try to find a categorical column that might be suitable for x-axis
                const categoricalColumns = columns.filter(col => {
                  const value = result[0][col];
                  return typeof value === 'string';
                });
                
                if (categoricalColumns.length > 0) {
                  setBarXAxisColumn(categoricalColumns[0]);
                } else {
                  setBarXAxisColumn(columns[0]);
                }
              }
              
              // Set default y-axis columns if not already set
              if (barYAxisColumns.length === 0 && columns.length > 0) {
                // Try to find numeric columns that might be suitable for y-axis
                const numericColumns = columns.filter(col => 
                  col !== barXAxisColumn && (
                    typeof result[0][col] === 'number' || 
                    !isNaN(parseFloat(result[0][col]))
                  )
                );
                
                if (numericColumns.length > 0) {
                  // Take up to 2 numeric columns
                  setBarYAxisColumns(numericColumns.slice(0, 2));
                }
              }
            }
            
            // For Pie Chart
            if (selectedTypeId === 4) {
              // Set default name column if not already set
              if (!nameColumn && columns.length > 0) {
                // Try to find a categorical column that might be suitable for name
                const categoricalColumns = columns.filter(col => {
                  const value = result[0][col];
                  return typeof value === 'string';
                });
                
                if (categoricalColumns.length > 0) {
                  setNameColumn(categoricalColumns[0]);
                } else {
                  setNameColumn(columns[0]);
                }
              }
              
              // Set default value column if not already set
              if (!valueColumnPie && columns.length > 0) {
                // Try to find a numeric column that might be suitable for value
                const numericColumns = columns.filter(col => 
                  col !== nameColumn && (
                    typeof result[0][col] === 'number' || 
                    !isNaN(parseFloat(result[0][col]))
                  )
                );
                
                if (numericColumns.length > 0) {
                  setValueColumnPie(numericColumns[0]);
                } else {
                  // Choose the first column that's not the name column
                  const otherColumns = columns.filter(col => col !== nameColumn);
                  if (otherColumns.length > 0) {
                    setValueColumnPie(otherColumns[0]);
                  } else if (columns.length > 1) {
                    setValueColumnPie(columns[1]);
                  } else {
                    setValueColumnPie(columns[0]);
                  }
                }
              }
            }
            
            // For Table
            if (selectedTypeId === 5) {
              // Set all columns as selected by default
              if (selectedColumns.length === 0) {
                setSelectedColumns([...columns]);
                
                // Set default formats based on data types
                const formats: Record<string, string> = {};
                columns.forEach(col => {
                  const value = result[0][col];
                  if (typeof value === 'number' || !isNaN(parseFloat(value))) {
                    formats[col] = 'number';
                  } else if (typeof value === 'string' && (
                    /^\d{4}-\d{2}-\d{2}/.test(value) || // ISO date format
                    /^\d{2}\/\d{2}\/\d{4}/.test(value) || // MM/DD/YYYY
                    /^\w{3}\s\d{4}/.test(value) // Month Year
                  )) {
                    formats[col] = 'date';
                  } else {
                    formats[col] = 'text';
                  }
                });
                setColumnFormats(formats);
              }
            }
          } else {
            // If no data is returned, set empty columns
            setAvailableColumns([]);
          }
        } catch (err) {
          console.error('Error fetching columns:', err);
          // Don't set dummy columns, just show an error
          setAvailableColumns([]);
          setError('Failed to execute SQL query. Please check your query syntax and try again.');
        }
      };
      
      fetchColumns();
    }
  }, [currentStep, sqlQuery, valueColumn, xAxisColumn, yAxisColumns, barXAxisColumn, barYAxisColumns, nameColumn, valueColumnPie, selectedColumns, selectedTypeId]);
  
  // Handle next step
  const handleNextStep = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (currentStep === 1) {
      if (!title.trim() || !selectedTypeId) {
        setError('Please provide a title and select a visualization type.')
        return
      }
    } else if (currentStep === 2) {
      if (!sqlQuery.trim()) {
        setError('Please provide a SQL query.')
        return
      }
    }
    
    setError(null)
    setCurrentStep(prev => prev + 1)
  }
  
  // Handle previous step
  const handlePreviousStep = () => {
    setCurrentStep(prev => prev - 1)
    setError(null)
  }
  
  // Handle form submission (final step)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate based on current step
    if (currentStep === 2 && !sqlQuery.trim()) {
      setError('Please provide a SQL query.')
      return
    }
    
    if (currentStep === 3) {
      if (selectedTypeId === 1 && !valueColumn) {
        setError('Please select a value column.')
        return
      }
      
      if (selectedTypeId === 2) {
        if (!xAxisColumn) {
          setError('Please select an X-axis column.')
          return
        }
        if (yAxisColumns.length === 0) {
          setError('Please select at least one Y-axis column.')
          return
        }
      }
      
      if (selectedTypeId === 3) {
        if (!barXAxisColumn) {
          setError('Please select an X-axis column.')
          return
        }
        if (barYAxisColumns.length === 0) {
          setError('Please select at least one Y-axis column.')
          return
        }
      }
      
      if (selectedTypeId === 4) {
        if (!nameColumn) {
          setError('Please select a name column.')
          return
        }
        if (!valueColumnPie) {
          setError('Please select a value column.')
          return
        }
      }
      
      if (selectedTypeId === 5 && selectedColumns.length === 0) {
        setError('Please select at least one column to display.')
        return
      }
    }
    
    setIsCreating(true)
    setError(null)
    
    try {
      // Get the selected visualization type
      const selectedType = visualizationTypes.find(type => type.id === selectedTypeId)
      
      if (!selectedType) {
        throw new Error('Invalid visualization type selected.')
      }
      
      // Create configurations based on form inputs
      const queryConfig = {
        dataSource: 'default', // Always use default data warehouse
        query: sqlQuery.trim(),
        refreshInterval
      }
      
      const defaultLayoutConfig = {
        w: 4,
        h: 4,
        minW: 2,
        minH: 2
      }
      
      // Create style config based on visualization type
      let styleConfig: any = {
        colors: ['#4f46e5', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
        showLegend: true,
        theme: 'auto' // 'auto', 'light', or 'dark'
      }
      
      // Add visualization type specific configuration
      if (selectedTypeId === 1) {
        // Number Card specific configuration
        styleConfig = {
          ...styleConfig,
          valueColumn,
          previousValueColumn: previousValueColumn || undefined,
          displayFormat,
          abbreviateNumbers,
          decimalPlaces,
          label,
          changeLabel: changeLabel || undefined
        }
      } else if (selectedTypeId === 2) {
        // Line Chart specific configuration
        styleConfig = {
          ...styleConfig,
          xAxisColumn,
          yAxisColumns,
          xAxisLabel: xAxisLabel || undefined,
          xAxisLabelFormat,
          yAxisLabel: yAxisLabel || undefined,
          yAxisLabelFormat,
          // Include Y-axis scaling options
          autoScaleYAxis: styleConfig.autoScaleYAxis,
          yAxisMin: styleConfig.autoScaleYAxis === false ? styleConfig.yAxisMin : null,
          yAxisMax: styleConfig.autoScaleYAxis === false ? styleConfig.yAxisMax : null
        }
      } else if (selectedTypeId === 3) {
        // Bar Chart specific configuration
        styleConfig = {
          ...styleConfig,
          xAxisColumn: barXAxisColumn,
          yAxisColumns: barYAxisColumns,
          xAxisLabel: barXAxisLabel || undefined,
          xAxisLabelFormat,
          yAxisLabel: barYAxisLabel || undefined,
          yAxisLabelFormat,
          isStacked,
          // Include Y-axis scaling options
          autoScaleYAxis: styleConfig.autoScaleYAxis,
          yAxisMin: styleConfig.autoScaleYAxis === false ? styleConfig.yAxisMin : null,
          yAxisMax: styleConfig.autoScaleYAxis === false ? styleConfig.yAxisMax : null
        }
      } else if (selectedTypeId === 4) {
        // Pie Chart specific configuration
        styleConfig = {
          ...styleConfig,
          nameKey: nameColumn,
          dataKey: valueColumnPie,
          innerRadius: showDonut ? innerRadius : 0,
          outerRadius,
          nameFormat: nameColumnFormatPie
        }
      } else if (selectedTypeId === 5) {
        // Table specific configuration
        styleConfig = {
          ...styleConfig,
          columns: selectedColumns.map(col => ({
            key: col,
            name: col,
            sortable: true,
            format: columnFormats[col] || 'text'
          })),
          pagination: enablePagination ? {
            pageSize,
            enabled: true
          } : undefined
        }
      }
      
      // Add the visualization
      const updatedDashboard = await addVisualization(dashboardId, {
        title: title.trim(),
        visualization_type_id: selectedTypeId as number, // Type assertion since we've already validated it's not null
        query_config: queryConfig,
        layout_config: defaultLayoutConfig,
        style_config: styleConfig
      })
      
      // Call the callback with the updated dashboard
      onVisualizationAdded(updatedDashboard)
    } catch (err) {
      console.error('Error adding visualization:', err)
      setError('Failed to add visualization. Please try again later.')
      setIsCreating(false)
    }
  }
  
  // Get the selected visualization type
  const selectedType = selectedTypeId 
    ? visualizationTypes.find(type => type.id === selectedTypeId) 
    : null
  
  return (
    <Dialog
      open={true}
      onClose={() => !isCreating && onClose()}
      className="relative z-50"
    >
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/30 dark:bg-black/50" aria-hidden="true" />
      
      {/* Full-screen container for centering */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-2xl rounded-lg bg-white dark:bg-card shadow-xl">
          {/* Modal header */}
          <div className="px-6 py-4 border-b border-secondary-200 dark:border-secondary-700 flex items-center justify-between">
            <div>
              <Dialog.Title className="text-lg font-medium text-secondary-900 dark:text-secondary-100">
                Add Visualization
              </Dialog.Title>
              <p className="text-sm text-secondary-500 dark:text-secondary-400 mt-1">
                Step {currentStep} of {totalSteps}: 
                {currentStep === 1 ? ' Basic Information' : 
                 currentStep === 2 ? ' Data Configuration' : 
                 ' Visualization Options'}
              </p>
            </div>
            <button
              onClick={onClose}
              disabled={isCreating}
              className="text-secondary-400 hover:text-secondary-500 dark:text-secondary-500 dark:hover:text-secondary-400"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Step indicator */}
          <div className="px-6 pt-4">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                currentStep === 1 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
              }`}>
                1
              </div>
              <div className={`flex-1 h-0.5 mx-2 ${
                currentStep >= 2 
                  ? 'bg-primary-600 dark:bg-primary-500' 
                  : 'bg-secondary-200 dark:bg-secondary-700'
              }`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                currentStep === 2 
                  ? 'bg-primary-600 text-white' 
                  : currentStep > 2
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                    : 'bg-secondary-100 text-secondary-700 dark:bg-secondary-900 dark:text-secondary-300'
              }`}>
                2
              </div>
              
              {totalSteps === 3 && (
                <>
                  <div className={`flex-1 h-0.5 mx-2 ${
                    currentStep === 3 
                      ? 'bg-primary-600 dark:bg-primary-500' 
                      : 'bg-secondary-200 dark:bg-secondary-700'
                  }`}></div>
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    currentStep === 3 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-secondary-100 text-secondary-700 dark:bg-secondary-900 dark:text-secondary-300'
                  }`}>
                    3
                  </div>
                </>
              )}
            </div>
          </div>
          
          {/* Modal content */}
          {currentStep === 1 && (
            <form onSubmit={handleNextStep}>
              <div className="px-6 py-4 space-y-6">
                {/* Error message */}
                {error && (
                  <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400 dark:text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800 dark:text-red-200">{error}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Title input */}
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                    Title <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                    placeholder="My Visualization"
                    disabled={isCreating}
                  />
                </div>
                
                {/* Visualization type selection */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                    Visualization Type <span className="text-red-500">*</span>
                  </label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {visualizationTypes.map((type) => (
                      <div
                        key={type.id}
                        className={`
                          flex items-start p-4 rounded-lg border cursor-pointer
                          ${selectedTypeId === type.id
                            ? 'border-primary-500 dark:border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                            : 'border-secondary-300 dark:border-secondary-700 hover:bg-secondary-50 dark:hover:bg-secondary-800'
                          }
                        `}
                        onClick={() => !isCreating && setSelectedTypeId(type.id)}
                      >
                        <div className="flex-shrink-0">
                          <type.icon className={`
                            h-6 w-6
                            ${selectedTypeId === type.id
                              ? 'text-primary-500 dark:text-primary-400'
                              : 'text-secondary-400 dark:text-secondary-500'
                            }
                          `} />
                        </div>
                        <div className="ml-4">
                          <h3 className={`
                            text-sm font-medium
                            ${selectedTypeId === type.id
                              ? 'text-primary-700 dark:text-primary-300'
                              : 'text-secondary-900 dark:text-secondary-100'
                            }
                          `}>
                            {type.name}
                          </h3>
                          <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                            {type.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Modal footer */}
              <div className="flex items-center justify-end px-6 py-4 border-t border-secondary-200 dark:border-secondary-700 space-x-3">
                <button
                  type="button"
                  className="px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-secondary-900 dark:hover:text-secondary-100"
                  onClick={onClose}
                  disabled={isCreating}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-800 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-500 disabled:opacity-50"
                  disabled={isCreating || !title.trim() || !selectedTypeId}
                >
                  Next
                  <ArrowRightIcon className="ml-2 h-4 w-4" />
                </button>
              </div>
            </form>
          )}
          
          {currentStep === 2 && (
            <form onSubmit={handleNextStep}>
              <div className="px-6 py-4 space-y-6">
                {/* Error message */}
                {error && (
                  <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400 dark:text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800 dark:text-red-200">{error}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Selected visualization info */}
                <div className="bg-secondary-50 dark:bg-secondary-800/50 p-4 rounded-md">
                  <div className="flex items-center">
                    {selectedType && (
                      <selectedType.icon className="h-6 w-6 text-primary-500 dark:text-primary-400 mr-2" />
                    )}
                    <div>
                      <h3 className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        {title} ({selectedType?.name})
                      </h3>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                        Configure the data source and query for this visualization
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* SQL query input */}
                <div>
                  <label htmlFor="sqlQuery" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                    SQL Query <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="sqlQuery"
                    value={sqlQuery}
                    onChange={(e) => setSqlQuery(e.target.value)}
                    rows={6}
                    className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100 font-mono"
                    placeholder="SELECT * FROM table_name"
                    disabled={isCreating}
                  />
                  <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                    Write a SQL query to fetch the data for your visualization.
                  </p>
                </div>
                
                {/* Refresh interval */}
                <div>
                  <label htmlFor="refreshInterval" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                    Refresh Interval
                  </label>
                  <select
                    id="refreshInterval"
                    value={refreshInterval}
                    onChange={(e) => setRefreshInterval(Number(e.target.value))}
                    className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                    disabled={isCreating}
                  >
                    {refreshIntervals.map((interval) => (
                      <option key={interval.value} value={interval.value}>
                        {interval.label}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                    How often should this visualization refresh its data?
                  </p>
                </div>
              </div>
              
              {/* Modal footer */}
              <div className="flex items-center justify-between px-6 py-4 border-t border-secondary-200 dark:border-secondary-700">
                <button
                  type="button"
                  onClick={handlePreviousStep}
                  disabled={isCreating}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-secondary-900 dark:hover:text-secondary-100"
                >
                  <ArrowLeftIcon className="mr-2 h-4 w-4" />
                  Back
                </button>
                
                <div className="flex space-x-3">
                  <button
                    type="button"
                    className="px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-secondary-900 dark:hover:text-secondary-100"
                    onClick={onClose}
                    disabled={isCreating}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-800 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-500 disabled:opacity-50"
                    disabled={isCreating || !sqlQuery.trim()}
                  >
                    <div className="inline-flex items-center">
                      Next
                      <ArrowRightIcon className="ml-2 h-4 w-4" />
                    </div>
                  </button>
                </div>
              </div>
            </form>
          )}
          
          {currentStep === 3 && (
            <form onSubmit={handleSubmit}>
              <div className="px-6 py-4 space-y-6 max-h-[60vh] overflow-y-auto">
                {/* Error message */}
                {error && (
                  <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400 dark:text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800 dark:text-red-200">{error}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Number Card Configuration */}
                {selectedTypeId === 1 && (
                  <>
                    <div className="bg-secondary-50 dark:bg-secondary-800/50 p-4 rounded-md">
                      <h3 className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        Number Card Configuration
                      </h3>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                        Configure how your number card will display data
                      </p>
                    </div>
                    
                    {/* Value Column Selection */}
                    <div>
                      <label htmlFor="valueColumn" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Value Column <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="valueColumn"
                        value={valueColumn}
                        onChange={(e) => setValueColumn(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="">Select a column</option>
                        {availableColumns.map((column) => (
                          <option key={column} value={column}>
                            {column}
                          </option>
                        ))}
                      </select>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        Select the column to display as the main value
                      </p>
                    </div>
                    
                    {/* Previous Value Column Selection (Optional) */}
                    <div>
                      <label htmlFor="previousValueColumn" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Previous Value Column (Optional)
                      </label>
                      <select
                        id="previousValueColumn"
                        value={previousValueColumn}
                        onChange={(e) => setPreviousValueColumn(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="">None (No comparison)</option>
                        {availableColumns.map((column) => (
                          <option key={column} value={column}>
                            {column}
                          </option>
                        ))}
                      </select>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        Select a column to compare against (for showing change)
                      </p>
                    </div>
                    
                    {/* Display Format */}
                    <div>
                      <label htmlFor="displayFormat" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Display Format
                      </label>
                      <select
                        id="displayFormat"
                        value={displayFormat}
                        onChange={(e) => setDisplayFormat(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="number">Number</option>
                        <option value="currency">Currency ($)</option>
                        <option value="percentage">Percentage (%)</option>
                      </select>
                    </div>
                    
                    {/* Number Formatting Options */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {/* Abbreviate Numbers */}
                      <div className="flex items-center">
                        <input
                          id="abbreviateNumbers"
                          type="checkbox"
                          checked={abbreviateNumbers}
                          onChange={(e) => setAbbreviateNumbers(e.target.checked)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                          disabled={isCreating}
                        />
                        <label htmlFor="abbreviateNumbers" className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                          Abbreviate large numbers (K, M, B)
                        </label>
                      </div>
                      
                      {/* Decimal Places */}
                      <div>
                        <label htmlFor="decimalPlaces" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                          Decimal Places
                        </label>
                        <select
                          id="decimalPlaces"
                          value={decimalPlaces}
                          onChange={(e) => setDecimalPlaces(Number(e.target.value))}
                          className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                          disabled={isCreating}
                        >
                          <option value={0}>0</option>
                          <option value={1}>1</option>
                          <option value={2}>2</option>
                          <option value={3}>3</option>
                        </select>
                      </div>
                    </div>
                    
                    {/* Label */}
                    <div>
                      <label htmlFor="label" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Label (Optional)
                      </label>
                      <input
                        type="text"
                        id="label"
                        value={label}
                        onChange={(e) => setLabel(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        placeholder="e.g., Active Users"
                        disabled={isCreating}
                      />
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        A descriptive label for the metric
                      </p>
                    </div>
                    
                    {/* Change Label */}
                    <div>
                      <label htmlFor="changeLabel" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Change Label (Optional)
                      </label>
                      <input
                        type="text"
                        id="changeLabel"
                        value={changeLabel}
                        onChange={(e) => setChangeLabel(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        placeholder="e.g., vs. previous period"
                        disabled={isCreating}
                      />
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        Label for the comparison value
                      </p>
                    </div>
                  </>
                )}
                
                {/* Line Chart Configuration */}
                {selectedTypeId === 2 && (
                  <>
                    <div className="bg-secondary-50 dark:bg-secondary-800/50 p-4 rounded-md">
                      <h3 className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        Line Chart Configuration
                      </h3>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                        Configure how your line chart will display data
                      </p>
                    </div>
                    
                    {/* X-Axis Column Selection */}
                    <div>
                      <label htmlFor="xAxisColumn" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        X-Axis Column <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="xAxisColumn"
                        value={xAxisColumn}
                        onChange={(e) => setXAxisColumn(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="">Select a column</option>
                        {availableColumns.map((column) => (
                          <option key={column} value={column}>
                            {column}
                          </option>
                        ))}
                      </select>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        Select the column to use for the X-axis (typically a date or category)
                      </p>
                    </div>
                    
                    {/* Y-Axis Column Selection */}
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Y-Axis Columns <span className="text-red-500">*</span>
                      </label>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400 mb-2">
                        Select up to 2 columns to display as lines (numeric values)
                      </p>
                      
                      {availableColumns
                        .filter(column => column !== xAxisColumn)
                        .map((column) => (
                          <div key={column} className="flex items-center mb-2">
                            <input
                              id={`y-axis-${column}`}
                              type="checkbox"
                              checked={yAxisColumns.includes(column)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  // Add column if not already selected and limit to 2
                                  if (!yAxisColumns.includes(column) && yAxisColumns.length < 2) {
                                    setYAxisColumns([...yAxisColumns, column]);
                                  }
                                } else {
                                  // Remove column if checked
                                  setYAxisColumns(yAxisColumns.filter(col => col !== column));
                                }
                              }}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                              disabled={isCreating || (!yAxisColumns.includes(column) && yAxisColumns.length >= 2)}
                            />
                            <label htmlFor={`y-axis-${column}`} className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                              {column}
                            </label>
                          </div>
                        ))}
                      
                      {yAxisColumns.length === 0 && (
                        <div className="text-sm text-secondary-500 dark:text-secondary-400 italic">
                          No columns selected for Y-axis
                        </div>
                      )}
                    </div>
                    
                    {/* X-Axis Label */}
                    <div>
                      <label htmlFor="xAxisLabel" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        X-Axis Label (Optional)
                      </label>
                      <input
                        type="text"
                        id="xAxisLabel"
                        value={xAxisLabel}
                        onChange={(e) => setXAxisLabel(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        placeholder="e.g., Date"
                        disabled={isCreating}
                      />
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        A label for the X-axis (defaults to column name if empty)
                      </p>
                      <select
                        id="xAxisLabelFormat"
                        value={xAxisLabelFormat}
                        onChange={(e) => setXAxisLabelFormat(e.target.value)}
                        className="block w-1/3 rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="text">Text</option>
                        <option value="number">Number</option>
                        <option value="currency">Currency</option>
                        <option value="date">Date</option>
                        <option value="percent">Percent</option>
                      </select>
                    </div>
                    
                    {/* Y-Axis Label */}
                    <div>
                      <label htmlFor="yAxisLabel" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Y-Axis Label (Optional)
                      </label>
                      <input
                        type="text"
                        id="yAxisLabel"
                        value={yAxisLabel}
                        onChange={(e) => setYAxisLabel(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        placeholder="e.g., Value"
                        disabled={isCreating}
                      />
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        A label for the Y-axis
                      </p>
                      <select
                        id="yAxisLabelFormat"
                        value={yAxisLabelFormat}
                        onChange={(e) => setYAxisLabelFormat(e.target.value)}
                        className="block w-1/3 rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="text">Text</option>
                        <option value="number">Number</option>
                        <option value="currency">Currency</option>
                        <option value="date">Date</option>
                        <option value="percent">Percent</option>
                      </select>
                    </div>
                    
                    {/* Y-Axis Scaling Options */}
                    <div className="border-t border-secondary-200 dark:border-secondary-700 pt-4 mt-4">
                      <h4 className="text-sm font-medium text-secondary-900 dark:text-secondary-100 mb-3">
                        Y-Axis Scaling Options
                      </h4>
                      
                      {/* Auto-scale Y-Axis */}
                      <div className="flex items-center mb-4">
                        <input
                          id="autoScaleYAxis"
                          type="checkbox"
                          checked={styleConfig.autoScaleYAxis !== false}
                          onChange={(e) => {
                            setStyleConfig({
                              ...styleConfig,
                              autoScaleYAxis: e.target.checked
                            });
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                          disabled={isCreating}
                        />
                        <label htmlFor="autoScaleYAxis" className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                          Auto-scale Y-axis (recommended for small values like churn rates)
                        </label>
                      </div>
                      
                      {/* Manual Y-Axis Range */}
                      {styleConfig.autoScaleYAxis === false && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          {/* Y-Axis Min */}
                          <div>
                            <label htmlFor="yAxisMin" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                              Y-Axis Minimum
                            </label>
                            <input
                              type="number"
                              id="yAxisMin"
                              value={styleConfig.yAxisMin || 0}
                              onChange={(e) => {
                                setStyleConfig({
                                  ...styleConfig,
                                  yAxisMin: e.target.value === '' ? null : Number(e.target.value)
                                });
                              }}
                              className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                              placeholder="0"
                              disabled={isCreating}
                            />
                          </div>
                          
                          {/* Y-Axis Max */}
                          <div>
                            <label htmlFor="yAxisMax" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                              Y-Axis Maximum
                            </label>
                            <input
                              type="number"
                              id="yAxisMax"
                              value={styleConfig.yAxisMax || 1}
                              onChange={(e) => {
                                setStyleConfig({
                                  ...styleConfig,
                                  yAxisMax: e.target.value === '' ? null : Number(e.target.value)
                                });
                              }}
                              className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                              placeholder="1"
                              disabled={isCreating}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}
                
                {/* Bar Chart Configuration */}
                {selectedTypeId === 3 && (
                  <>
                    <div className="bg-secondary-50 dark:bg-secondary-800/50 p-4 rounded-md">
                      <h3 className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        Bar Chart Configuration
                      </h3>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                        Configure how your bar chart will display data
                      </p>
                    </div>
                    
                    {/* X-Axis Column Selection */}
                    <div>
                      <label htmlFor="barXAxisColumn" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        X-Axis Column <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="barXAxisColumn"
                        value={barXAxisColumn}
                        onChange={(e) => setBarXAxisColumn(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="">Select a column</option>
                        {availableColumns.map((column) => (
                          <option key={column} value={column}>
                            {column}
                          </option>
                        ))}
                      </select>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        Select the column to use for the X-axis (typically a category)
                      </p>
                    </div>
                    
                    {/* Y-Axis Column Selection */}
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Y-Axis Columns <span className="text-red-500">*</span>
                      </label>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400 mb-2">
                        Select up to 2 columns to display as bars (numeric values)
                      </p>
                      
                      {availableColumns
                        .filter(column => column !== barXAxisColumn)
                        .map((column) => (
                          <div key={column} className="flex items-center mb-2">
                            <input
                              id={`bar-y-axis-${column}`}
                              type="checkbox"
                              checked={barYAxisColumns.includes(column)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  // Add column if not already selected and limit to 2
                                  if (!barYAxisColumns.includes(column) && barYAxisColumns.length < 2) {
                                    setBarYAxisColumns([...barYAxisColumns, column]);
                                  }
                                } else {
                                  // Remove column if checked
                                  setBarYAxisColumns(barYAxisColumns.filter(col => col !== column));
                                }
                              }}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                              disabled={isCreating || (!barYAxisColumns.includes(column) && barYAxisColumns.length >= 2)}
                            />
                            <label htmlFor={`bar-y-axis-${column}`} className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                              {column}
                            </label>
                          </div>
                        ))}
                      
                      {barYAxisColumns.length === 0 && (
                        <div className="text-sm text-secondary-500 dark:text-secondary-400 italic">
                          No columns selected for Y-axis
                        </div>
                      )}
                    </div>
                    
                    {/* X-Axis Label */}
                    <div>
                      <label htmlFor="barXAxisLabel" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        X-Axis Label (Optional)
                      </label>
                      <input
                        type="text"
                        id="barXAxisLabel"
                        value={barXAxisLabel}
                        onChange={(e) => setBarXAxisLabel(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        placeholder="e.g., Category"
                        disabled={isCreating}
                      />
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        A label for the X-axis (defaults to column name if empty)
                      </p>
                      {/* label Format Selection */}
                      <select
                        id="xAxisLabelFormat"
                        value={xAxisLabelFormat}
                        onChange={(e) => setXAxisLabelFormat(e.target.value)}
                        className="block w-1/3 rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="text">Text</option>
                        <option value="number">Number</option>
                        <option value="currency">Currency</option>
                        <option value="date">Date</option>
                        <option value="percent">Percent</option>
                      </select>
                    </div>
                    
                    {/* Y-Axis Label */}
                    <div>
                      <label htmlFor="barYAxisLabel" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Y-Axis Label (Optional)
                      </label>
                      <input
                        type="text"
                        id="barYAxisLabel"
                        value={barYAxisLabel}
                        onChange={(e) => setBarYAxisLabel(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        placeholder="e.g., Value"
                        disabled={isCreating}
                      />
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        A label for the Y-axis
                      </p>
                      {/* label Format Selection */}
                      <select
                        id="yAxisLabelFormat"
                        value={yAxisLabelFormat}
                        onChange={(e) => setYAxisLabelFormat(e.target.value)}
                        className="block w-1/3 rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="text">Text</option>
                        <option value="number">Number</option>
                        <option value="currency">Currency</option>
                        <option value="date">Date</option>
                        <option value="percent">Percent</option>
                      </select>
                    </div>

                    
                    {/* Stacked Bar Option */}
                    <div className="flex items-center mt-4">
                      <input
                        id="isStacked"
                        type="checkbox"
                        checked={isStacked}
                        onChange={(e) => setIsStacked(e.target.checked)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                        disabled={isCreating || barYAxisColumns.length < 2}
                      />
                      <label htmlFor="isStacked" className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                        Stack bars (when multiple Y-axis columns are selected)
                      </label>
                    </div>
                    
                    {/* Y-Axis Scaling Options */}
                    <div className="border-t border-secondary-200 dark:border-secondary-700 pt-4 mt-4">
                      <h4 className="text-sm font-medium text-secondary-900 dark:text-secondary-100 mb-3">
                        Y-Axis Scaling Options
                      </h4>
                      
                      {/* Auto-scale Y-Axis */}
                      <div className="flex items-center mb-4">
                        <input
                          id="barAutoScaleYAxis"
                          type="checkbox"
                          checked={styleConfig.autoScaleYAxis !== false}
                          onChange={(e) => {
                            setStyleConfig({
                              ...styleConfig,
                              autoScaleYAxis: e.target.checked
                            });
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                          disabled={isCreating}
                        />
                        <label htmlFor="barAutoScaleYAxis" className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                          Auto-scale Y-axis (recommended for most cases)
                        </label>
                      </div>
                      
                      {/* Manual Y-Axis Range */}
                      {styleConfig.autoScaleYAxis === false && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          {/* Y-Axis Min */}
                          <div>
                            <label htmlFor="barYAxisMin" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                              Y-Axis Minimum
                            </label>
                            <input
                              type="number"
                              id="barYAxisMin"
                              value={styleConfig.yAxisMin || 0}
                              onChange={(e) => {
                                setStyleConfig({
                                  ...styleConfig,
                                  yAxisMin: e.target.value === '' ? null : Number(e.target.value)
                                });
                              }}
                              className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                              placeholder="0"
                              disabled={isCreating}
                            />
                          </div>
                          
                          {/* Y-Axis Max */}
                          <div>
                            <label htmlFor="barYAxisMax" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                              Y-Axis Maximum
                            </label>
                            <input
                              type="number"
                              id="barYAxisMax"
                              value={styleConfig.yAxisMax || 1}
                              onChange={(e) => {
                                setStyleConfig({
                                  ...styleConfig,
                                  yAxisMax: e.target.value === '' ? null : Number(e.target.value)
                                });
                              }}
                              className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                              placeholder="1"
                              disabled={isCreating}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}
                
                {/* Pie Chart Configuration */}
                {selectedTypeId === 4 && (
                  <>
                    <div className="bg-secondary-50 dark:bg-secondary-800/50 p-4 rounded-md">
                      <h3 className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        Pie Chart Configuration
                      </h3>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                        Configure how your pie chart will display data
                      </p>
                    </div>
                    
                    {/* Name Column Selection */}
                    <div>
                      <label htmlFor="nameColumn" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Name Column <span className="text-red-500">*</span>
                      </label>
                      <div className="flex gap-2 mt-1">
                        <select
                          id="nameColumn"
                          value={nameColumn}
                          onChange={(e) => setNameColumn(e.target.value)}
                          className="block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                          disabled={isCreating}
                        >
                          <option value="">Select a column</option>
                          {availableColumns.map((column) => (
                            <option key={column} value={column}>
                              {column}
                            </option>
                          ))}
                        </select>
                        
                        {/* Name Format Selection */}
                        <select
                          id="nameColumnFormatPie"
                          value={nameColumnFormatPie}
                          onChange={(e) => setNameColumnFormatPie(e.target.value)}
                          className="block w-1/3 rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                          disabled={isCreating || !nameColumn}
                        >
                          <option value="text">Text</option>
                          <option value="number">Number</option>
                          <option value="currency">Currency</option>
                          <option value="date">Date</option>
                        </select>
                      </div>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        Select the column to use for segment names (categories) and its display format
                      </p>
                    </div>
                    
                    {/* Value Column Selection */}
                    <div>
                      <label htmlFor="valueColumnPie" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                        Value Column <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="valueColumnPie"
                        value={valueColumnPie}
                        onChange={(e) => setValueColumnPie(e.target.value)}
                        className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                        disabled={isCreating}
                      >
                        <option value="">Select a column</option>
                        {availableColumns
                          .filter(column => column !== nameColumn)
                          .map((column) => (
                            <option key={column} value={column}>
                              {column}
                            </option>
                          ))}
                      </select>
                      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
                        Select the column to use for segment values (size)
                      </p>
                    </div>
                    
                    {/* Rest of the Pie Chart configuration... */}
                    {/* Donut Chart Option */}
                    <div className="flex items-center mt-4 mb-2">
                      <input
                        id="showDonut"
                        type="checkbox"
                        checked={showDonut}
                        onChange={(e) => setShowDonut(e.target.checked)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                        disabled={isCreating}
                      />
                      <label htmlFor="showDonut" className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                        Show as donut chart (with hole in center)
                      </label>
                    </div>
                    
                    {/* Inner Radius and Outer Radius controls */}
                    {showDonut && (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2">
                        {/* Inner Radius */}
                        <div>
                          <label htmlFor="innerRadius" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                            Inner Radius
                          </label>
                          <input
                            type="range"
                            id="innerRadius"
                            min={0}
                            max={70}
                            value={innerRadius}
                            onChange={(e) => setInnerRadius(Number(e.target.value))}
                            className="mt-1 block w-full"
                            disabled={isCreating}
                          />
                          <div className="text-xs text-secondary-500 dark:text-secondary-400 mt-1 text-center">
                            {innerRadius}
                          </div>
                        </div>
                        
                        {/* Outer Radius */}
                        <div>
                          <label htmlFor="outerRadius" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                            Outer Radius
                          </label>
                          <input
                            type="range"
                            id="outerRadius"
                            min={50}
                            max={150}
                            value={outerRadius}
                            onChange={(e) => setOuterRadius(Number(e.target.value))}
                            className="mt-1 block w-full"
                            disabled={isCreating}
                          />
                          <div className="text-xs text-secondary-500 dark:text-secondary-400 mt-1 text-center">
                            {outerRadius}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
                
                {/* Table Configuration */}
                {selectedTypeId === 5 && (
                  <>
                    <div className="bg-secondary-50 dark:bg-secondary-800/50 p-4 rounded-md">
                      <h3 className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        Table Configuration
                      </h3>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                        Configure how your table will display data
                      </p>
                    </div>
                    
                    {/* Column Selection */}
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                        Columns to Display <span className="text-red-500">*</span>
                      </label>
                      
                      <div className="bg-white dark:bg-input border border-secondary-300 dark:border-secondary-700 rounded-md shadow-sm p-4 max-h-60 overflow-y-auto">
                        {availableColumns.length > 0 ? (
                          availableColumns.map((column) => (
                            <div key={column} className="flex items-center mb-3">
                              <input
                                id={`column-${column}`}
                                type="checkbox"
                                checked={selectedColumns.includes(column)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    // Add column
                                    setSelectedColumns([...selectedColumns, column]);
                                  } else {
                                    // Remove column
                                    setSelectedColumns(selectedColumns.filter(col => col !== column));
                                  }
                                }}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                                disabled={isCreating}
                              />
                              <label htmlFor={`column-${column}`} className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                                {column}
                              </label>
                              
                              {/* Column Format Selection */}
                              {selectedColumns.includes(column) && (
                                <select
                                  value={columnFormats[column] || 'text'}
                                  onChange={(e) => {
                                    setColumnFormats({
                                      ...columnFormats,
                                      [column]: e.target.value
                                    });
                                  }}
                                  className="ml-auto text-xs rounded-md border-secondary-300 dark:border-secondary-700 bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                                  disabled={isCreating}
                                >
                                  <option value="text">Text</option>
                                  <option value="number">Number</option>
                                  <option value="currency">Currency</option>
                                  <option value="percent">Percentage</option>
                                  <option value="date">Date</option>
                                </select>
                              )}
                            </div>
                          ))
                        ) : (
                          <div className="text-sm text-secondary-500 dark:text-secondary-400 italic">
                            No columns available
                          </div>
                        )}
                        
                        {availableColumns.length > 0 && selectedColumns.length === 0 && (
                          <div className="text-sm text-secondary-500 dark:text-secondary-400 italic">
                            No columns selected
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Pagination Options */}
                    <div className="mt-6">
                      <div className="flex items-center mb-4">
                        <input
                          id="enablePagination"
                          type="checkbox"
                          checked={enablePagination}
                          onChange={(e) => setEnablePagination(e.target.checked)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 dark:border-secondary-700 rounded"
                          disabled={isCreating}
                        />
                        <label htmlFor="enablePagination" className="ml-2 block text-sm text-secondary-700 dark:text-secondary-300">
                          Enable pagination
                        </label>
                      </div>
                      
                      {enablePagination && (
                        <div>
                          <label htmlFor="pageSize" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300">
                            Rows per page
                          </label>
                          <select
                            id="pageSize"
                            value={pageSize}
                            onChange={(e) => setPageSize(Number(e.target.value))}
                            className="mt-1 block w-full rounded-md border-secondary-300 dark:border-secondary-700 shadow-sm focus:border-primary-500 dark:focus:border-primary-500 focus:ring-primary-500 dark:focus:ring-primary-500 sm:text-sm bg-white dark:bg-input text-secondary-900 dark:text-secondary-100"
                            disabled={isCreating}
                          >
                            <option value={5}>5</option>
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                          </select>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
              
              {/* Modal footer */}
              <div className="flex items-center justify-between px-6 py-4 border-t border-secondary-200 dark:border-secondary-700">
                <button
                  type="button"
                  onClick={handlePreviousStep}
                  disabled={isCreating}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-secondary-900 dark:hover:text-secondary-100"
                >
                  <ArrowLeftIcon className="mr-2 h-4 w-4" />
                  Back
                </button>
                
                <div className="flex space-x-3">
                  <button
                    type="button"
                    className="px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-secondary-900 dark:hover:text-secondary-100"
                    onClick={onClose}
                    disabled={isCreating}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-800 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-500 disabled:opacity-50"
                    disabled={isCreating || 
                      (selectedTypeId === 1 && !valueColumn) || 
                      (selectedTypeId === 2 && (!xAxisColumn || yAxisColumns.length === 0)) ||
                      (selectedTypeId === 3 && (!barXAxisColumn || barYAxisColumns.length === 0)) ||
                      (selectedTypeId === 4 && (!nameColumn || !valueColumnPie)) ||
                      (selectedTypeId === 5 && selectedColumns.length === 0)
                    }
                  >
                    {isCreating ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </div>
                    ) : (
                      'Add Visualization'
                    )}
                  </button>
                </div>
              </div>
            </form>
          )}
        </Dialog.Panel>
      </div>
    </Dialog>
  )
}