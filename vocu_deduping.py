import pandas as pd
import re
import os
from tqdm import tqdm
from fuzzywuzzy import fuzz

# === STEP 1: Load Excel ===
input_path = "/mnt/c/Users/<USER>/Downloads/business_names.xlsx"  # Update this if needed
df = pd.read_excel(input_path)

# === STEP 2: Detect business name column ===
columns = df.columns.tolist()
business_col = [col for col in columns if "business" in col.lower() or "name" in col.lower()][0]

# === STEP 3: Canonicalize names ===
def canonicalize(name):
    name = str(name).upper()
    name = re.sub(r"\b(PTY\s+LTD|LIMITED|LTD|CORPORATION|INC|GROUP|AUSTRALIA|SERVICES|HOLDINGS|PROPRIETARY|TRADING AS|T/A)\b", "", name)
    name = re.sub(r"[^A-Z0-9]", " ", name)
    name = re.sub(r"\s+", " ", name).strip()
    return name

df["Business_Name"] = df[business_col]
tqdm.pandas(desc="🔄 Canonicalizing")
df["Canonical_Name"] = df["Business_Name"].progress_apply(canonicalize)

# === STEP 4: Build smart duplicate clusters ===
canonical_names = df["Canonical_Name"].unique().tolist()
group_map = {}
group_id = 0

for i, name in enumerate(canonical_names):
    if name in group_map:
        continue
    group_map[name] = name  # anchor
    for other in canonical_names[i+1:]:
        if other in group_map:
            continue
        score = fuzz.ratio(name, other)
        if score >= 92 and (name in other or other in name):
            group_map[other] = name  # assign to cluster

# === STEP 5: Map deduplicated group names ===
df["Deduped_As"] = df["Canonical_Name"].map(lambda x: group_map.get(x, ""))

# === STEP 6: Save to Downloads folder ===
downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
output_file = os.path.join(downloads_path, "/mnt/c/Users/<USER>/Downloads/deduplicated_businesses_smart.csv")
df[["Business_Name", "Canonical_Name", "Deduped_As"]].to_csv(output_file, index=False)

print(f"✅ Smart deduplication complete.\n📄 Saved to: {output_file}")
